<svg width="1423" height="560" viewBox="0 0 1423 560" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_89_5010)">
<rect width="466.426" height="946.782" rx="233.213" transform="matrix(0.98979 0.142533 -0.142431 0.989805 -317.481 -800.987)" fill="#D96B3C"/>
<rect width="467.786" height="946.783" rx="233.893" transform="matrix(0.990418 -0.138104 0.138005 0.990431 16.0464 -762.949)" fill="#F0913A"/>
<rect width="539.08" height="1161.86" rx="269.54" transform="matrix(0.987647 -0.156693 0.156582 0.987665 430.3 -935.53)" fill="#EA6300"/>
<rect width="466.424" height="946.787" rx="233.212" transform="matrix(0.997534 0.0701845 -0.0701336 0.997538 979.126 -814.984)" fill="#315A99"/>
<rect x="1384.58" y="-799.783" width="466.423" height="946.789" rx="233.211" fill="#357AE1"/>
<rect x="-427" y="-688.209" width="2278" height="993.669" fill="#F7F6F5" fill-opacity="0.59"/>
</g>
<defs>
<filter id="filter0_f_89_5010" x="-706.332" y="-1274" width="2811.33" height="1860" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="127" result="effect1_foregroundBlur_89_5010"/>
</filter>
</defs>
</svg>
