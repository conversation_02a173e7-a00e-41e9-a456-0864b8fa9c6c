name: td_procurement
description: 'A new Flutter project.'
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.3.2
  intl: ^0.20.2
  flutter_localizations:
    sdk: flutter
  gap: ^3.0.1
  go_router: ^15.1.3
  intercom_flutter: ^9.2.3
  equatable: ^2.0.3
  collection: ^1.15.0
  data_table_2: ^2.5.18
  td_flutter_core:
    git:
      url: https://github.com/tradedepot/td-flutter-core
      ref: app/procurement
  td_commons_flutter:
    git:
      url: https://github.com/tradedepot/td-commons-flutter
      ref: app/procurement
  flutter_svg: ^2.0.10+1
  percent_indicator: ^4.2.3
  flutter_screenutil: ^5.8.4
  shared_preferences: ^2.3.2
  json_annotation: ^4.9.0
  skeletonizer: ^2.0.1
  table_calendar: ^3.1.2
  printing: ^5.14.2
  pdf: ^3.11.1
  calendar_date_picker2: ^1.1.7
  file_picker: 8.1.4
  timelines_plus: ^1.0.4
  number_pagination: ^1.1.5
  cached_network_image: ^3.4.1
  libphonenumber: ^2.0.2
  google_maps_places_autocomplete_widgets: ^1.2.3
  url_launcher: ^6.3.1
  dlibphonenumber: ^1.1.25
  flutter_svg_provider: ^1.0.7
  flutter_libphonenumber_web: ^1.3.0
  sentry_flutter: ^9.1.0
  dio: ^5.7.0
  path_provider: ^2.1.5

dependency_overrides:
  connectivity_plus: ^6.1.0
  rxdart: ^0.28.0
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  json_serializable: ^6.2.0
  build_runner: ^2.4.13
  custom_lint_builder: ^0.6.5
  riverpod_lint: ^2.3.13
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  generate: true
  uses-material-design: true

  assets:
    # SVG
    - assets/svgs/
    - assets/svgs/packs/
    - assets/svgs/auth/
    - assets/svgs/business_verification/
    - assets/svgs/order/
    - assets/svgs/shipment/
    - assets/svgs/side_bar/
    - assets/svgs/settings/
    - assets/svgs/account/
    - assets/svgs/home/
    - assets/images/packs/

  fonts:
    - family: Pretendard Variable
      fonts:
        - asset: assets/fonts/PretendardVariable.ttf
          weight: 200
        - asset: assets/fonts/PretendardVariable.ttf
          weight: 300
        - asset: assets/fonts/PretendardVariable.ttf
          weight: 400
        - asset: assets/fonts/Pretendard_Medium.otf
          weight: 500
        - asset: assets/fonts/Pretendard_SemiBold.otf
          weight: 600
        - asset: assets/fonts/Pretendard_Bold.otf
          weight: 700
        - asset: assets/fonts/Pretendard_ExtraBold.otf
          weight: 800
    # - family: HelveticaNeue
    #   fonts:
    #     - asset: assets/fonts/HelveticaNeue/HelveticaNeue-Light.otf
    #       weight: 400
    #     - asset: assets/fonts/HelveticaNeue/HelveticaNeue-Medium.otf
    #       weight: 500
    #     - asset: assets/fonts/HelveticaNeue/HelveticaNeue-Bold.otf
    #       weight: 600
    #     - asset: assets/fonts/HelveticaNeue/HelveticaNeue-Heavy.otf
    #       weight: 700
    # - family: Roboto
    #   fonts:
    #     - asset: assets/fonts/roboto/Roboto-Light.ttf
    #       weight: 200
    #     - asset: assets/fonts/roboto/Roboto-Thin.ttf
    #       weight: 300
    #     - asset: assets/fonts/roboto/Roboto-Medium.ttf
    #       weight: 400
    #     - asset: assets/fonts/roboto/Roboto-Black.ttf
    #       weight: 500
    #     - asset: assets/fonts/roboto/Roboto-Bold.ttf
    #       weight: 600
    #     - asset: assets/fonts/roboto/Roboto-Bold.ttf
    #       weight: 700

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Scripts that can be run with "dart run rps <script>"
scripts:
  # web hot reload
  web: flutter run -d chrome --web-experimental-hot-reload
