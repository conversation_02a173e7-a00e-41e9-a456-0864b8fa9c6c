# Application Architecture

This document outlines the architecture of the TD Procurement Flutter application.

## Overview

The application follows a feature-based architecture. Each major feature of the application is encapsulated within its own directory in `lib/app`. This approach helps to keep the code organized, modular, and easy to maintain.

## Core Concepts

### State Management

The application uses [Riverpod](https://riverpod.dev/) for state management. Riverpod was chosen for its compile-safe and declarative approach to managing state. It allows for a clean separation of the UI from the business logic.

### Navigation

Navigation is handled by [GoRouter](https://pub.dev/packages/go_router). GoRouter provides a URL-based API for navigating between screens, which is ideal for a web-enabled Flutter application. It also simplifies deep linking and passing parameters between routes.

### Dependency Injection

The application uses a simple dependency injection (DI) pattern, with services and repositories being provided to the UI layer through Riverpod providers. This makes the code more testable and easier to reason about.

## Project Structure

The project is organized into the following main directories:

-   `lib/app`: Contains the feature-specific modules of the application. Each major feature is encapsulated within its own directory (e.g., `lib/app/auth`, `lib/app/order`). This approach promotes modularity and maintainability. Each feature directory follows a Clean Architecture-inspired structure, organized into three main layers: `data`, `domain`, and `presentation`.

    ```
    <feature_name>/
    ├── data/         # Data retrieval and storage
    │   ├── models/         # Data Transfer Objects (DTOs), request/response models (e.g., `user_dto.dart`)
    │   ├── repositories/   # Concrete implementations of domain repositories (e.g., `auth_repository_impl.dart`)
    │   └── sources/        # API clients, data sources (local/remote) (e.g., `auth_api_service.dart`)
    ├── domain/       # Core business logic, independent of other layers
    │   ├── entities/       # Core business objects (plain Dart objects) (e.g., `user_entity.dart`)
    │   ├── repositories/   # Abstract repository interfaces (e.g., `auth_repository.dart`)
    │   └── use_cases/      # Application-specific business logic (e.g., `login_use_case.dart`)
    └── presentation/ # UI layer, responsible for displaying data and handling user input
        ├── controllers/    # State management (e.g., Notifiers, Cubits, BLoCs) (e.g., `auth_controller.dart`)
        ├── screens/        # The main UI screen for a feature or part of a feature (e.g., `login_screen.dart`)
        └── widgets/        # Reusable widgets specific to this feature (e.g., `login_form_widget.dart`)
    ```

    **Layer Descriptions:**

    *   **`data` layer:** Responsible for data retrieval and storage. It implements the repository interfaces defined in the `domain` layer.
        *   `models`: Defines data structures (DTOs) that map directly to data sources (e.g., JSON responses from an API).
        *   `repositories`: Contains the concrete implementations of the abstract repository interfaces defined in the `domain` layer. These classes handle the actual data fetching from `sources`.
        *   `sources`: Contains classes that interact directly with external data sources, such as REST APIs, local databases, or shared preferences.

    *   **`domain` layer:** The core of the feature, containing the business logic. This layer is completely independent of other layers and should not have any dependencies on `data` or `presentation`.
        *   `entities`: Defines the core business objects (plain Dart objects) that represent the application's domain concepts.
        *   `repositories`: Contains abstract interfaces (contracts) for data operations. These interfaces are implemented by the `data` layer.
        *   `use_cases`: Encapsulates specific business logic. Use cases orchestrate interactions between entities and repositories to perform a specific application task.

    *   **`presentation` layer:** The UI layer, responsible for displaying data and handling user input. It depends on the `domain` layer but should not directly depend on the `data` layer.
        *   `controllers`: Manages the state of the UI and interacts with the `domain` layer (use cases) to fetch or manipulate data. This could be Riverpod Notifiers, BLoCs, Cubits, etc.
        *   `screens`: Contains the top-level widgets that represent distinct screens or pages in the application.
        *   `widgets`: Contains reusable UI components that are specific to the feature and are used within its screens.

-   `lib/core`: Contains the core components of the application that are shared across multiple features. This includes:
    -   `config`: Application-level configuration (e.g., `app_config.dart`).
    -   `DI`: Dependency injection setup and providers (e.g., `di_providers.dart`).
    -   `env`: Environment-specific configuration (e.g., `environment.dart`).
    -   `helpers`: Utility functions and helper classes that are generally applicable across the application (e.g., `date_formatter.dart`).
    -   `models`: Common data models used throughout the application that are not specific to a single feature (e.g., `api_response.dart`).
    -   `router`: GoRouter configuration and route definitions for the entire application (e.g., `app_router.dart`).
    -   `services`: Shared services, such as generic API clients, authentication services, or data repositories that are not feature-specific (e.g., `http_service.dart`).
-   `lib/src`: Contains the source code for reusable widgets, UI resources (colors, themes, etc.), and extensions that are shared across features but don't fit into `lib/core`.
    -   `components`: Generic, reusable UI widgets that can be used anywhere in the application (e.g., `custom_button.dart`).
    -   `extensions`: Dart extensions for various types (e.g., `string_extensions.dart`).
    -   `res`: Application resources like colors, themes, text styles, and constants (e.g., `app_colors.dart`, `app_theme.dart`).
    -   `utils`: General utility functions or classes that don't belong to a specific feature or core service (e.g., `validator.dart`).
-   `assets`: Contains static assets, such as images, fonts, and SVGs.
    -   `fonts/`: Custom font files.
    -   `images/`: Raster images (PNG, JPG).
    -   `svgs/`: Scalable Vector Graphics, often organized by feature or category.

## Naming Conventions

To maintain consistency and readability, the following naming conventions are adopted:

*   **Files and Directories:** `snake_case` (e.g., `login_screen.dart`, `user_entity.dart`, `auth_repository_impl.dart`).
*   **Classes and Enums:** `PascalCase` (e.g., `LoginScreen`, `UserEntity`, `AuthRepositoryImpl`).
*   **Functions and Variables:** `camelCase` (e.g., `loginUser`, `userName`).
*   **Constants:** `SCREAMING_SNAKE_CASE` for global constants (e.g., `BASE_URL`), or `camelCase` for `final` variables within classes.
*   **Abstract Classes/Interfaces:** Often prefixed with `I` or `Base` (e.g., `IAuthRepository`, `BaseApiService`), or simply named without a prefix if the context makes it clear it's an interface (e.g., `AuthRepository`).
*   **Models/DTOs:** Suffixed with `Dto` or `Model` (e.g., `UserDto`, `ProductModel`).
*   **Entities:** Suffixed with `Entity` (e.g., `UserEntity`).
*   **Repositories:** Suffixed with `Repository` for interfaces and `RepositoryImpl` for implementations (e.g., `AuthRepository`, `AuthRepositoryImpl`).
*   **Use Cases:** Suffixed with `UseCase` (e.g., `LoginUseCase`).
*   **Controllers/Notifiers:** Suffixed with `Controller` or `Notifier` (e.g., `AuthController`, `AuthStateNotifier`).
*   **Screens:** Suffixed with `Screen` (e.g., `LoginScreen`).
*   **Widgets:** Suffixed with `Widget` (e.g., `CustomButtonWidget`).
*   **Providers (Riverpod):** Typically `camelCase` and often suffixed with `Provider` (e.g., `authControllerProvider`).
-   `lib/core`: Contains the core components of the application that are shared across multiple features. This includes:
    -   `config`: Application-level configuration.
    -   `DI`: Dependency injection setup.
    -   `env`: Environment-specific configuration.
    -   `helpers`: Utility functions and helper classes.
    -   `models`: Data models used throughout the application.
    -   `router`: GoRouter configuration and route definitions.
    -   `services`: Shared services, such as API clients and data repositories.
-   `lib/src`: Contains the source code for reusable widgets, UI resources (colors, themes, etc.), and extensions.
-   `assets`: Contains static assets, such as images, fonts, and SVGs.

## UI and Styling

The application uses a custom design system, with a set of predefined colors, text styles, and reusable widgets. This ensures a consistent look and feel across the application.

-   **Widgets**: Reusable UI components are located in `lib/src/components`.
-   **Styling**: The application uses a custom theme, defined in `lib/src/res`.
-   **Icons**: SVG icons are used throughout the application and are located in `assets/svgs`.

## Testing

The project includes a `test` directory for unit and widget tests. The tests are written using the `flutter_test` framework.