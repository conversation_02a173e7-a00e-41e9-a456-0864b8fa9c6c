<!DOCTYPE html>
<html>
<head>
    <!--
      If you are serving your web app in a path other than the root, change the
      href value below to reflect the base path you are serving from.

      The path provided below has to start and end with a slash "/" in order for
      it to work correctly.

      For more details:
      * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

      This is a placeholder for base href that will be replaced by the value of
      the `--base-href` argument provided to `flutter build`.
    -->
    <base href="$FLUTTER_BASE_HREF">

    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta name="description" content="A new Flutter project.">

    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="td_procurement">
    <link rel="apple-touch-icon" href="icons/Td-Icon-192.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png"/>

    <title>Iconic brands for the next billion customers</title>
    <link rel="manifest" href="manifest.json">

    <style>
      body {
        background-color: #f9f9f9;
        inset: 0;
        overflow: hidden;
        margin: 0;
        padding: 0;
        position: fixed;
      }
    
      #loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        animation: fadeIn 1s ease-in-out;
      }
    
      #loading-indicator img {
        width: 100px;
        height: 100px;
        animation: breathe 2s infinite ease-in-out;
      }
    
      #loading-message {
        margin-top: 10px;
        font-size: 1rem;
        color: #555;
        text-align: center;
      }
    
      @keyframes breathe {
        0% {
          transform: scale(1);
        }
    
        50% {
          transform: scale(1.1);
        }
    
        100% {
          transform: scale(1);
        }
      }
    
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
    
        to {
          opacity: 1;
        }
      }
    </style>
    
</head>
<body>
<!-- Loading Screen -->
<div id="loading-indicator">
  <img src="logo.svg" alt="Loading indicator..." />
  <!-- <div id="loading-message">Loading the app...</div> -->
</div>

<!-- Flutter App Script -->
<script type="module">
  import init from "./flutter.js";
  const canvas = document.createElement("canvas");
  document.body.appendChild(canvas);

  init(canvas).then(() => {
    // Remove loading screen once the app is initialized
    document.getElementById("loading-indicator").style.display = "none";
  });
</script>

<!-- prettier-ignore -->
<!-- <script>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
        ({key: "${GOOGLE_API_KEY}", v: "weekly"});
</script> -->
<script>(function(g){var h,a,p="The Google Maps JavaScript API",c="google",l="importLibrary",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),e=new URLSearchParams,u=function(){return h||(h=new Promise(function(f,n){a=m.createElement("script");e.set("key",g.key);e.set("loading","async");e.set("callback","Function.prototype");a.src="https://maps."+c+"apis.com/maps/api/js?"+e.toString();d.__ib__=f;a.onerror=function(){h=n(Error(p+" could not load."))};a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}))};if(d[l]){console.warn(p+" only loads once. Ignoring:",g)}else{d[l]=function(f){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];return u().then(function(){return d[l](f,...i)})}}})({key:"${GOOGLE_API_KEY}"});</script>
<script>
    window.intercomSettings = {
        hide_default_launcher: false,
        app_id: "${INTERCOM_APP_ID}",
    };
  (function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',w.intercomSettings);}else{var d=document;var i=function(){i.c(arguments);};i.q=[];i.c=function(args){i.q.push(args);};w.Intercom=i;var l=function(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/';var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s, x);};if(document.readyState==='complete'){l();}else if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})();
</script>
<script src="js/places.js"></script>
<script src="flutter_bootstrap.js" async></script>
</body>
</html>
