 async function initPrediction(input) {
      const { Place, AutocompleteSessionToken, AutocompleteSuggestion } =
        await google.maps.importLibrary("places");
      let request = {
        input: input,
        language: "en-US",
      };
      const token = new AutocompleteSessionToken();
      request.sessionToken = token;
      const { suggestions } =  await AutocompleteSuggestion.fetchAutocompleteSuggestions(request);
      return JSON.stringify(suggestions);
    }

    async function fetchPlace(id){
      const { Place } = await google.maps.importLibrary("places");
      const place = new Place({
        id: id,
        requestedLanguage: "en", // optional
      });
      // Call fetchFields, passing the desired data fields.
      await place.fetchFields({
        fields: ["displayName", "formattedAddress", "location","addressComponents"],
      });
      return JSON.stringify(place);
    }

    window._initPrediction = initPrediction;
    window._fetchPlace = fetchPlace;