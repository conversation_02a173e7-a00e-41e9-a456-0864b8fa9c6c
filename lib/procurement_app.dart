import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:td_procurement/core/router/router.dart';
import 'package:td_procurement/procurement10n/app_localizations.dart';
import 'package:td_procurement/src/components/widgets/mobile_page.dart';
import 'package:td_procurement/src/res/theme/theme.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class ProcurementApp extends ConsumerStatefulWidget {
  const ProcurementApp({super.key});
  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _ProcurementApp();
  }
}

class _ProcurementApp extends ConsumerState<ProcurementApp> {
  @override
  void initState() {
    super.initState();
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    final appRouter = ref.read(routerProvider);
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: 'TradeDepot Procurement App',
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      theme: lightTheme,
      routerConfig: appRouter.router,
      builder: (context, child) => GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: MediaQuery.of(context).size.width > kDesktopMinSize
            ? child!
            : const MobilePage(),
      ),
    );
  }
}
