import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:td_procurement/procurement_app.dart';
import 'configure_non_web.dart'
    if (dart.library.js_interop) 'configure_web.dart';
import 'core/DI/di_providers.dart';
import 'core/services/shared_prefs/shared_prefs.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final prefs = await SharedPreferences.getInstance();
  final sharedPref = SharedPrefs()..initPreferences = prefs;
  configureApp();
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.black,
        systemNavigationBarColor: Colors.black,
        statusBarBrightness: Brightness.dark),
  );
  return runApp(
    ProviderScope(
      overrides: [storageProvider.overrideWithValue(sharedPref)],
      child: const ProcurementApp(),
    ),
  );
}
