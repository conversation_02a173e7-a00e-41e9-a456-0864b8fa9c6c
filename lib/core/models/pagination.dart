import 'dart:convert';

class Pagination {
  final int page;
  final int perPage;
  final int totalPages;

  Pagination({
    required this.page,
    required this.perPage,
    required this.totalPages,
  });

  Pagination copyWith({
    int? page,
    int? perPage,
    int? totalPages,
  }) {
    return Pagination(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      totalPages: totalPages ?? this.totalPages,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'perPage': perPage,
      'totalPages': totalPages,
    };
  }

  factory Pagination.fromMap(Map<String, dynamic> map) {
    return Pagination(
      page: map['page'] ?? 0,
      perPage: map['perPage'] ?? 0,
      totalPages: map['totalPages'] ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory Pagination.fromJson(String source) =>
      Pagination.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}
