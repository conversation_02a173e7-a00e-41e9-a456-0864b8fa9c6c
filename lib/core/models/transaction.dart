import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/src/res/values/app_values/app_values.dart';

class Transaction extends Equatable {
  final String id;
  final Currency? currency;
  final String retailOutletId;
  final String? outletBusinessName;
  final num? amount;
  final String? objectType;
  final num? shippingCost;
  final num? discounts;
  final num? processingCost;
  final num? itemTotal;
  final num? tax;
  final String? summary;
  final String? reference;
  final String? paymentReference;
  final String? country;
  final String? state;
  final String? shippingType;
  final String? status;
  final String? approvalStatus;
  final String? shippingStatus;
  final num? pickupCode;
  final String? paymentIntentId;
  final DateTime? postingDate;
  final DateTime? updatedAt;
  final DateTime? createdAt;
  final FirstOrderItem? firstOrderItem;
  final bool canCancel;
  final String? draftOrderNumber;
  final String? contactPhone;
  final DateTime? estimatedDeliveryDate;
  final String? extChannel;
  final DateTime? issuedAt;
  final List<OrderItem>? items;
  final List<Note> notes;
  final num? orderTotal;
  final num? partTotal;
  final String? phoneNumber;
  final bool? preview;
  final String? globalOrderNumber;

  /// Indicates whether this transaction is a global/export order.
  ///
  /// This is the base field that determines if an order is global/export.
  /// Use [isGlobalOrder] or [isExportOrder] getters to access this value.
  /// These getters are provided for semantic clarity but return the same value.
  final bool isGlobal;

  const Transaction(
      {required this.id,
      this.currency,
      required this.retailOutletId,
      this.outletBusinessName,
      this.amount,
      this.objectType,
      this.shippingCost,
      this.discounts,
      this.processingCost,
      this.itemTotal,
      this.tax,
      this.summary,
      this.reference,
      this.country,
      this.state,
      this.shippingType,
      this.status,
      this.approvalStatus,
      this.shippingStatus,
      this.pickupCode,
      this.paymentIntentId,
      this.postingDate,
      this.updatedAt,
      this.createdAt,
      this.firstOrderItem,
      required this.canCancel,
      this.draftOrderNumber,
      this.contactPhone,
      this.estimatedDeliveryDate,
      this.extChannel,
      this.issuedAt,
      this.items,
      this.notes = const [],
      this.orderTotal,
      this.partTotal,
      this.phoneNumber,
      this.preview,
      this.globalOrderNumber,
      this.paymentReference,
      this.isGlobal = false});

  // bool get isDraft => status?.toLowerCase() == 'pending';
  bool get isDraft => draftOrderNumber != null && draftOrderNumber!.isNotEmpty;
  String? get orderNumber => isDraft ? draftOrderNumber : reference;

  /// Indicates whether this transaction is a global/export order
  bool get isGlobalOrder => isGlobal;

  /// Indicates whether this transaction is a global/export order
  bool get isExportOrder => isGlobal;

  String get orderSummary {
    if (summary != null && summary!.isNotEmpty) {
      return summary!;
    } else if (items != null && items!.isNotEmpty) {
      return items!.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;

        // Check if the next item exists for formatting
        final hasNextItem = index + 1 < items!.length;

        return '${item.name}${hasNextItem ? ", " : ""}';
      }).join();
    }

    return '';
  }

  String get orderStatus => status == 'pending'
      ? 'Draft'
      : status == 'open'
          ? 'Pending'
          : status ?? '';

  factory Transaction.defaultValue() {
    return Transaction(
        id: 'id',
        currency: defaultCurrency,
        retailOutletId: 'retailOutletId',
        outletBusinessName: 'Sample Outlet',
        amount: 150.0,
        objectType: 'transaction',
        shippingCost: 10.0,
        discounts: 5.0,
        processingCost: 2.0,
        itemTotal: 140.0,
        tax: 15.0,
        summary: 'Transaction for purchase',
        reference: 'ref-123',
        country: 'USA',
        state: 'CA',
        shippingType: 'standard',
        status: 'completed',
        approvalStatus: 'approved',
        shippingStatus: 'delivered',
        pickupCode: 123456,
        paymentIntentId: 'pi_001',
        postingDate: DateTime.parse('2024-01-01T00:00:00Z'),
        updatedAt: DateTime.parse('2024-01-02T00:00:00Z'),
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        firstOrderItem: FirstOrderItem.defaultValue(),
        canCancel: true,
        draftOrderNumber: 'draftOrderNumber',
        contactPhone: '************',
        estimatedDeliveryDate: DateTime.parse('2024-01-05T00:00:00Z'),
        extChannel: 'online',
        issuedAt: DateTime.parse('2024-01-01T00:00:00Z'),
        items: const [],
        notes: List.filled(2, Note(userId: 'id', body: 'body')),
        orderTotal: 160,
        partTotal: 150,
        phoneNumber: '************',
        preview: false,
        globalOrderNumber: '123-231-734');
  }

  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
        id: map['_id'] ?? map['id'] ?? '',
        currency:
            map['currency'] != null ? Currency.fromMap(map['currency']) : null,
        retailOutletId: map['retailOutletId'] ?? '',
        outletBusinessName: map['outletBusinessName'],
        amount: map['amount'],
        objectType: map['objectType'],
        shippingCost: map['shippingCost'],
        discounts: map['discounts'],
        processingCost: map['processingCost'],
        itemTotal: map['itemTotal'],
        tax: map['tax'],
        summary: map['summary'],
        reference: map['reference'],
        paymentReference: map['paymentReference'],
        country: map['country'],
        state: map['state'],
        shippingType: map['shippingType'],
        status: map['status'],
        approvalStatus: map['approvalStatus'],
        shippingStatus: map['shippingStatus'],
        pickupCode: map['pickupCode'],
        paymentIntentId: map['paymentIntentId'],
        postingDate: map['postingDate'] != null
            ? DateTime.parse(map['postingDate'])
            : null,
        updatedAt: parseDate(map['updatedAt']),
        createdAt: parseDate(map['createdAt']),
        firstOrderItem: map['firstOrderItem'] != null
            ? FirstOrderItem.fromMap(map['firstOrderItem'])
            : null,
        canCancel: map['canCancel'] ?? false,
        draftOrderNumber: map['draftOrderNumber'],
        contactPhone: map['contactPhone'],
        estimatedDeliveryDate: parseDate(map['estimatedDeliveryDate']),
        extChannel: map['extChannel'],
        issuedAt: parseDate(map['issuedAt']),
        items: (map['items'] != null && map['items'] is List)
            ? List<OrderItem>.from(
                map['items'].map((x) => OrderItem.fromMap(x)))
            : [],
        notes: (map['notes'] != null && map['notes'] is List)
            ? List<Note>.from(map['notes'].map((x) => Note.fromMap(x)))
            : [],
        orderTotal: map['orderTotal'],
        partTotal: map['partTotal'],
        phoneNumber: map['phoneNumber'],
        preview: map['preview'],
        globalOrderNumber: map['globalOrderNumber']);
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'currency': currency?.toMap(),
      'retailOutletId': retailOutletId,
      'outletBusinessName': outletBusinessName,
      'amount': amount,
      'objectType': objectType,
      'shippingCost': shippingCost,
      'discounts': discounts,
      'processingCost': processingCost,
      'itemTotal': itemTotal,
      'tax': tax,
      'summary': summary,
      'reference': reference,
      'country': country,
      'state': state,
      'shippingType': shippingType,
      'status': status,
      'approvalStatus': approvalStatus,
      'shippingStatus': shippingStatus,
      'pickupCode': pickupCode,
      'paymentIntentId': paymentIntentId,
      'postingDate': postingDate?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'firstOrderItem': firstOrderItem?.toMap(),
      'canCancel': canCancel,
      'draftOrderNumber': draftOrderNumber,
      'contactPhone': contactPhone,
      'estimatedDeliveryDate': estimatedDeliveryDate?.toIso8601String(),
      'extChannel': extChannel,
      'issuedAt': issuedAt?.toIso8601String(),
      'items': items?.map((item) => item.toMap()).toList(),
      'notes': notes.map((n) => n.toMap()).toList(),
      'orderTotal': orderTotal,
      'partTotal': partTotal,
      'phoneNumber': phoneNumber,
      'preview': preview,
      'globalOrderNumber': globalOrderNumber,
      'paymentReference': paymentReference
    };
  }

  @override
  List<Object?> get props {
    return [
      id,
      currency,
      retailOutletId,
      outletBusinessName,
      amount,
      objectType,
      shippingCost,
      discounts,
      processingCost,
      itemTotal,
      tax,
      summary,
      reference,
      country,
      state,
      shippingType,
      status,
      approvalStatus,
      shippingStatus,
      pickupCode,
      paymentIntentId,
      postingDate,
      updatedAt,
      createdAt,
      firstOrderItem,
      canCancel,
      draftOrderNumber,
      contactPhone,
      estimatedDeliveryDate,
      extChannel,
      issuedAt,
      items,
      notes,
      orderTotal,
      partTotal,
      phoneNumber,
      preview,
      paymentReference
    ];
  }

  @override
  String toString() => '${toMap()}';
}

class FirstOrderItem extends Equatable {
  final String id;
  final String? name;
  final num? quantity;
  final num? price;
  final num? promoPrice;
  final num? basePrice;
  final String variantId;
  final String? status;
  final bool? isPromo;
  final String? masterCode;
  final num? discount;
  final String? code;
  final num? principalPrice;
  final num? principalPromoPrice;
  final num? promoDiscount;
  final bool? hasPromo;
  final num? taxRateOverride;
  final num? shippedQuantity;
  final bool isDropShipping;

  const FirstOrderItem({
    required this.id,
    this.name,
    this.quantity,
    this.price,
    this.promoPrice,
    this.basePrice,
    required this.variantId,
    this.status,
    this.isPromo,
    this.masterCode,
    this.discount,
    this.code,
    this.principalPrice,
    this.principalPromoPrice,
    this.promoDiscount,
    this.hasPromo,
    this.taxRateOverride,
    this.shippedQuantity,
    required this.isDropShipping,
  });

  factory FirstOrderItem.defaultValue() {
    return const FirstOrderItem(
      id: 'default-item-id',
      name: 'Default Item',
      quantity: 1,
      price: 100.0,
      promoPrice: 90.0,
      basePrice: 80.0,
      variantId: 'default-variant-id',
      status: 'available',
      isPromo: true,
      masterCode: 'default-master-code',
      discount: 10.0,
      code: 'default-code',
      principalPrice: 95.0,
      principalPromoPrice: 85.0,
      promoDiscount: 5.0,
      hasPromo: true,
      taxRateOverride: 0.08,
      shippedQuantity: 1,
      isDropShipping: false,
    );
  }

  FirstOrderItem copyWith({
    String? id,
    String? name,
    num? quantity,
    num? price,
    num? promoPrice,
    num? basePrice,
    String? variantId,
    String? status,
    bool? isPromo,
    String? masterCode,
    num? discount,
    String? code,
    num? principalPrice,
    num? principalPromoPrice,
    num? promoDiscount,
    bool? hasPromo,
    num? taxRateOverride,
    num? shippedQuantity,
    bool? isDropShipping,
  }) {
    return FirstOrderItem(
      id: id ?? this.id,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      promoPrice: promoPrice ?? this.promoPrice,
      basePrice: basePrice ?? this.basePrice,
      variantId: variantId ?? this.variantId,
      status: status ?? this.status,
      isPromo: isPromo ?? this.isPromo,
      masterCode: masterCode ?? this.masterCode,
      discount: discount ?? this.discount,
      code: code ?? this.code,
      principalPrice: principalPrice ?? this.principalPrice,
      principalPromoPrice: principalPromoPrice ?? this.principalPromoPrice,
      promoDiscount: promoDiscount ?? this.promoDiscount,
      hasPromo: hasPromo ?? this.hasPromo,
      taxRateOverride: taxRateOverride ?? this.taxRateOverride,
      shippedQuantity: shippedQuantity ?? this.shippedQuantity,
      isDropShipping: isDropShipping ?? this.isDropShipping,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'name': name,
      'quantity': quantity,
      'price': price,
      'promoPrice': promoPrice,
      'basePrice': basePrice,
      'variantId': variantId,
      'status': status,
      'isPromo': isPromo,
      'masterCode': masterCode,
      'discount': discount,
      'code': code,
      'principalPrice': principalPrice,
      'principalPromoPrice': principalPromoPrice,
      'promoDiscount': promoDiscount,
      'hasPromo': hasPromo,
      'taxRateOverride': taxRateOverride,
      'shippedQuantity': shippedQuantity,
      'isDropShipping': isDropShipping,
    };
  }

  factory FirstOrderItem.fromMap(Map<String, dynamic> map) {
    return FirstOrderItem(
      id: map['_id'] ?? map['id'] ?? '',
      name: map['name'],
      quantity: map['quantity'] ?? 0,
      price: map['price'] ?? 0,
      promoPrice: map['promoPrice'] ?? 0,
      basePrice: map['basePrice'] ?? 0,
      variantId: map['variantId'],
      status: map['status'],
      isPromo: map['isPromo'],
      masterCode: map['masterCode'],
      discount: map['discount'],
      code: map['code'],
      principalPrice: map['principalPrice'] ?? 0,
      principalPromoPrice: map['principalPromoPrice'] ?? 0,
      promoDiscount: map['promoDiscount'],
      hasPromo: map['hasPromo'],
      taxRateOverride: map['taxRateOverride'] ?? 0,
      shippedQuantity: map['shippedQuantity'],
      isDropShipping: map['isDropShipping'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory FirstOrderItem.fromJson(String source) =>
      FirstOrderItem.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props {
    return [
      id,
      name,
      quantity,
      price,
      promoPrice,
      basePrice,
      variantId,
      status,
      isPromo,
      masterCode,
      discount,
      code,
      principalPrice,
      principalPromoPrice,
      promoDiscount,
      hasPromo,
      taxRateOverride,
      shippedQuantity,
      isDropShipping,
    ];
  }
}

class Note {
  final String? userId;
  final String? body;
  Note({
    this.userId,
    this.body,
  });

  Note copyWith({
    String? userId,
    String? body,
  }) {
    return Note(
      userId: userId ?? this.userId,
      body: body ?? this.body,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'body': body,
    };
  }

  factory Note.fromMap(Map<String, dynamic> map) {
    return Note(
      userId: map['userId'],
      body: map['body'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Note.fromJson(String source) => Note.fromMap(json.decode(source));

  @override
  String toString() => 'Note(userId: $userId, body: $body)';
}
