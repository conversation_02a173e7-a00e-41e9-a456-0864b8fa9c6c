import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_commons_flutter/utils/index.dart';

import 'index.dart';

class OrderDetails {
  final Transaction transaction;
  final Shipment? shipment;
  final List<Order> orders;
  final Pagination pagination;

  OrderDetails({
    required this.transaction,
    this.shipment,
    required List<Order> orders,
    required this.pagination,
  }) : orders = List.unmodifiable(orders);

  factory OrderDetails.defaultValue() {
    return OrderDetails(
      transaction: Transaction.defaultValue(),
      // shipment: Shipment.defaultValue(),
      orders: List.filled(2, Order(id: 'id')),
      pagination: Pagination(page: 1, perPage: 10, totalPages: 1),
    );
  }

  OrderDetails copyWith({
    Transaction? transaction,
    Shipment? shipment,
    List<Order>? orders,
    Pagination? pagination,
  }) {
    return OrderDetails(
      transaction: transaction ?? this.transaction,
      shipment: shipment ?? this.shipment,
      orders: orders ?? this.orders,
      pagination: pagination ?? this.pagination,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'transaction': transaction.toMap(),
      'shipment': shipment?.toMap(),
      'orders': orders.map((x) => x.toMap()).toList(),
      'pagination': pagination.toMap(),
    };
  }

  factory OrderDetails.fromMap(Map<String, dynamic> map) {
    return OrderDetails(
      transaction: Transaction.fromMap(map['transaction']),
      shipment: (map['shipment'] != null &&
              map['shipment'] is List &&
              map['shipment'].isNotEmpty)
          ? Shipment.fromMap(map['shipment'][0])
          : null,
      orders: (map['orders'] != null &&
              map['orders'] is List &&
              map['orders'].isNotEmpty)
          ? List<Order>.from(map['orders'].map((x) => Order.fromMap(x)))
          : [],
      pagination: Pagination.fromMap(map['pagination']),
    );
  }

  String toJson() => json.encode(toMap());

  factory OrderDetails.fromJson(String source) {
    try {
      return OrderDetails.fromMap(json.decode(source));
    } catch (e) {
      throw FormatException('Invalid JSON for OrderDetails: $e');
    }
  }

  @override
  String toString() => '${toMap()}';
}

class OrderTracking extends Equatable {
  final bool isOrderPlaced;
  final DateTime? orderPlacedDate;
  final bool isOrderConfirmed;
  final DateTime? orderConfirmedDate;
  final bool isOrderOutForDelivery;
  final DateTime? orderOutForDeliveryDate;
  final bool isOrderDelivered;
  final DateTime? orderDeliveredDate;

  const OrderTracking({
    required this.isOrderPlaced,
    this.orderPlacedDate,
    required this.isOrderConfirmed,
    this.orderConfirmedDate,
    required this.isOrderOutForDelivery,
    this.orderOutForDeliveryDate,
    required this.isOrderDelivered,
    this.orderDeliveredDate,
  });

  @override
  List<Object?> get props => [
        isOrderPlaced,
        orderPlacedDate,
        isOrderConfirmed,
        orderConfirmedDate,
        isOrderOutForDelivery,
        orderOutForDeliveryDate,
        isOrderDelivered,
        orderDeliveredDate
      ];
}

class ExportOrderTracking {
  final String status;
  final DateTime? createdAt;

  ExportOrderTracking({
    required this.status,
    this.createdAt,
  });

  ExportOrderTracking copyWith({
    String? status,
    DateTime? createdAt,
  }) {
    return ExportOrderTracking(
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'status': status,
      'createdAt': createdAt?.millisecondsSinceEpoch,
    };
  }

  factory ExportOrderTracking.fromMap(Map<String, dynamic> map) {
    return ExportOrderTracking(
      status: map['status'] ?? '',
      createdAt: map['createdAt'] != null ? parseDate(map['createdAt']) : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory ExportOrderTracking.fromJson(String source) =>
      ExportOrderTracking.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}
