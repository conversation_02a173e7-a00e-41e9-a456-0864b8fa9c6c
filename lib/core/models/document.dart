import 'package:equatable/equatable.dart';

class Document extends Equatable {
  final String id;
  final String documentType;
  final String extension;
  final String shipmentId;
  final String name;
  final num size;
  final String mimeType;
  
  const Document({
    required this.id,
    required this.documentType,
    required this.extension,
    required this.shipmentId,
    required this.name,
    required this.size,
    required this.mimeType,
  });

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'documentType': documentType,
      'extension': extension,
      'shipmentId': shipmentId,
      'name': name,
      'size': size,
      'mimeType': mimeType,
    };
  }

  factory Document.fromMap(Map<String, dynamic> map) {
    return Document(
      id: map['_id'],
      documentType: map['documentType'],
      extension: map['extension'],
      shipmentId: map['shipmentId'],
      name: map['name'],
      size: map['size'],
      mimeType: map['mimeType'],
    );
  }

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props => [
        id,
        documentType,
        extension,
        shipmentId,
        name,
        size,
        mimeType,
      ];
}
