class QueryParameters {
  final int page;
  final int totalPages;
  final int perPage;

  const QueryParameters({
    this.page = 1,
    this.totalPages = 1,
    this.perPage = 10,
  });

  factory QueryParameters.fromMap(Map<String, dynamic> map) {
    return QueryParameters(
      page: _intParser(map['page'] ?? 1),
      totalPages: _intParser(map['totalPages'] ?? 1),
      perPage: _intParser(map['perPage'] ?? 10),
    );
  }

  int get totalCount => totalPages * perPage;

  @override
  String toString() =>
      'QueryParameters(page: $page, totalPages: $totalPages, perPage: $perPage)';
}

int _intParser(dynamic value) {
  if (value is int) return value;
  if (value is String) {
    return int.tryParse(value) ??
        (throw FormatException("Invalid integer string: $value"));
  }
  if (value is num) return value.toInt();
  // throw ArgumentError('Unsupported type: ${value.runtimeType}');
  return 0;
}
