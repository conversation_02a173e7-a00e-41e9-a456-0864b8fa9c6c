import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:flutter/cupertino.dart';

typedef ActionCall = Future<ApiResponse> Function();
typedef OnSuccessCall = VoidCallback;

Future<void> actionHandler(BuildContext context, ActionCall action,
    OnSuccessCall onSuccess, ValueNotifier<bool> loader) async {
  loader.value = true;
  final response = await action();
  loader.value = false;
  switch (response) {
    case Success():
      onSuccess();
    case Failure failure:
      {
        if (context.mounted) {
          Toast.apiError(failure.error, context);
        }
      }
  }
}
