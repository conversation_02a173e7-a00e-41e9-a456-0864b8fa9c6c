const kLoginRoute = 'sign-in';
const kRegisterRoute = 'sign-up';
const kLoginOTPRoute = 'login-OTP';
const kGetStartedRoute = 'get-started';
const kHomeRoute = 'home';
const kAccountRoute = 'account-statement';
const kOrdersRoute = 'purchase-orders';
const kOrderSummaryRoute = 'order-summary';
const kSalesOrdersRoute = 'sales-orders';
const kSalesOrderSummaryRoute = 'sales-order-summary';
const kInvoicesRoute = 'purchase-invoices';
const kInvoiceSummaryRoute = 'invoice-summary';
const kInvoiceDetailsRoute = 'invoice-details';
const kSalesInvoicesRoute = 'sales-invoices';
const kSalesInvoiceSummaryRoute = 'sales-invoice-summary';
const kStripeRoute = 'stripe-verification';
const kBusinessVerificationRoute = 'business-verification';
const kCreateOrderRoute = 'create-purchase-order';
const kUploadDocumentsRoute = 'upload-documents';
const kLoanApplication = 'loan-application';
const kLoanForm = 'loan-form';
const kCreateSalesOrderRoute = 'create-sales-order';
const kContainerInfoRoute = 'container-information';
const kCatalogRoute = 'catalog';
const kDeliveriesRoute = 'deliveries';
const kDeliveryDetailsRoute = 'delivery-details';
const kPayoutRoute = 'payout';
const kPayoutSummaryRoute = 'payout-summary';
const kShipmentsRoute = 'shipments';
const kShippingDetailsRoute = 'shipping-details';
const kForbiddenRoute = 'forbidden';
const kAdvanceInvoiceListRoute = 'advance-invoices';
const kCreateAdvanceInvoiceRoute = 'create-advance-invoice';
const kAdvanceInvoiceSummaryRoute = 'advance-invoice-summary';
const kAcceptPodRoute = 'accept-pod';

extension RoutePaths on String {
  String get path => "/$this";
}
