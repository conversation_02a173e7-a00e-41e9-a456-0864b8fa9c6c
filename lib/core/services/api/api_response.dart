import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

sealed class ApiResponse<T> {
  R when<R>({
    required R Function(T data) success,
    required R Function(AppException error, int statusCode) failure,
  }) {
    switch (this) {
      case Success<T>(data: var data):
        return success(data);
      case Failure<T>(error: var error, statusCode: var code):
        return failure(error, code);
    }
  }

  AsyncValue<T> asyncGuard() {
    return switch (this) {
      Success<T> data => AsyncData(data.data),
      Failure<T> error => AsyncError(error.error, StackTrace.empty)
    };
  }

  T extract() {
    switch (this) {
      case Success<T>(data: var data):
        return data;
      case Failure<T>(error: var error):
        throw error;
    }
  }

  R map<R>({
    required R Function(Success<T> success) success,
    required R Function(Failure<T> failure) failure,
  }) {
    switch (this) {
      case Success<T>(data: var data):
        return success(Success(data));
      case Failure<T>(error: var error, statusCode: var code):
        return failure(Failure(error, code));
    }
  }

  R maybeMap<R>({
    R Function(Success<T> success)? success,
    R Function(Failure<T> failure)? failure,
    required R Function() orElse,
  }) {
    switch (this) {
      case Success<T>(data: var data):
        return success != null ? success(Success(data)) : orElse();
      case Failure<T>(error: var error, statusCode: var code):
        return failure != null ? failure(Failure(error, code)) : orElse();
    }
  }
}

class Success<T> extends ApiResponse<T> {
  final T data;
  Success(this.data);
}

class Failure<T> extends ApiResponse<T> {
  final int statusCode;
  final AppException error;

  Failure(this.error, [this.statusCode = -1]);
}


// sealed class ApiResponse<T> {
//   R when<R>({
//     required R Function(T data) success,
//     required R Function(AppException error) failure,
//   }) {
//     switch (this) {
//       case Success<T>(data: var data):
//         return success(data);
//       case Failure<T>(error: var error):
//         return failure(error);
//     }
//   }

//   AsyncValue<T> asyncGuard() {
//     return switch (this) {
//       Success<T> data => AsyncData(data.data),
//       Failure<T> error => AsyncError(error.error, StackTrace.empty)
//     };
//   }

//   T extract() {
//     switch (this) {
//       case Success<T>(data: var data):
//         return data;
//       case Failure<T>(error: var error):
//         throw error;
//     }
//   }

//   R map<R>({
//     required R Function(Success<T> success) success,
//     required R Function(Failure<T> failure) failure,
//   }) {
//     switch (this) {
//       case Success<T>(data: var data):
//         return success(Success(data));
//       case Failure<T>(error: var error):
//         return failure(Failure(error));
//     }
//   }

//   R maybeMap<R>({
//     R Function(Success<T> success)? success,
//     R Function(Failure<T> failure)? failure,
//     required R Function() orElse,
//   }) {
//     switch (this) {
//       case Success<T>(data: var data):
//         return success != null ? success(Success(data)) : orElse();
//       case Failure<T>(error: var error):
//         return failure != null ? failure(Failure(error)) : orElse();
//     }
//   }
// }

// class Success<T> extends ApiResponse<T> {
//   final T data;
//   Success(this.data);
// }

// class Failure<T> extends ApiResponse<T> {
//   final int statusCode;
//   final AppException error;

//   Failure(this.error, [this.statusCode = -1]);
// }
