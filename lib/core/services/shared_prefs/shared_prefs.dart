import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefs {
  late final SharedPreferences _prefs;

  // void init() async {
  //   _prefs = await SharedPreferences.create(
  //     cacheOptions: const SharedPreferencesWithCacheOptions(),
  //   );
  // }

  set initPreferences(SharedPreferences pref) => _prefs = pref;

  String? read(String key) {
    return _prefs.getString(key);
  }

  void save(String key, value) {
    _prefs.setString(key, value);
  }

  void remove(String key) {
    _prefs.remove(key);
  }

  bool check(String key) {
    return _prefs.containsKey(key);
  }

  void storeInt(String key, value) {
    _prefs.setInt(key, value);
  }

  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  void storeDouble(String key, value) {
    _prefs.setDouble(key, value);
  }

  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  void storeBool(String key, value) {
    _prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  Future<void> clear() async => await _prefs.clear();

  Future<void> refresh() async => await _prefs.reload();
}
