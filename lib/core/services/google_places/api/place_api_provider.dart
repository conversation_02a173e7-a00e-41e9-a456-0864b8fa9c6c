import 'dart:convert';
import 'package:http/http.dart';

import '../model/place.dart';
import '../model/suggestion.dart';

class PlaceApiProvider {
  final client = Client();

  PlaceApiProvider(this.mapsApiKey, this.compomentCountry, this.language);

  final String mapsApiKey;
  final String? compomentCountry;
  final String? language;

  ///[includeFullSuggestionDetails] if we should include ALL details that are returned in API suggestions.
  ///   (This is sent as true when the `onInitialSuggestionClick` is in use)
  ///[postalCodeLookup] if we should request `postal_code` type return information
  ///   instead of address type information.
  Future<List<Suggestion>> fetchSuggestions(String input,
      {bool includeFullSuggestionDetails = false,
      bool postalCodeLookup = false}) async {
    final Map<String, dynamic> parameters = <String, dynamic>{
      'input': input,
      'types': postalCodeLookup
          ? 'postal_code'
          : 'address', // this is for looking up fully qualified addresses
      // Could be used for ZIP lookups//   'types': 'postal_code',
      'key': maps<PERSON><PERSON><PERSON>ey,
      //'sessiontoken': sessionToken
    };

    if (language != null) {
      parameters.addAll(<String, dynamic>{'language': language});
    }
    if (compomentCountry != null) {
      parameters
          .addAll(<String, dynamic>{'components': 'country:$compomentCountry'});
    }

    final Uri request = Uri(
        scheme: 'https',
        host: 'maps.googleapis.com',
        path: '/maps/api/place/autocomplete/json',
        queryParameters: parameters);

    final response = await client.get(request);

    if (response.statusCode == 200) {
      final result = json.decode(response.body);
      if (result['status'] == 'OK') {
        // compose suggestions in a list
        return result['predictions'].map<Suggestion>((p) {
          if (includeFullSuggestionDetails) {
            // Package everything useful from API json
            final mainText = p['structured_formatting']?['main_text'];
            final secondaryText = p['structured_formatting']?['secondary_text'];
            final terms = p['terms']
                .map<String>((term) => term['value'] as String)
                .toList();
            final types =
                p['types'].map<String>((atype) => atype as String).toList();

            return Suggestion(p['place_id'], p['description'],
                mainText: mainText,
                secondaryText: secondaryText,
                terms: terms,
                types: types);
          } else {
            // just use the simple Suggestion parts we need
            return Suggestion(p['place_id'], p['description']);
          }
        }).toList();
      }
      if (result['status'] == 'ZERO_RESULTS') {
        return [];
      }
      throw Exception(result['error_message']);
    } else {
      throw Exception('Failed to fetch suggestion');
    }
  }

  ///Requests full address info from Google Places API for the specified
  ///[placeId] and returns a [Place] object returned info.
  Future<Place> getPlaceDetailFromId(String placeId) async {
    // if you want to get the details of the selected place by place_id
    final Map<String, dynamic> parameters = <String, dynamic>{
      'place_id': placeId,
      'fields': 'name,formatted_address,address_component,geometry',
      'key': mapsApiKey,
      //'sessiontoken': sessionToken
    };
    final Uri request = Uri(
        scheme: 'https',
        host: 'maps.googleapis.com',
        path: '/maps/api/place/details/json',

        //PlaceApiNew     host: 'places.googleapis.com',
        //PlaceApiNew     path: '/v1/places/$placeId',

        queryParameters: parameters);

    final response = await client.get(request);
    /* PlaceApiNew:
        , headers: {
            'X-Goog-Api-Key': mapsApiKey,
            'X-Goog-FieldMask': 'displayName,formattedAddress',
      });
    PlaceApiNew */

    if (response.statusCode == 200) {
      final result = json.decode(response.body);
      if (result['status'] == 'OK') {
        final components =
            result['result']['address_components'] as List<dynamic>;

        // build result
        final place = Place();

        place.formattedAddress = result['result']['formatted_address'];
        place.name = result['result']['name'];
        place.lat = result['result']['geometry']['location']['lat'] as double;
        place.lng = result['result']['geometry']['location']['lng'] as double;

        for (var component in components) {
          final List type = component['types'];
          if (type.contains('street_address')) {
            place.streetAddress = component['long_name'];
          }
          if (type.contains('street_number')) {
            place.streetNumber = component['long_name'];
          }
          if (type.contains('route')) {
            place.street = component['long_name'];
            place.streetShort = component['short_name'];
          }
          if (type.contains('sublocality') ||
              type.contains('sublocality_level_1')) {
            place.vicinity = component['long_name'];
          }
          if (type.contains('locality')) {
            place.city = component['long_name'];
          }
          if (type.contains('administrative_area_level_2')) {
            place.county = component['long_name'];
          }
          if (type.contains('administrative_area_level_1')) {
            place.state = component['long_name'];
            place.stateShort = component['short_name'];
          }
          if (type.contains('country')) {
            place.country = component['long_name'];
            place.countryShort = component['short_name'];
          }
          if (type.contains('postal_code')) {
            place.zipCode = component['long_name'];
          }
          if (type.contains('postal_code_suffix')) {
            place.zipCodeSuffix = component['short_name'];
          }
        }

        place.zipCodePlus4 ??=
            '${place.zipCode}${place.zipCodeSuffix != null ? '-${place.zipCodeSuffix}' : ''}';
        if (place.streetNumber != null) {
          place.streetAddress ??= '${place.streetNumber} ${place.streetShort}';
          place.formattedAddress ??=
              '${place.streetNumber} ${place.streetShort}, ${place.city}, ${place.stateShort} ${place.zipCode}';
          place.formattedAddressZipPlus4 ??=
              '${place.streetNumber} ${place.streetShort}, ${place.city}, ${place.stateShort} ${place.zipCodePlus4}';
        }
        return place;
      }
      throw Exception(result['error_message']);
    } else {
      throw Exception('Failed to fetch suggestion');
    }
  }
}
