import 'package:td_procurement/core/services/google_places/address_autocomplete_widgets.dart';
import 'package:td_procurement/core/services/google_places/model/suggestion.dart';

class PlacesService {
  static Future<List<dynamic>> initPrediction(String input) async => [];
  static Future<dynamic> fetchPlace(String id) async => {};
  static Future<Place> fetchPlaceDetails(String id) async => Place();
  static Future<List<Suggestion>> fetchPredictions(String input) async => [];
}
