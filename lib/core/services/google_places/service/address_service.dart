import 'package:flutter/foundation.dart';
import 'package:td_procurement/core/services/google_places/model/place.dart';
import 'package:td_procurement/core/services/google_places/model/suggestion.dart';
import 'package:td_procurement/core/services/google_places/service/places/places_service.dart';

import '../api/place_api_provider.dart';

class AddressService {
  AddressService(this.mapsApiKey, this.componentCountry, this.language) {
    apiClient = PlaceApiProvider(mapsApiKey, componentCountry, language);
  }

  final String mapsApiKey;
  final String? componentCountry;
  final String? language;
  late PlaceApiProvider apiClient;

  Future<List<Suggestion>> search(String query,
      {bool includeFullSuggestionDetails = false,
      bool postalCodeLookup = false}) async {
    return kIsWeb
        ? await PlacesService.fetchPredictions(query)
        : await apiClient.fetchSuggestions(query,
            includeFullSuggestionDetails: includeFullSuggestionDetails,
            postalCodeLookup: postalCodeLookup);
  }

  Future<Place> getPlaceDetail(String placeId) async {
    Place placeDetails = kIsWeb
        ? await PlacesService.fetchPlaceDetails(placeId)
        : await apiClient.getPlaceDetailFromId(placeId);
    return placeDetails;
  }
}
