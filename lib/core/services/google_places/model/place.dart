class Place {
  String? name;
  String? formattedAddress;
  String? formattedAddressZipPlus4;
  String? streetAddress;
  String? streetNumber;
  String? streetShort;
  String? street;
  String? city;
  String? county;
  String? state;
  String? stateShort;
  String? zipCode;
  String? zipCodeSuffix;
  String? zipCodePlus4;
  String? vicinity;
  String? country;
  String? countryShort;
  double? lat;
  double? lng;

  Place({
    this.name,
    this.formattedAddress,
    this.formattedAddressZipPlus4,
    this.streetAddress,
    this.streetNumber,
    this.streetShort,
    this.street,
    this.city,
    this.county,
    this.state,
    this.countryShort,
    this.stateShort,
    this.zipCode,
    this.zipCodeSuffix,
    this.zipCodePlus4,
    this.vicinity,
    this.country,
    this.lat,
    this.lng,
  });

  String? get address => formattedAddress ?? streetAddress ?? streetShort;

  @override
  String toString() {
    return 'Place(name: $name, formattedAddress: $formattedAddress, formattedAddressZipPlus4: $formattedAddressZipPlus4, streetAddress: $streetAddress, streetNumber: $streetNumber, streetShort: $streetShort, street: $street, city: $city, county: $county, state: $state, stateShort: $stateShort, zipCode: $zipCode, zipCodeSuffix: $zipCodeSuffix, zipCodePlus4: $zipCodePlus4, vicinity: $vicinity, country: $country, countryShort: $countryShort, lat: $lat, lng: $lng)';
  }

  Map<String, dynamic> toData() {
    return {
      'address': address,
      'state': state,
      'country': countryShort,
    };
  }
}
