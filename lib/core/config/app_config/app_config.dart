class AppConfig {
  final String firebaseServiceUrl;
  final String consoleUrl;
  final String appUrl;
  final String awsApiUrl;
  final String awsApiUrlV2;
  final String awsApiUrlV3;
  final String awsApiUrlV4;
  final String googleApiKey;
  final String hubspotApiKey;
  final String searchUrl;
  final ENV env;
  AppConfig(
    this.firebaseServiceUrl,
    this.consoleUrl,
    this.appUrl,
    this.awsApiUrl,
    this.awsApiUrlV2,
    this.awsApiUrlV3,
    this.awsApiUrlV4,
    this.googleApiKey,
    this.searchUrl,
    this.hubspotApiKey,
    this.env,
  );

  AppConfig.fromJson(Map<Object, dynamic> map)
      : firebaseServiceUrl = map['FIREBASE_SERVICE_URL'],
        appUrl = map["APP_URL"],
        hubspotApiKey = map['HUBSPOT_API_KEY'],
        consoleUrl = map['CONSOLE_URL'],
        awsApiUrl = map['AWS_API_URL'],
        awsApiUrlV2 = map['AWS_API_URL_V2'],
        awsApiUrlV3 = map['AWS_API_URL_V3'],
        awsApiUrlV4 = map['AWS_API_URL_V4'],
        googleApiKey = map['GOOGLE_API_KEY'],
        searchUrl = map['SEARCH_URL'],
        env = ENV.parse(map['FLUTTER_APP_FLAVOR']);
}

enum ENV {
  dev("dev"),
  prod("prod");

  final String name;
  const ENV(this.name);

  static final Map<String, ENV> _map = {
    dev.name: dev,
    prod.name: prod,
  };

  static ENV parse(String? flavor, {ENV defaultEnv = ENV.dev}) {
    return flavor != null ? _map[flavor] ?? defaultEnv : defaultEnv;
  }

  @override
  String toString() => name;
}
