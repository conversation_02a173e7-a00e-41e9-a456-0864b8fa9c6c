import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/visits.dart';
import 'package:td_procurement/app/deliveries/domain/use_cases/deliveries_controller.dart';
import 'package:td_procurement/app/deliveries/domain/use_cases/visits_provider.dart';
import 'package:td_procurement/app/deliveries/presentation/widgets/deliveries_status_chip.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

class DeliveriesSummary extends ConsumerStatefulWidget {
  final String deliveryId;

  const DeliveriesSummary({
    super.key,
    required this.deliveryId,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DeliverySummary();
}

class _DeliverySummary extends ConsumerState<DeliveriesSummary> {
  final _cancelButtonNotifier = ValueNotifier<bool>(false);
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref.read(visitsProvider.notifier).clearVisit();
      _fetchData();
    });
  }

  @override
  void didUpdateWidget(covariant DeliveriesSummary oldWidget) {
    super.didUpdateWidget(oldWidget);
    _fetchData();
  }

  void _fetchData() {
    Future.microtask(() {
      fetchDeliveryDetails();
      fetchDeliveryOrders();
    });
  }

  Future<void> fetchDeliveryOrders() async {
    if (mounted) {
      setState(() => _isLoading = true);
    } else {
      _isLoading = true;
    }

    final res = await ref
        .read(deliveriesControllerProvider.notifier)
        .fetchDeliveryOrders(widget.deliveryId, forced: true);

    res.when(
      success: (data) {
        setState(() => _isLoading = false);
      },
      failure: (e, _) {
        setState(() => _isLoading = false);
        Toast.apiError(e, context);
      },
    );
  }

  void fetchDeliveryDetails() {
    ref
        .read(deliveriesControllerProvider.notifier)
        .fetchDeliveryDetails(widget.deliveryId, forced: true);
  }

  Future<void> cancelDelivery() async {
    _cancelButtonNotifier.value = true;
    final res = await ref
        .read(deliveriesControllerProvider.notifier)
        .cancelDelivery(widget.deliveryId);

    res.when(
      success: (_) {
        if (!mounted) return;
        _cancelButtonNotifier.value = false;
        context.pop();
        Toast.success('Delivery cancelled successfully', context);
        ref.read(deliveriesControllerProvider.notifier).fetchDeliveries(
            ref.read(deliveriesControllerProvider).fetchDeliveriesParam,
            forced: true);
      },
      failure: (e, _) {
        if (!mounted) return;
        _cancelButtonNotifier.value = false;
        Toast.apiError(e, context);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final deliveryDetails =
        ref.watch(deliveriesControllerProvider).deliveryDetails;
    final selectedVisit = ref.watch(visitsProvider);

    return ClipRRect(
      borderRadius: const BorderRadius.vertical(
        top: Radius.circular(16),
      ),
      child: deliveryDetails.when(
        data: (details) {
          if (details == null) {
            return const Center(child: CircularProgressIndicator());
          }
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(textTheme, deliveryDetails, selectedVisit),
                const Gap(20),
                _buildSummary(textTheme, deliveryDetails),
                const Gap(20),
                Divider(color: Colors.grey.withValues(alpha: 0.3)),
                const Gap(20),
                _buildDetailsContainer(
                    textTheme, deliveryDetails, selectedVisit),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, _) {
          final msg = e is Failure
              ? e.error.error.toString()
              : e is DefaultError
                  ? e.errorMessage
                  : '!';
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error loading delivery details: $msg'),
                const Gap(10),
                ElevatedButton(
                  onPressed: _fetchData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(TextTheme textTheme, deliveryDetails, selectedVisit) {
    final name = deliveryDetails.value?.driver?.profile?.fullName ?? "-";
    return SizedBox(
      height: 70,
      width: double.infinity,
      child: DecoratedBox(
        decoration: BoxDecoration(color: Palette.kFCFCFC),
        child: Row(
          children: [
            Flexible(
              child: Row(
                children: [
                  const Gap(20),
                  IconButton(
                    onPressed: () {
                      context.pop();
                      ref.read(visitsProvider.notifier).clearVisit();
                    },
                    icon: SvgPicture.asset(kSvgArrowBackIcon),
                  ),
                  const Gap(10),
                  Text('Deliveries',
                      style: textTheme.bodyLarge?.copyWith(
                          color: Palette.k6B797C, fontWeight: FontWeight.w400)),
                  const Gap(10),
                  SvgPicture.asset(kChevronRightSvg),
                  const Gap(10),
                  Text(name, style: textTheme.bodyMedium),
                  if (selectedVisit.id != null) ...[
                    const Gap(10),
                    SvgPicture.asset(kChevronRightSvg),
                    const Gap(10),
                    Text(selectedVisit.retailOutletName ?? '-',
                        style: textTheme.bodyMedium),
                  ],
                ],
              ),
            ),
            if (deliveryDetails.value?.status == 'planned')
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.only(right: 30),
                  child: _buildCancelButton(textTheme),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCancelButton(TextTheme textTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        ConstrainedBox(
          constraints:
              const BoxConstraints(minWidth: 116, minHeight: 40, maxHeight: 40),
          child: ValueListenableBuilder(
            valueListenable: _cancelButtonNotifier,
            builder: (context, loadingButton, child) {
              return TextButton.icon(
                style: TextButton.styleFrom(
                  elevation: 0,
                  foregroundColor: Palette.deleteRed,
                  backgroundColor:
                      loadingButton ? Palette.kECEDED : Palette.deleteRed,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  disabledBackgroundColor: Palette.kECEDED,
                ),
                onPressed: cancelDelivery,
                label: loadingButton
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator())
                    : Text('Cancel Trip',
                        style: textTheme.bodyMedium
                            ?.copyWith(color: Palette.kFFFFFF)),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSummary(TextTheme textTheme, deliveryDetails) {
    final name = deliveryDetails.value?.driver?.profile?.fullName ?? "-";
    final completionRate = (deliveryDetails.value?.visits
                .where((v) => v.status == 'completed')
                .length ??
            0) /
        (deliveryDetails.value?.visits.length ?? 0) *
        100;
    final totalAmount = deliveryDetails.value?.visits.fold<num>(
            0, (num sum, Visit visit) => sum + (visit.totalAmount ?? 0)) ??
        0;
    final totalQuantity = deliveryDetails.value?.visits.fold<num>(
            0, (num sum, Visit visit) => sum + (visit.itemsQuantity ?? 0)) ??
        0;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(name,
              style:
                  textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700)),
          const Gap(10),
          Row(
            children: [
              _buildSummaryColumn(
                  'VISIT',
                  deliveryDetails.value?.visits.length.toString() ?? '0',
                  textTheme),
              const Gap(60),
              _buildSummaryColumn(
                  'QUANTITY', totalQuantity.toString(), textTheme),
              const Gap(60),
              _buildSummaryColumn(
                  'COMPLETION RATE', '$completionRate%', textTheme),
              const Gap(60),
              _buildSummaryColumn('TOTAL', totalAmount.toString(), textTheme),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryColumn(String title, String value, TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildContentText(title, textTheme),
        _buildContentText(value, textTheme, true),
      ],
    );
  }

  Widget _buildDetailsContainer(
      TextTheme textTheme, deliveryDetails, selectedVisit) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 30),
      decoration: BoxDecoration(
        border: Border.all(color: Palette.kE7E7E7),
        boxShadow: const [
          BoxShadow(
              offset: Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 0,
              color: Palette.k0000000A)
        ],
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(16),
          if (selectedVisit.id != null)
            _buildVisitHeader(textTheme, selectedVisit),
          Padding(
            padding: const EdgeInsets.only(left: 20),
            child: Row(
              children: [
                Text(selectedVisit.id != null ? 'Orders' : "Deliveries",
                    style: textTheme.headlineSmall),
              ],
            ),
          ),
          const Gap(20),
          _buildDataTable(textTheme, deliveryDetails, selectedVisit),
        ],
      ),
    );
  }

  Widget _buildVisitHeader(TextTheme textTheme, selectedVisit) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, bottom: 12),
      child: Row(
        children: [
          TextButton(
            style: TextButton.styleFrom(elevation: 0),
            onPressed: () => ref.read(visitsProvider.notifier).clearVisit(),
            child: Text('Deliveries',
                style: textTheme.bodyMedium?.copyWith(color: Palette.k6B797C)),
          ),
          const Gap(10),
          SvgPicture.asset(kChevronRightSvg),
          const Gap(16),
          Text(selectedVisit.retailOutletName ?? '-',
              style: textTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildDataTable(TextTheme textTheme, deliveryDetails, selectedVisit) {
    return Row(
      children: [
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return selectedVisit.id != null
                  ? _visitOrdersTable(textTheme,
                      deliveryDetails.value?.visits ?? [], _isLoading)
                  : _visitsTable(textTheme, deliveryDetails.value?.visits ?? [],
                      _isLoading);
            },
          ),
        ),
      ],
    );
  }

  Widget _visitsTable(TextTheme textTheme, List<Visit> visits, bool isLoading) {
    return isLoading
        ? const SizedBox(
            height: 100,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          )
        : DataTable(
            showCheckboxColumn: false,
            headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
            columns: [
              DataColumn(
                label: _buildHeaderCell('Customer', textTheme),
              ),
              DataColumn(
                label: _buildHeaderCell('Quantity', textTheme),
              ),
              DataColumn(
                label: _buildHeaderCell('Total', textTheme),
              ),
              DataColumn(
                label: _buildHeaderCell('Status', textTheme),
              ),
              DataColumn(label: _buildHeaderCell('', textTheme))
            ],
            rows: visits
                .map((visit) => DataRow(
                      cells: [
                        DataCell(_buildTableBodyCell(
                            visit.retailOutletName ?? visit.locationName ?? '-',
                            textTheme)),
                        DataCell(_buildTableBodyCell(
                            visit.itemsQuantity.toString(), textTheme)),
                        DataCell(_buildContentAmount(
                            (visit.totalAmount ?? 0).toInt(),
                            textTheme,
                            null,
                            visit.currency)),
                        DataCell(DeliveryStatusChip(
                          status: visit.status ?? '-',
                        )),
                        DataCell(
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: Palette.orangePrimaryDark, width: 2),
                            ),
                            // padding: const EdgeInsets.symmetric(
                            //     horizontal: 8),
                            width: 60,
                            height: 30,
                            child: Center(
                                child: TextButton.icon(
                              style: TextButton.styleFrom(
                                  elevation: 0,
                                  foregroundColor: Palette.stroke),
                              onPressed: () => ref
                                  .read(visitsProvider.notifier)
                                  .setVisit(visit),
                              label: Text(
                                'View',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                        color: Palette.orangePrimaryDark,
                                        fontWeight: FontWeight.w500),
                              ),
                            )),
                          ),
                        ),
                      ],
                    ))
                .toList(),
          );
  }

  Widget _visitOrdersTable(
    TextTheme textTheme,
    List<Visit> visits,
    bool isLoading,
  ) {
    final visitOrderIds = ref.watch(visitsProvider).orderIds;
    final deliveriesState = ref.watch(deliveriesControllerProvider);

    final List<Order> orders =
        _getOrdersForVisit(visitOrderIds, deliveriesState);
    final List<OrderItem> orderItems = _getOrderItems(orders);

    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (orderItems.isEmpty) {
      return const EmptyWidget(
        icon: '$kSvgDir/order/cart.svg',
        title: 'No items for the order in this visit',
        subTitle: '',
      );
    }

    return _buildOrderItemsTable(textTheme, orderItems, orders);
  }

  List<Order> _getOrdersForVisit(List<String>? visitOrderIds, deliveriesState) {
    if (visitOrderIds == null || visitOrderIds.isEmpty) {
      return [];
    }

    final orders = deliveriesState.deliveryOrders;
    if (orders is AsyncData<List<Order>>) {
      return orders.value
              .where((order) => visitOrderIds.contains(order.id))
              .toList() ??
          [];
    }
    return [];
  }

  List<OrderItem> _getOrderItems(List<Order> orders) {
    return orders
        .expand((order) => order.items ?? [])
        .cast<OrderItem>()
        .toList();
  }

  Widget _buildOrderItemsTable(
      TextTheme textTheme, List<OrderItem> orderItems, List<Order> orders) {
    return DataTable(
      showCheckboxColumn: false,
      headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
      columns: _buildOrderTableColumns(textTheme),
      rows: _buildOrderTableRows(textTheme, orderItems, orders),
    );
  }

  List<DataColumn> _buildOrderTableColumns(TextTheme textTheme) {
    return [
      DataColumn(label: _buildHeaderCell('', textTheme)),
      DataColumn(label: _buildHeaderCell('Name', textTheme)),
      DataColumn(label: _buildHeaderCell('Quantity', textTheme)),
      DataColumn(label: _buildHeaderCell('Total', textTheme)),
    ];
  }

  List<DataRow> _buildOrderTableRows(
      TextTheme textTheme, List<OrderItem> orderItems, List<Order> orders) {
    return orderItems.map((item) {
      return DataRow(cells: [
        DataCell(SizedBox(
          width: 30,
          height: 30,
          child: CachedImage(item.variantId, ImageSize.small),
        )),
        DataCell(_buildTableBodyCell(item.name ?? '-', textTheme)),
        DataCell(_buildTableBodyCell(item.quantity.toString(), textTheme)),
        DataCell(_buildContentAmount(
            item.total, textTheme, null, orders.first.currency)),
      ]);
    }).toList();
  }

  Widget _buildContentText(String text, TextTheme textTheme,
      [bool isValue = false]) {
    if (!isValue) {
      return Padding(
          padding: const EdgeInsets.only(bottom: 5.0),
          child: Text(
            text,
            style: textTheme.bodyMedium?.copyWith(
                fontSize: 12,
                color: Palette.blackSecondary,
                fontWeight: FontWeight.w500),
          ));
    }
    return Text(
      text,
      style: textTheme.bodyMedium?.copyWith(
          fontSize: 14,
          color: Palette.primaryBlack,
          fontWeight: FontWeight.w400),
    );
  }

  Widget _buildTableBodyCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme,
      [Color? color, Currency? currency]) {
    return Padding(
      padding: const EdgeInsets.only(left: 0),
      child: Row(
        children: [
          CurrencyWidget(
            amount,
            currency?.iso ?? kDefaultCurrency,
            amountStyle: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Gap(5),
          Text(
            currency?.iso ?? 'NGN',
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
