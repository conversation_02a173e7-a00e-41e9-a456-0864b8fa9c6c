import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/deliveries/data/models/deliveries.dart';
import 'package:td_procurement/app/deliveries/domain/entities/deliveries_params.dart';
import 'package:td_procurement/app/deliveries/domain/use_cases/deliveries_controller.dart';
import 'package:td_procurement/app/deliveries/presentation/widgets/deliveries_pagination.dart';
import 'package:td_procurement/app/deliveries/presentation/widgets/deliveries_table.dart';
import 'package:td_procurement/app/deliveries/presentation/widgets/empty_deliveries.dart';
import 'package:td_procurement/app/order/presentation/widgets/orders_table.dart';
import 'package:td_procurement/app/shell/presentation/widgets/top_bar.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class DeliveriesWidget extends ConsumerStatefulWidget {
  const DeliveriesWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DeliveriesState();
}

class _DeliveriesState extends ConsumerState<DeliveriesWidget> {
  int activeIndex = 0;
  List<DeliveriesType>? deliveriesList;
  final ValueNotifier<DeliveriesStatus> _selectedOption =
      ValueNotifier(DeliveriesStatus.all);
  String queryText = '';
  final _deBouncer = DeBouncer(milliseconds: 500);
  String locationId = '';

  @override
  void initState() {
    super.initState();
    final userState = ref.read(userControllerProvider);
    locationId = userState?.retailOutlets?.first.salesLocationId ?? '';
    fetchDeliveries();
  }

  @override
  void didUpdateWidget(covariant DeliveriesWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    fetchDeliveries();
  }

  void fetchDeliveries() {
    final params = ref
        .read(deliveriesControllerProvider)
        .fetchDeliveriesParam
        .copyWith(locationId: locationId);
    Future.microtask(() => ref
        .read(deliveriesControllerProvider.notifier)
        .fetchDeliveries(params, forced: true));
  }

  void _onTextChanged(String query) {
    queryText = query;
    // _deBouncer.run(() async {
    //   final params = ref.read(deliveriesArgProvider);
    //   ref.read(deliveriesArgProvider.notifier).state =
    //       params.querySearch(query, _selectedOption.value);
    // });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final deliveriesState = ref.watch(deliveriesControllerProvider);
    final statusOptions = deliveriesState.deliveriesStatusOptions;
    final deliveries = deliveriesState.deliveries;

    return CustomScrollView(
      physics: const NeverScrollableScrollPhysics(),
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Gap(20),
              Padding(
                  padding: const EdgeInsets.only(left: 40),
                  child: Text('Deliveries', style: textTheme.headlineMedium)),
              const Gap(20),
              Container(
                padding: const EdgeInsets.only(left: 40).copyWith(bottom: 12),
                decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Palette.stroke)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                        flex: 3,
                        child: Row(
                          children: statusOptions.map((status) {
                            int index = statusOptions.indexOf(status);
                            bool isSelected = activeIndex == index;

                            return Flexible(
                              child: StatusFilteringWidget(
                                status,
                                isSelected,
                                width: 130,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 19),
                                onPressed: () {
                                  if (!deliveriesState.deliveries.isLoading &&
                                      index != activeIndex) {
                                    setState(() {
                                      activeIndex = index;
                                    });
                                    handleDeliveryOption(index, locationId);
                                  }
                                },
                              ),
                            );
                          }).toList(),
                        ))
                  ],
                ),
              ),
              Visibility(
                visible: !deliveriesState.deliveries.isLoading ||
                    (deliveriesState.deliveries.hasValue &&
                        deliveriesState
                            .deliveries.value!.deliveryTrips.isNotEmpty),
                child: Skeletonizer(
                    enabled: deliveriesState.deliveries.isLoading,
                    child: _tableHeader()),
              ),
            ],
          ),
        ),
        deliveries.when(
          data: (data) {
            if (data == null || data.deliveryTrips.isEmpty) {
              return const SliverFillRemaining(
                  hasScrollBody: false, child: EmptyDeliveriesWidget());
            }
            return SliverFillRemaining(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: DeliveriesTableWidget(data.deliveryTrips),
                  ),
                  const Gap(10),
                  const DeliveriesPagination(),
                  const Gap(10),
                ],
              ),
            );
          },
          loading: () {
            return SliverSkeletonizer(
              enabled: true,
              child: SliverFillRemaining(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                        child: DeliveriesTableWidget(
                            List.filled(10, Deliveries.defaultValues()))),
                    const Gap(10),
                    const DeliveriesPagination(),
                    const Gap(10),
                  ],
                ),
              ),
            );
          },
          error: (error, stackTrace) {
            return SliverToBoxAdapter(
                child: FailureWidget(
              fullScreen: true,
              heightFactor: 0.7,
              e: error,
              retry: fetchDeliveries,
            ));
          },
        )
      ],
    );
  }

  Widget _tableHeader() {
    final textTheme = Theme.of(context).textTheme;
    return buildTableHeader(
        tableHeader: Table(
      columnWidths: const {
        0: FlexColumnWidth(0.2),
        1: FlexColumnWidth(.99),
        2: FlexColumnWidth(1.05),
        3: FlexColumnWidth(0.6),
        4: FlexColumnWidth(0.55),
        5: FlexColumnWidth(1),
        6: FlexColumnWidth(0.8),
      },
      children: [
        TableRow(
          children: [
            Container(),
            buildHeaderCell('Driver', textTheme),
            buildHeaderCell('Date', textTheme),
            buildHeaderCell('Visits', textTheme),
            buildHeaderCell('Quantity', textTheme),
            buildHeaderCell('Completion rate', textTheme),
            buildHeaderCell('Total', textTheme),
            buildHeaderCell('Status', textTheme),
            Container(), // Empty space for floating icon
            Container(), // Empty space for floating icon
          ],
        ),
      ],
    ));
  }

  handleDeliveryOption(int index, String locationId) {
    final status =
        ref.read(deliveriesControllerProvider).deliveriesStatusOptions[index];
    final params = switch (status.toLowerCase()) {
      'all' => FetchDeliveriesParam.defaultValue().copyWith(status: 'all'),
      'planned' =>
        FetchDeliveriesParam.defaultValue().copyWith(status: 'planned'),
      'started' =>
        FetchDeliveriesParam.defaultValue().copyWith(status: 'started'),
      'ended' => FetchDeliveriesParam.defaultValue().copyWith(status: 'ended'),
      _ => FetchDeliveriesParam.defaultValue()
          .copyWith(status: status.toLowerCase()),
    };

    final deliveryNotifier = ref.read(deliveriesControllerProvider.notifier);

    Future.microtask(() => deliveryNotifier.fetchDeliveries(
        params.copyWith(activeIndex: index, locationId: locationId),
        forced: true));
  }
}
