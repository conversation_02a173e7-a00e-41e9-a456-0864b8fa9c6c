import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:td_procurement/app/deliveries/domain/use_cases/deliveries_controller.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class DeliveriesPagination extends ConsumerWidget {
  final int? total;
  const DeliveriesPagination({super.key, this.total});

  @override
  Widget build(BuildContext context, ref) {
    final deliveriesState = ref.watch(deliveriesControllerProvider);
    final params = deliveriesState.fetchDeliveriesParam;
    final perPage = params.perPage;
    final start = ((params.currentPage - 1) * perPage) + 1;
    final end = params.currentPage * perPage;
    final totalCount =
        params.totalPages > 0 ? params.totalPages : params.currentPage;

    return Padding(
      padding: const EdgeInsets.only(left: 35, right: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$start - ${end > totalCount ? totalCount : end} of $totalCount',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: Palette.blackSecondary),
          ),
          // if (params.totalPages > 0)
          IntrinsicWidth(
            child: NumberPagination(
              onPageChanged: (int pageNumber) {
                ref.read(deliveriesControllerProvider.notifier).fetchDeliveries(
                    params.copyWith(currentPage: pageNumber),
                    forced: true);
              },
              totalPages: params.totalPages,
              currentPage: params.currentPage,
              visiblePagesCount: perPage > 5 || perPage == 0 ? 5 : perPage,
              buttonElevation: 0.5,
              buttonRadius: 6,
              selectedButtonColor: Palette.primaryBlack,
              selectedNumberColor: Colors.white,
              unSelectedButtonColor: Colors.white,
              unSelectedNumberColor: Palette.blackSecondary,
              fontSize: 14,
              numberButtonSize: const Size(35, 35),
              controlButtonSize: const Size(40, 40),
              firstPageIcon: SvgPicture.asset(kDoubleChevronLeftSvg,
                  width: 25, height: 25),
              previousPageIcon: SvgPicture.asset(kChevronLeftSvg,
                  colorFilter:
                      ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
                  width: 16,
                  height: 16),
              lastPageIcon: SvgPicture.asset(kDoubleChevronRightSvg,
                  width: 25, height: 25),
              nextPageIcon: SvgPicture.asset(kChevronRightSvg,
                  width: 16,
                  height: 16,
                  colorFilter: ColorFilter.mode(
                      Palette.blackSecondary, BlendMode.srcIn)),
            ),
          ),
        ],
      ),
    );
  }
}
