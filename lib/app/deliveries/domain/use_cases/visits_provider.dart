import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/visits.dart';

class VisitsProvider extends Notifier<Visit> {
  @override
  Visit build() => Visit();

  void setVisit(Visit visit) {
    state = visit;
  }

  void clearVisit() {
    state = Visit();
  }
}

final visitsProvider = NotifierProvider<VisitsProvider, Visit>(() {
  return VisitsProvider();
});
