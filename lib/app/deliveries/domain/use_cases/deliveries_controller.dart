import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/deliveries/data/sources/deliveries_service.dart';
import 'package:td_procurement/app/deliveries/domain/entities/deliveries_params.dart';
import 'package:td_procurement/app/deliveries/presentation/controllers/deliveries_state.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final deliveriesControllerProvider =
    NotifierProvider<DeliveriesController, DeliveriesState>(
        DeliveriesController.new);

class DeliveriesController extends Notifier<DeliveriesState> {
  late DeliveriesService _deliveriesService;

  @override
  DeliveriesState build() {
    _deliveriesService = ref.read(deliveriesServiceProvider);
    return DeliveriesState.initial();
  }

  Future<void> fetchDeliveries(
    FetchDeliveriesParam params, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    state = state.copyWith(
      fetchDeliveriesParam:
          state.fetchDeliveriesParam.copyWith(activeIndex: params.activeIndex),
    );

    if (forced) {
      state = state.copyWith(deliveries: const AsyncLoading());
    } else if (state.deliveries is AsyncData &&
            state.deliveries.valueOrNull != null ||
        state.fetchDeliveriesParam.currentPage ==
            state.fetchDeliveriesParam.totalPages) {
      return;
    } else {
      state = state.copyWith(deliveries: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _deliveriesService.fetchDeliveries(params);
      res.when(
        success: (data) {
          state = state.copyWith(
              deliveries: AsyncData(data),
              fetchDeliveriesParam: params.copyWith(
                  queryParams: data.pagination,
                  totalPages: data.pagination.totalPages,
                  currentPage: data.pagination.page));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
              deliveries: AsyncError(e, StackTrace.current),
            );
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<ApiResponse<List<Order>>> fetchDeliveryOrders(
    String deliveryId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;
    late ApiResponse<List<Order>> response;

    if (forced) {
      state = state.copyWith(deliveryOrders: const AsyncLoading());
    } else if (state.deliveryOrders is AsyncData &&
        state.deliveryOrders.valueOrNull != null) {
      return Success(state.deliveryOrders.value!);
    } else {
      state = state.copyWith(deliveryOrders: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      response = await _deliveriesService.fetchDeliveryOrders(deliveryId);

      response.when(
        success: (data) {
          state = state.copyWith(deliveryOrders: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                deliveryOrders: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }

    return response;
  }

  Future<void> fetchDeliveryDetails(
    String deliveryId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(deliveryDetails: const AsyncLoading());
    } else if (state.deliveryDetails is AsyncData &&
        state.deliveryDetails.valueOrNull != null) {
      return;
    } else {
      state = state.copyWith(deliveryDetails: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _deliveriesService.fetchDeliveryDetails(deliveryId);
      res.when(
        success: (data) {
          state = state.copyWith(deliveryDetails: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                deliveryDetails: AsyncError(e, StackTrace.current));
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<ApiResponse<void>> cancelDelivery(String deliveryId,
      {int retryCount = 0, int delaySeconds = 2, forced = false}) async {
    int attempts = 0;
    bool shouldRetry = true;
    late ApiResponse<void> response;

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      response = await _deliveriesService.cancelDelivery(deliveryId);

      response.when(
        success: (data) {
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }

    return response;
  }
}
