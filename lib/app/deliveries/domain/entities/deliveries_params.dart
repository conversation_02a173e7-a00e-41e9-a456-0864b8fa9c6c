import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_procurement/app/deliveries/data/models/deliveries.dart';
import 'package:td_procurement/core/models/index.dart';

class DeliveriesParams {
  final int limit;
  final DeliveriesStatus status;
  final String? query;

  DeliveriesParams(
      {this.limit = 10, this.status = DeliveriesStatus.all, this.query});

  DeliveriesParams paginate(int index) =>
      DeliveriesParams(limit: limit, status: status);

  DeliveriesParams switchStatus(DeliveriesStatus tab) =>
      DeliveriesParams(limit: limit, status: tab);

  DeliveriesParams querySearch(String text, DeliveriesStatus tab) =>
      DeliveriesParams(limit: limit, status: tab, query: text);

  Map<String, Object?> toMap() {
    return {
      'limit': limit,
      if (status != DeliveriesStatus.all) 'status': status.name,
      if (query != null && query != '') 'searchQuery': query,
    };
  }
}

class FetchDeliveriesParam {
  final String locationId;
  final String status;
  final String queryText;
  final QueryParameters queryParams;
  final int activeIndex;
  final int perPage;
  final int currentPage;
  final int totalPages;

  FetchDeliveriesParam({
    required this.status,
    required this.queryText,
    required this.locationId,
    this.queryParams = const QueryParameters(),
    this.perPage = 10,
    this.activeIndex = 0,
    this.currentPage = 0,
    this.totalPages = 1,
  });

  factory FetchDeliveriesParam.defaultValue() {
    return FetchDeliveriesParam(
        status: 'all',
        queryText: '',
        locationId: '',
        activeIndex: 0,
        perPage: 10,
        currentPage: 0,
        totalPages: 1);
  }

  FetchDeliveriesParam copyWith(
      {String? status,
      String? queryText,
      String? locationId,
      QueryParameters? queryParams,
      int? activeIndex,
      int? perPage,
      int? currentPage,
      int? totalPages}) {
    return FetchDeliveriesParam(
      status: status ?? this.status,
      locationId: locationId ?? this.locationId,
      queryText: queryText ?? this.queryText,
      queryParams: queryParams ?? this.queryParams,
      activeIndex: activeIndex ?? this.activeIndex,
      perPage: perPage ?? this.perPage,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'status': status,
      'queryText': queryText,
      'locationId': locationId,
      'queryParameter': queryParams.toString(),
      'activeIndex': activeIndex,
      'perPage': perPage,
      'currentPage': currentPage,
      'totalPages': totalPages,
    };
  }
}

class DeliveriesType extends Deliveries {
  DeliveriesType({
    required String id,
  }) : super(
          id: id,
        );
}
