import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/deliveries/data/models/deliveries.dart';
import 'package:td_procurement/app/deliveries/data/sources/deliveries_data_source.dart';
import 'package:td_procurement/app/deliveries/domain/entities/deliveries_params.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final deliveriesRepoProvider = Provider.autoDispose<DeliveriesRepo>((ref) {
  final dataSource = ref.read(deliveriesDataSourceProvider);
  return DeliveriesRepoImplementation(dataSource, ref);
});

abstract class DeliveriesRepo {
  Future<ApiResponse<DeliveriesResponse>> fetchDeliveries(
      FetchDeliveriesParam params);
  Future<ApiResponse<List<Order>>> fetchDeliveryOrders(String deliveryId);
  Future<ApiResponse<void>> cancelDelivery(String deliveryId);
  Future<ApiResponse<Deliveries>> fetchDeliveryDetails(String deliveryId);
}

class DeliveriesRepoImplementation implements DeliveriesRepo {
  final DeliveriesDataSource _deliveriesDataSource;
  final Ref _ref;

  DeliveriesRepoImplementation(this._deliveriesDataSource, this._ref);

  @override
  Future<ApiResponse<DeliveriesResponse>> fetchDeliveries(
      FetchDeliveriesParam params) async {
    return await dioInterceptor(
        () => _deliveriesDataSource.fetchDeliveries(params), _ref);
  }

  @override
  Future<ApiResponse<List<Order>>> fetchDeliveryOrders(
      String deliveryId) async {
    return await dioInterceptor(
        () => _deliveriesDataSource.fetchDeliveryOrders(deliveryId), _ref);
  }

  @override
  Future<ApiResponse<void>> cancelDelivery(String deliveryId) async {
    return await dioInterceptor(
        () => _deliveriesDataSource.cancelDelivery(deliveryId), _ref);
  }

  @override
  Future<ApiResponse<Deliveries>> fetchDeliveryDetails(
      String deliveryId) async {
    return await dioInterceptor(
        () => _deliveriesDataSource.fetchDeliveryDetails(deliveryId), _ref);
  }
}
