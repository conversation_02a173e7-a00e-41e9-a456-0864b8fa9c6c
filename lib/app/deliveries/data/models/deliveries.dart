import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_procurement/core/models/index.dart';

class DeliveriesResponse {
  final QueryParameters pagination;
  final List<Deliveries> deliveryTrips;

  const DeliveriesResponse({
    this.pagination = const QueryParameters(),
    this.deliveryTrips = const [],
  });

  factory DeliveriesResponse.fromJson(Map<String, dynamic> json) =>
      DeliveriesResponse(
        pagination: json['pagination'] != null
            ? QueryParameters.fromMap(json['pagination'])
            : const QueryParameters(),
        deliveryTrips: (json['delivery_trips'] != null &&
                json['delivery_trips'] is List &&
                json['delivery_trips'].isNotEmpty)
            ? List<Deliveries>.from(json['delivery_trips']
                .map((x) => Deliveries.fromMap(x).toMap()))
            : [],
      );
}

enum DeliveriesStatus {
  all('All', '#0679FF'),
  planned('Planned', '#0679FF'),
  started('Started', '#FF0606'),
  ended('Ended', '#FF0606');

  final String value;
  final String foregroundColor;
  const DeliveriesStatus(this.value, this.foregroundColor);
}
