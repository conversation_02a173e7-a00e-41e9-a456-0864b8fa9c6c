import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/deliveries/data/models/deliveries.dart';
import 'package:td_procurement/app/deliveries/domain/entities/deliveries_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/models/query_parameters.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

class DeliveriesDataSourceImplementation implements DeliveriesDataSource {
  final TdApiClient _apiClient;
  final AppConfig _config;
  DeliveriesDataSourceImplementation(this._apiClient, this._config);

  @override
  Future<DeliveriesResponse> fetchDeliveries(
      FetchDeliveriesParam params) async {
    final Response<dynamic> res;
    final filters = <String, dynamic>{};

    if (params.status != 'all') {
      filters['status'] = params.status;
    }
    if (params.currentPage > 0) {
      filters['page'] = params.currentPage.toString();
    }
    final queryString = Uri(queryParameters: filters).query;
    res = await _apiClient.get(
        '${_config.consoleUrl}${kFetchDeliveriesApiPath.replaceFirst(':locationId', params.locationId).replaceFirst(':queryString', queryString)}');

    final data = res.data['data'];
    final List<dynamic> deliveryTrips = data['deliveryTrips'];
    final Map<String, dynamic> paginationData = data['pagination'];
    final Map<String, dynamic> pagination = {
      'page': paginationData['page'],
      'totalPages': paginationData['total'],
      'perPage': paginationData['limit'],
    };
    return DeliveriesResponse(
      deliveryTrips: deliveryTrips.map((x) => Deliveries.fromMap(x)).toList(),
      pagination: QueryParameters.fromMap(pagination),
    );
  }

  @override
  Future<List<Order>> fetchDeliveryOrders(String deliveryId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kFetchDeliveryOrdersApiPath.replaceFirst(':deliveryId', deliveryId)}');
    final orders = res.data['data']['orders'] as List;
    return orders.map((x) => Order.fromMap(x)).toList();
  }

  @override
  Future<void> cancelDelivery(String deliveryId) async {
    await _apiClient.delete(
        '${_config.consoleUrl}${kDeliveryIDApiPath.replaceFirst(':deliveryId', deliveryId)}');
    return;
  }

  @override
  Future<Deliveries> fetchDeliveryDetails(String deliveryId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kDeliveryIDApiPath.replaceFirst(':deliveryId', deliveryId)}');
    print('res data ---- $res.data');
    return Deliveries.fromMap(res.data);
  }
}

abstract class DeliveriesDataSource {
  Future<DeliveriesResponse> fetchDeliveries(FetchDeliveriesParam params);
  Future<List<Order>> fetchDeliveryOrders(String deliveryId);
  Future<void> cancelDelivery(String deliveryId);
  Future<Deliveries> fetchDeliveryDetails(String deliveryId);
}

final deliveriesDataSourceProvider = Provider<DeliveriesDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  final config = ref.read(appConfigProvider);
  return DeliveriesDataSourceImplementation(apiClient, config);
});
