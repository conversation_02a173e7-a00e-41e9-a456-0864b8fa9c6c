class SendOTParams {
  const SendOTParams({
    required this.mode,
    this.phoneNumber,
    required this.url,
    this.email,
    this.domain = SendOtpDomain.tradedepot,
  }) : assert((mode == PhoneAuthMode.Email && email != null) ||
      (mode != PhoneAuthMode.Email && phoneNumber != null));

  final PhoneAuthMode mode;
  final String? phoneNumber;
  final String url;
  final String? email;
  final SendOtpDomain? domain;

  Map<String, String?> toMap() {
    if (mode == PhoneAuthMode.Email) {
      assert(email != null, 'Email cannot be null when PhoneAuthMode is email');
      return {
        'mode': mode.name,
        'email': email,
        if (domain != null) 'domain': domain?.name,
        'url': url
      };
    }

    assert(phoneNumber != null,
    'PhoneNumber cannot be null for SMS or WhatsApp mode');

    return {
      'mode': mode.name,
      'phoneNumber': phoneNumber,
      if (domain != null) 'domain': domain?.name,
      if (email != null) 'email': email,
      'url': url
    };
  }

  factory SendOTParams.fromMap(Map<String, dynamic> map) {
    return SendOTParams(
        mode: PhoneAuthMode.values.byName(map['mode']),
        email: map['email'],
        phoneNumber: map['phoneNumber'],
        domain: map['domain'] != null
            ? SendOtpDomain.values.byName(map['domain'])
            : null,
        url: map['url']);
  }
}

enum SendOtpDomain { shoptopup, tradedepot }
enum PhoneAuthMode { SMS, WhatsApp, Email, All }