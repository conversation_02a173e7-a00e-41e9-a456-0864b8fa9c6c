class LoginTokenParams {
  final String? phone;
  final String? email;
  final String token;

  LoginTokenParams({required this.token, this.phone, this.email}) {
    assert(
    email != null || phone != null, 'Both email and phone cannot be null.');
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {
      'accessToken': token,
    };

    if (email != null) {
      data['email'] = email;
    }

    if (phone != null) {
      data['phoneNumber'] = phone;
    }

    return data;
  }
}