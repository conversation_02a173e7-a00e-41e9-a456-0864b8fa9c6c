import 'package:td_commons_flutter/models/user.dart';

/// Abstract class for handling post-login events.
abstract class OnLogin {
  Future<void> onLogin(User user);
}

/// Abstract class for handling pre-login events.
abstract class OnBeforeLogin {
  Future<void> onBeforeLogin();
}

/// Abstract class for handling post-logout events.
abstract class OnLogout {
  Future<void> onLogout();
}

/// Abstract class for handling pre-logout events.
abstract class OnBeforeLogout {
  Future<void> onBeforeLogout();
}

/// Abstract class for handling user data change events.
abstract class OnUserChanged {
  Future<void> onUserChanged(User user);
}
