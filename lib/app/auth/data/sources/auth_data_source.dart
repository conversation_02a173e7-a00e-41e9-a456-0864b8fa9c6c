import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/auth/data/models/outlet_type.dart';
import 'package:td_procurement/app/auth/data/models/user_status.dart';
import 'package:td_procurement/app/auth/data/models/verified_otp.dart';
import 'package:td_procurement/app/auth/domain/entities/check_phone.dart';
import 'package:td_procurement/app/auth/domain/entities/login_token.dart';
import 'package:td_procurement/app/auth/domain/entities/partner_reg.dart';
import 'package:td_procurement/app/auth/domain/entities/send_otp.dart';
import 'package:td_procurement/app/auth/domain/entities/verify_otp.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

abstract class AuthDataSource {
  Future<User> loginWithOtp(LoginTokenParams params);
  Future<UserStatus> verifyEmailAddress(CheckPhoneParams params);
  Future<Map<String, dynamic>?> sendOTP(SendOTParams params);
  Future<VerifiedOTP> verifyOTP(VerifyOTParams params);
  Future<List<RetailOutletType>> fetchOutletTypes(String countryIso);
  Future<bool> signUp(Map params);
  Future<bool> registerPartner(PartnerRegParams params);
  Future<RetailOutlet> getRetailOutlet(User user);
  Future<bool> checkLoanContractStatus();
  Future<bool> deleteUserAccount();
}

final authDataProvider = Provider<AuthDataSource>((ref) {
  return AuthDataSourceImplementation(ref);
});

class AuthDataSourceImplementation extends AuthDataSource {
  final Ref _ref;
  AuthDataSourceImplementation(this._ref);

  late final TdApiClient _apiClient = _ref.read(apiClientProvider);
  late final config = _ref.read(appConfigProvider);

  @override
  Future<User> loginWithOtp(LoginTokenParams params) async {
    final res = await _apiClient.post(
      "${config.firebaseServiceUrl}$kLoginApiPath",
      data: params.toMap(),
    );

    return User.fromMap(res.data);
  }

  @override
  Future<UserStatus> verifyEmailAddress(CheckPhoneParams params) async {
    final res = await _apiClient.get(
      "${config.firebaseServiceUrl}$kCheckPhoneApiPath",
      queryParameters: params.toMap(),
    );
    return UserStatus.fromMap(res.data['data']['data']);
  }

  @override
  Future<VerifiedOTP> verifyOTP(VerifyOTParams params) async {
    final res = await _apiClient.post(
      params.url,
      data: params.toMap(),
    );
    return VerifiedOTP.fromMap(res.data['res']);
  }

  @override
  Future<Map<String, dynamic>?> sendOTP(SendOTParams params) async {
    final res = await _apiClient.post(
      params.url,
      data: params.toMap(),
    );
    return res.data;
  }

  @override
  Future<List<RetailOutletType>> fetchOutletTypes(String countryIso) async {
    final response = await _apiClient
        .get(kFetchOutletApiPath, queryParameters: {'country': countryIso});
    return (response.data['data'] as List<dynamic>)
        .map((e) => RetailOutletType.fromMap(e))
        .toList();
  }

  @override
  Future<bool> signUp(Map params) async {
    await _apiClient.post('${config.consoleUrl}$kCreateApiPath', data: {
      'extChannel': 'SHOP.WEB',
      'businessStatus': '',
      'registrationNumber': '',
      'companyBankAccountName': '',
      'companyBankAccountNumber': '',
      'companyAccountSignatoryBvn': '',
      'images': [],
      ...params,
    });
    return true;
  }

  @override
  Future<bool> registerPartner(PartnerRegParams params) async {
    try {
      final response = await _apiClient
          .post('${config.awsApiUrlV4}$kRegisterPartnerApiPath', data: {
        "path": "crm/v3/objects/contacts",
        "method": "POST",
        "params": {
          "properties": {
            "company": params.companyParams['name'],
            ...params.partnerParams
          }
        }
      }, headers: {
        'x-api-key': config.hubspotApiKey
      });
      await _apiClient
          .post('${config.awsApiUrlV4}$kRegisterPartnerApiPath', data: {
        "path": "crm/v3/objects/companies",
        "method": "POST",
        "params": {
          "properties": params.companyParams,
          "associations": [
            {
              "to": {"id": response.data['id']},
              "types": [
                {
                  "associationCategory": "HUBSPOT_DEFINED",
                  "associationTypeId": 280
                }
              ]
            }
          ]
        }
      }, headers: {
        'x-api-key': config.hubspotApiKey
      });
      return true;
    } on DioException catch (error) {
      final message = ((error.response?.data as Map?)?['category'] as String?)
          ?.replaceAll('_', ' ');
      if (message != null) {
        throw DioException(
          requestOptions: RequestOptions(),
          response: Response(
            requestOptions: RequestOptions(),
            data: {'message': message},
          ),
        );
      }
      rethrow;
    } catch (_) {
      rethrow;
    }
  }

  @override
  Future<RetailOutlet> getRetailOutlet(User user) async {
    final res = await _apiClient.get(
        "${config.appUrl}/api/v4/procurement/get-retail-outlet?outletId=${user.currentRetailOutlet?.id}",
        headers: {'x-api-key': user.apiKey});

    return RetailOutlet.fromMap(res.data['data']);
  }

  @override
  Future<bool> deleteUserAccount() async {
    await _apiClient.post('${config.consoleUrl}$kDeleteAccountApiPath');
    return true;
  }

  @override
  Future<bool> checkLoanContractStatus() async {
    final res = await _apiClient
        .post("${config.consoleUrl}/api/v3/invoice-discounts/can-apply");

    if (res.statusCode == 200) {
      return true;
    }
    return false;
  }
}
