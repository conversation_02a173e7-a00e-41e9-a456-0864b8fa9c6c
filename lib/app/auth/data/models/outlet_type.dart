import 'package:equatable/equatable.dart';

class RetailOutletType extends Equatable{
  final String? id;
  final String? name;
  final String? domain;
  final bool documentRequired;
  final String? salesChannel;

  const RetailOutletType({
    this.id,
    this.name,
    this.domain,
    this.documentRequired = false,
    this.salesChannel,
  }) : super();

  factory RetailOutletType.fromMap(Map<String, dynamic> map) {
    return RetailOutletType(
      id: map['_id'],
      name: map['name'],
      domain: map['domain'],
      documentRequired: map['documentRequired'],
      salesChannel: map['salesChannel'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'domain': domain,
      'documentRequired': documentRequired,
      'salesChannel': salesChannel,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }

  @override
  List<Object?> get props => [id];
}
