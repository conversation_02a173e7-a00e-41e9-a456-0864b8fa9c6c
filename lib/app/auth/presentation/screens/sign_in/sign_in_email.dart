import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';

class SignInEmailView extends StatefulWidget {
  final TextEditingController controller;
  final ValueNotifier<bool> loader;
  final VoidCallback onPressed;
  final ValueNotifier<bool> isButtonDisabled;

  const SignInEmailView({
    super.key,
    required this.controller,
    required this.loader,
    required this.onPressed,
    required this.isButtonDisabled,
  });

  @override
  State<StatefulWidget> createState() {
    return _SignInEmailView();
  }
}

class _SignInEmailView extends State<SignInEmailView> {
  @override
  void initState() {
    widget.controller.addListener(() {
      widget.isButtonDisabled.value = widget.controller.text.isEmpty;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Sign in to your account",
          style: theme.textTheme.headlineMedium
              ?.copyWith(fontWeight: FontWeight.w800),
        ),
        const Gap(12),
        TextField(
          decoration: const InputDecoration(hintText: 'Email address'),
          controller: widget.controller,
          keyboardType: TextInputType.emailAddress,
          style: const TextStyle(fontSize: 14),
          onSubmitted: (_) => widget.onPressed(),
        ),
        const Gap(12),
        CustomFilledButton(
          onPressed: widget.onPressed,
          text: 'Continue',
          loaderNotifier: widget.loader,
          disabledNotifier: widget.isButtonDisabled,
        ),
      ],
    );
  }
}
