import 'dart:async';
import 'dart:ui' as ui show PlaceholderAlignment;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class VerificationCodeView extends StatefulWidget {
  final TextEditingController controller;
  final AuthRoute route;
  final ValueNotifier<bool> loader;
  final VoidCallback onPop;
  final VoidCallback onPressed;
  final Function resendOtp;
  final ValueNotifier<bool> isButtonDisabled;

  const VerificationCodeView(
      {super.key,
      required this.controller,
      required this.loader,
      required this.onPop,
      required this.onPressed,
      required this.resendOtp,
      required this.route,
      required this.isButtonDisabled});

  @override
  State<StatefulWidget> createState() {
    return _VerificationCodeView();
  }
}

class _VerificationCodeView extends State<VerificationCodeView> {
  ValueNotifier<int> timerNotifier = ValueNotifier(60);

  @override
  void initState() {
    initTimer();
    widget.controller.addListener(() {
      widget.isButtonDisabled.value = widget.controller.text.isEmpty;
    });
    super.initState();
  }

  @override
  void dispose() {
    timerNotifier.dispose();
    super.dispose();
  }

  void initTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        timerNotifier.value = --timerNotifier.value;
        if (timer.tick > 60) {
          timer.cancel();
        }
      }
    });
  }

  String _extractTime(int seconds) {
    if (seconds >= 60) {
      return '60s';
    }
    if (seconds < 10) {
      return '0${seconds}s';
    }
    if (seconds < 0) {
      return '0s';
    }
    return '${seconds}s';
  }

  void resendOtp() async {
    widget.controller.text = '';
    timerNotifier.value = 100;
    final bool res = await widget.resendOtp();
    if (res) {
      timerNotifier.value = 60;
      initTimer();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SizedBox(
      width: 380, // tell
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Confirm ${switch (widget.route) {
              AuthRoute.signIn => 'Log in',
              AuthRoute.signUp => 'Sign Up',
            }}",
            style: theme.textTheme.headlineMedium,
          ),
          Text(
            'Enter the verification code we sent to your email address',
            style: theme.textTheme.bodyLarge
                ?.copyWith(color: Palette.blackSecondary),
          ),
          const Gap(12),
          TextField(
            decoration: InputDecoration(
              hintText: 'Enter verification code',
              suffixIcon: ValueListenableBuilder<int>(
                  valueListenable: timerNotifier,
                  builder: (context, time, _) {
                    if (time <= 0) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 5),
                        child: TextButton(
                          onPressed: resendOtp,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.all(5),
                          ),
                          child: const Text("Resend"),
                        ),
                      );
                    } else {
                      return Text(
                        time >= 100
                            ? 'Resending code...'
                            : "Resend in ${_extractTime(time)}",
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Palette.primary,
                        ),
                        textAlign: TextAlign.center,
                      );
                    }
                  }),
              suffixIconConstraints: const BoxConstraints.tightFor(width: 100),
            ),
            controller: widget.controller,
            keyboardType: TextInputType.number,
            inputFormatters: [
              LengthLimitingTextInputFormatter(6),
              FilteringTextInputFormatter.digitsOnly
            ],
            style: const TextStyle(fontSize: 14),
            onSubmitted: (_) => widget.onPressed(),
          ),
          const Gap(12),
          CustomFilledButton(
            onPressed: widget.onPressed,
            text: 'Continue',
            loaderNotifier: widget.loader,
            disabledNotifier: widget.isButtonDisabled,
          ),
          const Gap(10),
          Text.rich(
            TextSpan(
              text:
                  "Still haven’t received a code? Check your spam folder, or ",
              style: theme.textTheme.bodyMedium
                  ?.copyWith(color: Palette.blackSecondary),
              children: [
                WidgetSpan(
                  child: InkWell(
                    onTap: widget.onPop,
                    child: Text(
                      "try a different email.",
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Palette.primary,
                      ),
                    ),
                  ),
                  baseline: TextBaseline.alphabetic,
                  alignment: ui.PlaceholderAlignment.baseline,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

enum AuthRoute { signIn, signUp }
