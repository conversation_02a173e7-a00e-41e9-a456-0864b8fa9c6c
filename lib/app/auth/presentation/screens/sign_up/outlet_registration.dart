import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/auth/data/models/outlet_type.dart';
import 'package:td_procurement/app/auth/domain/use_cases/auth_use_cases.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/google_places/address_autocomplete_widgets.dart';
import 'package:td_procurement/core/services/google_places/model/suggestion.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/app_values/app_values.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class OutletRegistration extends ConsumerStatefulWidget {
  final OutletParams? params;
  final ValueChanged<OutletParams> onNext;
  const OutletRegistration(this.onNext, this.params, {super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _OutletRegistration();
  }
}

class _OutletRegistration extends ConsumerState<OutletRegistration> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ValueNotifier<AutovalidateMode> _validateMode =
      ValueNotifier(AutovalidateMode.disabled);
  late final ValueNotifier<bool> _postCodeNotifier = ValueNotifier(
      widget.params?.address?.countryShort == Country.britain.slug);
  late final OutletParams params = widget.params ?? OutletParams();
  late final googleKey = ref.read(appConfigProvider).googleApiKey;
  late final _typeController = TextEditingController(text: params.type?.name);
  late final _postCodeController = TextEditingController(text: params.postCode);
  late final _addressController =
      TextEditingController(text: params.address?.address);

  @override
  void dispose() {
    _validateMode.dispose();
    _typeController.dispose();
    _postCodeController.dispose();
    _postCodeNotifier.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: SizedBox(
        width: 380,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sign up as a Retailer',
              style: theme.textTheme.headlineMedium,
            ),
            const Gap(10),
            ValueListenableBuilder<AutovalidateMode>(
              valueListenable: _validateMode,
              builder: (context, mode, _) => Form(
                key: _formKey,
                autovalidateMode: mode,
                child: Column(
                  children: [
                    TextFormField(
                      decoration:
                          const InputDecoration(hintText: 'Company name'),
                      initialValue: params.name,
                      keyboardType: TextInputType.name,
                      validator: (input) => Validators.verifyInput(input,
                          field: 'Company name', length: 3),
                      onSaved: (value) => params.setName = value?.trim(),
                    ),
                    const Gap(10),
                    AddressAutocompleteTextFormField(
                      mapsApiKey: googleKey,
                      showGoogleTradeMark: false,
                      debounceTime: 200,
                      requiredField: true,
                      controller: _addressController,
                      //onChanged: (_)=> params.setAddress=null,
                      // onClearClick: ()=> params.setAddress = null,
                      /*    initialValue: params.address?.streetAddress ??
                          params.address?.streetShort ??
                          params.address?.formattedAddress,*/
                      reportValidationFailAndRequestFocus: (_) => true,
                      onInitialSuggestionClick: (suggestion) {},
                      onSuggestionClickGetTextToUseForControl: (place) =>
                          place.address,
                      onSuggestionClick: (place) {
                        if (params.address?.countryShort !=
                            place.countryShort) {
                          params.setAddress = place;
                          _typeController.text = '';
                          ref.invalidate(fetchOutletTypesProvider);
                        } else {
                          params.setAddress = place;
                        }

                        _postCodeController.text = place.zipCode ?? '';
                        _postCodeNotifier.value =
                            place.countryShort == Country.britain.slug;
                      },
                      hoverColor:
                          Palette.kE7E7E7, // for desktop platforms with mouse
                      selectionColor:
                          Palette.primary, // for desktop platforms with mouse
                      buildItem: (Suggestion suggestion, int index) {
                        return Container(
                          margin: const EdgeInsets.fromLTRB(2, 2, 2,
                              2), //<<This area will get hoverColor/selectionColor on desktop
                          padding: const EdgeInsets.all(8),
                          alignment: Alignment.centerLeft,
                          color: Colors.white,
                          child: Text(suggestion.description,
                              style: theme.textTheme.bodyMedium),
                        );
                      },
                      clearButton: Icon(
                        Icons.close,
                        size: 15,
                        color: Palette.primaryBlack,
                      ),
                      language: 'en-Us',
                      scrollPadding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      keyboardType: TextInputType.streetAddress,
                      textCapitalization: TextCapitalization.words,
                      textInputAction: TextInputAction.next,
                      decoration:
                          const InputDecoration(hintText: 'Company address'),
                    ),
                    const Gap(10),
                    ValueListenableBuilder<bool>(
                      valueListenable: _postCodeNotifier,
                      builder: (context, state, _) => state
                          ? Column(
                              children: [
                                TextFormField(
                                  decoration: const InputDecoration(
                                      hintText: 'Post code'),
                                  controller: _postCodeController,
                                  inputFormatters: [Validators.validInput()],
                                  keyboardType: TextInputType.name,
                                  validator: (input) => Validators.verifyInput(
                                      input,
                                      field: 'Post code',
                                      length: 5),
                                  onSaved: (value) =>
                                      params.setPostCode = value?.trim(),
                                ),
                                const Gap(10),
                              ],
                            )
                          : const SizedBox.shrink(),
                    ),
                    Consumer(
                      builder: (context, ref, _) {
                        ref.listen(
                          fetchOutletTypesProvider(
                              params.address?.countryShort),
                          (prev, next) {
                            if (next.hasError) {
                              _typeController.text = '';
                              Toast.apiError(
                                next.error as AppException,
                                context,
                              );
                            }
                          },
                        );
                        final state = ref.watch(fetchOutletTypesProvider(
                            params.address?.countryShort));
                        final isEnabled = params.address?.countryShort != null;
                        return DropdownMenu<RetailOutletType>(
                          trailingIcon: state.maybeWhen(
                            orElse: () => SvgPicture.asset(
                              kChevronDownSvg,
                              fit: BoxFit.cover,
                            ),
                            error: (_, __) => InkWell(
                              onTap: () =>
                                  ref.invalidate(fetchOutletTypesProvider),
                              hoverColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Padding(
                                padding: const EdgeInsets.all(2),
                                child: Text(
                                  'Retry',
                                  style: theme.textTheme.bodyMedium
                                      ?.copyWith(color: Palette.primary),
                                ),
                              ),
                            ),
                          ),
                          controller: _typeController,
                          enabled: isEnabled,
                          errorText: state.maybeWhen(
                            orElse: () => mode == AutovalidateMode.always &&
                                    _typeController.text.isEmpty
                                ? 'Type of business is required'
                                : null,
                            error: (_, __) => 'Something Went Wrong',
                          ),
                          hintText: state.maybeWhen(
                              orElse: () => 'Type of business',
                              loading: () => 'Loading...'),
                          menuHeight: 300,
                          width: 380,
                          textStyle: theme.textTheme.bodyMedium,
                          enableSearch: false,
                          requestFocusOnTap: false,
                          selectedTrailingIcon: state.maybeWhen(
                            orElse: () => SvgPicture.asset(
                              kChevronDownSvg,
                              fit: BoxFit.cover,
                            ),
                            error: (_, __) => InkWell(
                              onTap: () =>
                                  ref.invalidate(fetchOutletTypesProvider),
                              hoverColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Padding(
                                padding: const EdgeInsets.all(2),
                                child: Text(
                                  'Retry',
                                  style: theme.textTheme.bodyMedium
                                      ?.copyWith(color: Palette.primary),
                                ),
                              ),
                            ),
                          ),
                          inputDecorationTheme: InputDecorationTheme(
                            fillColor: Palette.kE7E7E7,
                            filled: !isEnabled,
                            constraints: BoxConstraints.tight(
                              Size.fromHeight(
                                state.maybeWhen(
                                  orElse: () =>
                                      mode == AutovalidateMode.always &&
                                              _typeController.text.isEmpty
                                          ? 74
                                          : 44,
                                  error: (_, __) => 74,
                                ),
                              ),
                            ),
                          ),
                          onSelected: (value) {
                            params.setType = value;
                            if (mode == AutovalidateMode.always) {
                              ref.invalidate(fetchOutletTypesProvider);
                            }
                          },
                          dropdownMenuEntries: state.maybeWhen(
                            orElse: () => [],
                            data: (types) => types
                                .map<DropdownMenuEntry<RetailOutletType>>(
                                  (option) =>
                                      DropdownMenuEntry<RetailOutletType>(
                                    value: option,
                                    label: option.name ?? 'type',
                                    style: MenuItemButton.styleFrom(
                                        foregroundColor: Palette.primaryBlack),
                                  ),
                                )
                                .toList(),
                          ),
                        );
                      },
                    )
                  ],
                ),
              ),
            ),
            const Gap(12),
            CustomFilledButton(
              onPressed: handleSubmitted,
              text: 'Continue',
              loaderNotifier: ValueNotifier(false),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> handleSubmitted() async {
    final FormState form = _formKey.currentState!;
    if (params.address != null &&
        params.address?.address != _addressController.text) {
      Toast.error("Kindly select a valid address", context);
    } else if (form.validate() && _typeController.text.isNotEmpty) {
      form.save();
      widget.onNext(params);
    } else {
      _validateMode.value = AutovalidateMode.always;
      Toast.error("Kindly attend to the error(s) below", context);
    }
  }

  void reset(FormState form) {
    form.reset();
  }
}

class OutletParams {
  OutletParams();
  String? _name;
  Place? _address;
  String? _postCode;
  RetailOutletType? _type;

  RetailOutletType? get type => _type;
  String? get name => _name;
  Place? get address => _address;
  String? get postCode => _postCode;

  set setName(names) => _name = names;
  set setPostCode(code) => _postCode = code;
  set setType(type) => _type = type;
  set setAddress(Place? address) => _address = address;

  toMap() {
    return {
      'outletBusinessName': name,
      'streetName': address?.formattedAddress ?? address?.streetAddress,
      'state': address?.state,
      'latitude': address?.lat,
      'lga': address?.county,
      'longitude': address?.lng,
      'outletTypeId': type?.id,
      'postCode': _postCode ?? address?.zipCode,
      'domain': address?.countryShort == Country.britain.slug
          ? 'tradedepot'
          : 'shoptopup',
      'country': address?.countryShort
    };
  }
}

final fetchOutletTypesProvider =
    FutureProviderFamily<List<RetailOutletType>, String?>(
        (ref, arg) async => arg == null
            ? []
            : (await ref.read(
                fetchOutletsUseCaseProvider(arg),
              ))
                .extract());
