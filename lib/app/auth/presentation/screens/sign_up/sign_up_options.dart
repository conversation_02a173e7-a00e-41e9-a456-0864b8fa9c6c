import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class SignUpOptions extends StatelessWidget {
  final ValueChanged<SignUpOption> onNext;
  const SignUpOptions({super.key, required this.onNext});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SizedBox(
      width: 585,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("How will you use TradeDepot?",
              style: theme.textTheme.headlineMedium),
          Text(
            'Select an option below',
            style: theme.textTheme.bodyLarge
                ?.copyWith(color: Palette.blackSecondary),
          ),
          ...options.map(
            (option) => SignUpOptionTile(
              title: option.title,
              subTitle: option.subTitle,
              icon: option.icon,
              onNext: () => onNext(option.option),
            ),
          )
        ],
      ),
    );
  }
}

class SignUpOptionTile extends StatelessWidget {
  final String title;
  final String subTitle;
  final String icon;
  final VoidCallback onNext;

  const SignUpOptionTile(
      {super.key,
      required this.title,
      required this.subTitle,
      required this.icon,
      required this.onNext});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      margin: const EdgeInsets.only(top: 20),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Palette.kE7E7E7),
          boxShadow: const [
            BoxShadow(
              color: Palette.k0000000A,
              blurRadius: 2,
              spreadRadius: -1,
              offset: Offset(0, 2),
            )
          ],
          color: Palette.kFCFCFC),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(
            icon,
            width: 20,
            height: 20,
            colorFilter:
                ColorFilter.mode(Palette.primaryBlack, BlendMode.srcIn),
          ),
          const Gap(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.headlineSmall,
                ),
                Text(
                  subTitle,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Palette.blackSecondary,
                  ),
                ),
                const Gap(20),
                Align(
                  alignment: Alignment.centerRight,
                  child: FilledButton(
                    style: FilledButton.styleFrom(
                      minimumSize: const Size(92, 32),
                      fixedSize: const Size(92, 32),
                    ),
                    onPressed: onNext,
                    child: const Text('Next'),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

class SignUpOptionModel {
  final String title;
  final String subTitle;
  final String icon;
  final SignUpOption option;

  const SignUpOptionModel(this.title, this.subTitle, this.icon, this.option);
}

const options = [
  SignUpOptionModel(
      "I’m a Retailer",
      "Explore our curated selection of products sourced from manufacturers worldwide. Whether you’re a store owner or a reseller, TradeDepot offers direct access to top-quality goods across various categories.",
      kBuildingStoreSvg,
      SignUpOption.retailer),
  SignUpOptionModel(
      'I’m a Distribution Partner',
      'Partner with TradeDepot to expand your business. Join our global network of importers and distributors to efficiently bring quality products into new markets.',
      kUserCircleSvg,
      SignUpOption.partner)
];

enum SignUpOption { retailer, partner }
