import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/google_places/address_autocomplete_widgets.dart';
import 'package:td_procurement/core/services/google_places/model/suggestion.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class CompanyRegistration extends ConsumerStatefulWidget {
  final CompanyParams? params;
  final ValueChanged<CompanyParams> onNext;
  const CompanyRegistration(this.onNext, this.params, {super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _CompanyRegistration();
  }
}

class _CompanyRegistration extends ConsumerState<CompanyRegistration> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ValueNotifier<AutovalidateMode> _validateMode =
      ValueNotifier(AutovalidateMode.disabled);
  late final params = widget.params ?? CompanyParams();
  late final googleKey = ref.read(appConfigProvider).googleApiKey;
  late final _addressController = TextEditingController(text:params.address?.address);

  @override
  void dispose() {
    _validateMode.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: SizedBox(
        width: 380,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sign up as a Partner',
              style: theme.textTheme.headlineMedium,
            ),
            Text(
              'Fill in your company information',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(color: Palette.blackSecondary),
            ),
            const Gap(20),
            ValueListenableBuilder<AutovalidateMode>(
              valueListenable: _validateMode,
              builder: (context, mode, _) => Form(
                key: _formKey,
                autovalidateMode: mode,
                child: Column(
                  children: [
                    TextFormField(
                      decoration:
                          const InputDecoration(hintText: 'Company name'),
                      initialValue: params.name,
                      keyboardType: TextInputType.name,
                      validator: (input) => Validators.verifyInput(input,
                          field: 'Company name', length: 3),
                      onSaved: (value) => params.name = value?.trim(),
                    ),
                    const Gap(10),
                    AddressAutocompleteTextFormField(
                      mapsApiKey: googleKey,
                      debounceTime: 200,
                      requiredField: true,
                      controller: _addressController,
                      reportValidationFailAndRequestFocus: (_) => true,
                      onInitialSuggestionClick: (suggestion) {},
                      onSuggestionClickGetTextToUseForControl: (place) =>
                          place.address,
                      onSuggestionClick: (place) => params.setAddress = place,
                      hoverColor:
                          Palette.kE7E7E7, // for desktop platforms with mouse
                      selectionColor:
                          Palette.primary, // for desktop platforms with mouse
                      buildItem:
                          (Suggestion suggestion, int index) {
                        return Container(
                          margin: const EdgeInsets.fromLTRB(2, 2, 2,
                              2), //<<This area will get hoverColor/selectionColor on desktop
                          padding: const EdgeInsets.all(8),
                          alignment: Alignment.centerLeft,
                          color: Colors.white,
                          child: Text(suggestion.description,
                              style: theme.textTheme.bodyMedium),
                        );
                      },
                      clearButton: Icon(
                        Icons.close,
                        size: 15,
                        color: Palette.primaryBlack,
                      ),
                      language: 'en-Us',
                      scrollPadding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      keyboardType: TextInputType.streetAddress,
                      textCapitalization: TextCapitalization.words,
                      textInputAction: TextInputAction.next,
                      decoration:
                          const InputDecoration(hintText: 'Company address'),
                    ),
                  ],
                ),
              ),
            ),
            const Gap(12),
            CustomFilledButton(
              onPressed: handleSubmitted,
              text: 'Continue',
              loaderNotifier: ValueNotifier(false),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> handleSubmitted() async {
    final FormState form = _formKey.currentState!;
    if(params.address != null &&  params.address?.address != _addressController.text){
      Toast.error("Kindly select a valid address", context);
    }
    else if (form.validate()) {
      form.save();
      widget.onNext(params);
    } else {
      _validateMode.value = AutovalidateMode.always;
      Toast.error("Kindly attend to the error(s) below", context);
    }
  }

  void reset(FormState form) {
    form.reset();
  }
}

class CompanyParams {
  CompanyParams();
  String? name;
  Place? address;

  set setName(names) => name = names;
  set setAddress(Place address) => this.address = address;

  toMap() {
    return {
      'name': name,
      'country': address?.country,
      'city': address?.city,
      'address': address?.formattedAddress ?? address?.streetAddress,
      'state': address?.state,
      'zip': address?.zipCode
    };
  }
}
