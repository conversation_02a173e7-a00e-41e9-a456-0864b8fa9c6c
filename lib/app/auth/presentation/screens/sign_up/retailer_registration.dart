import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/auth/domain/entities/send_otp.dart';
import 'package:td_procurement/app/auth/domain/use_cases/auth_use_cases.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class RetailerRegistration extends ConsumerStatefulWidget {
  final ValueChanged<RetailerParams> onNext;
  final ValueChanged<RetailerParams> onValueChanged;
  final String? countryCode;
  final RetailerParams? params;
  const RetailerRegistration(
      this.onNext, this.params, this.countryCode, this.onValueChanged,
      {super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _RetailerRegistration();
  }
}

class _RetailerRegistration extends ConsumerState<RetailerRegistration> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ValueNotifier<bool> _loader = ValueNotifier(false);
  final ValueNotifier<AutovalidateMode> _validateMode =
      ValueNotifier(AutovalidateMode.disabled);
  late final params = widget.params ?? RetailerParams();

  @override
  void dispose() {
    _validateMode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: SizedBox(
        width: 410,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sign up as a Retailer',
              style: theme.textTheme.headlineMedium,
            ),
            Text(
              'Fill in your contact information',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(color: Palette.blackSecondary),
            ),
            const Gap(20),
            ValueListenableBuilder<AutovalidateMode>(
              valueListenable: _validateMode,
              builder: (context, mode, _) => Form(
                key: _formKey,
                autovalidateMode: mode,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            decoration:
                                const InputDecoration(hintText: 'First Name'),
                            keyboardType: TextInputType.name,
                            validator: (input) => Validators.verifyInput(input,
                                field: 'first name', length: 2),
                            onSaved: (value) => params.setFName = value?.trim(),
                            initialValue: params.fName,
                            onChanged: (value) {
                              params.setFName = value.trim();
                              widget.onValueChanged(params);
                            },
                          ),
                        ),
                        const Gap(10),
                        Expanded(
                          child: TextFormField(
                            decoration:
                                const InputDecoration(hintText: 'Last Name'),
                            keyboardType: TextInputType.name,
                            validator: (input) => Validators.verifyInput(input,
                                field: 'Last name', length: 2),
                            onSaved: (value) => params.setLName = value?.trim(),
                            initialValue: params.lName,
                            onChanged: (value) {
                              params.setLName = value.trim();
                              widget.onValueChanged(params);
                            },
                          ),
                        ),
                      ],
                    ),
                    const Gap(10),
                    TextFormField(
                      decoration: const InputDecoration(
                          hintText: 'Contact phone number'),
                      initialValue: params.number,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        Validators.validNumberInput(),
                      ],
                      validator: (input) => Validators.verifyInput(input,
                          field: 'Contact phone number', length: 1),
                      onSaved: (value) => params.setNumber = value!.trim(),
                      onChanged: (value) {
                        params.setNumber = value.trim();
                        widget.onValueChanged(params);
                      },
                    ),
                    const Gap(10),
                    TextFormField(
                      decoration: const InputDecoration(
                          hintText: 'Company email address'),
                      keyboardType: TextInputType.emailAddress,
                      initialValue: params.email,
                      validator: Validators.verifyEmail,
                      onSaved: (value) => params.setEmail = value!.trim(),
                      onChanged: (value) {
                        params.setEmail = value.trim();
                        widget.onValueChanged(params);
                      },
                    ),
                    const Gap(10),
                    TextFormField(
                      decoration: const InputDecoration(
                          hintText: 'Your position (e.g Buyer)'),
                      keyboardType: TextInputType.name,
                      inputFormatters: [Validators.validInput()],
                      validator: (input) => Validators.verifyInput(input,
                          field: 'Position', length: 3),
                      onSaved: (value) => params.setPosition = value!,
                      initialValue: params.position,
                      onChanged: (value) {
                        params.setPosition = value.trim();
                        widget.onValueChanged(params);
                      },
                    ),
                  ],
                ),
              ),
            ),
            const Gap(12),
            CustomFilledButton(
              style: FilledButton.styleFrom(
                minimumSize: const Size(double.infinity, 49),
              ),
              onPressed: handleSubmitted,
              text: 'Continue',
              loaderNotifier: _loader,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> handleSubmitted() async {
    final FormState form = _formKey.currentState!;
    form.save();
    if (form.validate()) {
      sendOtpToEmail();
    } else {
      _validateMode.value = AutovalidateMode.always;
      Toast.error("Kindly attend to the error(s) below", context);
    }
  }

  Future<void> sendOtpToEmail() async {
    _loader.value = true;
    final normalizedNumber = await Validators.validatePhoneNumber(
        params.number!, widget.countryCode!);
    if (widget.countryCode != null && normalizedNumber != null) {
      this.params.setNumber = normalizedNumber;
      final params = SendOTParams(
        mode: PhoneAuthMode.Email,
        email: this.params.email,
        url:
            '${ref.read(appConfigProvider).firebaseServiceUrl}$kSendOtpApiPath',
      );
      final res = await ref.read(
        sendOtpUseCaseProvider(params),
      );
      _loader.value = false;
      switch (res) {
        case Success():
          widget.onNext(this.params);
        case Failure error:
          if (mounted) {
            Toast.apiError(error.error, context);
          }
      }
    } else {
      _loader.value = false;
      if (mounted) {
        Toast.error(
            "Server could not validate phone number for selected address",
            context);
      }
    }
  }

  void reset(FormState form) {
    form.reset();
  }
}

class RetailerParams {
  RetailerParams();
  String? fName;
  String? lName;
  String? number;
  String? email;
  String? position;

  set setFName(String? names) => fName = names;
  set setLName(String? names) => lName = names;
  set setNumber(String? number) => this.number = number;
  set setEmail(String? email) => this.email = email;
  set setPosition(String? position) => this.position = position;

  toMap() {
    return {
      'contactEmail': email?.toLowerCase(),
      'email': email?.toLowerCase(),
      'contactPhone': number,
      'phone': number,
      'contactName': '$fName $lName',
      'contactRole': position,
    };
  }
}
