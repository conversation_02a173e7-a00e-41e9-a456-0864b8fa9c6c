import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class PartnerRegistration extends ConsumerStatefulWidget {
  final ValueChanged<PartnerParams> onNext;
  final ValueChanged<PartnerParams> onValueChanged;
  final ValueNotifier<bool> loader;
  final PartnerParams? params;
  final String? countryCode;
  const PartnerRegistration(
      this.onNext, this.params, this.loader, this.countryCode, this.onValueChanged,
      {super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _PartnerRegistration();
  }
}

class _PartnerRegistration extends ConsumerState<PartnerRegistration> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ValueNotifier<AutovalidateMode> _validateMode =
      ValueNotifier(AutovalidateMode.disabled);
  late final params = widget.params ?? PartnerParams();

  @override
  void dispose() {
    _validateMode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: SizedBox(
        width: 380,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sign up as a Partner',
              style: theme.textTheme.headlineMedium,
            ),
            Text(
              'Fill in your contact information',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(color: Palette.blackSecondary),
            ),
            const Gap(20),
            ValueListenableBuilder<AutovalidateMode>(
              valueListenable: _validateMode,
              builder: (context, mode, _) => Form(
                key: _formKey,
                autovalidateMode: mode,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            decoration:
                                const InputDecoration(hintText: 'First Name'),
                            keyboardType: TextInputType.name,
                            validator: (input) => Validators.verifyInput(input,
                                field: 'first name', length: 3),
                            onSaved: (value) => params.setFName = value?.trim(),
                            initialValue: params.fName,
                            onChanged: (value){
                              params.setFName = value.trim();
                              widget.onValueChanged(params);
                            },
                          ),
                        ),
                        const Gap(10),
                        Expanded(
                          child: TextFormField(
                            decoration:
                                const InputDecoration(hintText: 'Last Name'),
                            keyboardType: TextInputType.name,
                            validator: (input) => Validators.verifyInput(input,
                                field: 'Last name', length: 3),
                            onSaved: (value) => params.setLName = value?.trim(),
                            initialValue: params.lName,
                            onChanged: (value){
                              params.setLName = value.trim();
                              widget.onValueChanged(params);
                            },
                          ),
                        ),
                      ],
                    ),
                    const Gap(10),
                    TextFormField(
                      decoration: const InputDecoration(
                          hintText: 'Contact phone number'),
                      initialValue: params.number,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        Validators.validNumberInput(),
                      ],
                      validator: (input) => Validators.verifyInput(input,
                          field: 'Contact phone number', length: 1),
                      onSaved: (value) => params.setNumber = value!,
                      onChanged: (value){
                        params.setNumber = value.trim();
                        widget.onValueChanged(params);
                      },
                    ),
                    const Gap(10),
                    TextFormField(
                      decoration:
                          const InputDecoration(hintText: 'Contact email'),
                      keyboardType: TextInputType.emailAddress,
                      initialValue: params.email,
                      inputFormatters: const [],
                      validator: Validators.verifyEmail,
                      onSaved: (value) => params.setEmail = value!,
                      onChanged: (value){
                        params.setEmail = value.trim();
                        widget.onValueChanged(params);
                      },
                    ),
                    const Gap(10),
                    TextFormField(
                      decoration: const InputDecoration(
                          hintText: 'Contact position (e.g Buyer)'),
                      keyboardType: TextInputType.name,
                      inputFormatters: [Validators.validInput()],
                      validator: (input) => Validators.verifyInput(input,
                          field: 'Contact position', length: 3),
                      onSaved: (value) => params.setPosition = value!,
                      initialValue: params.position,
                      onChanged: (value){
                        params.setPosition= value.trim();
                        widget.onValueChanged(params);
                      },
                    ),
                  ],
                ),
              ),
            ),
            const Gap(12),
            CustomFilledButton(
              onPressed: handleSubmitted,
              text: 'Continue',
              loaderNotifier: widget.loader,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> handleSubmitted() async {
    final FormState form = _formKey.currentState!;
    form.save();
    if (form.validate()) {
      final normalizedNumber = await Validators.validatePhoneNumber(
          params.number!, widget.countryCode!);
      if (widget.countryCode != null && normalizedNumber != null) {
        params.setNumber = normalizedNumber;
        widget.onNext(params);
      } else {
        if (mounted) {
          Toast.error(
              "Server could not validate phone number for selected address",
              context);
        }
      }
    } else {
      _validateMode.value = AutovalidateMode.always;
      Toast.error("Kindly attend to the error(s) below", context);
    }
  }

  void reset(FormState form) {
    form.reset();
  }
}

class PartnerParams {
  PartnerParams();
  String? fName;
  String? lName;
  String? position;
  String? number;
  String? email;

  set setNumber(number) => this.number = number;
  set setEmail(email) => this.email = email;
  set setFName(names) => fName = names;
  set setLName(names) => lName = names;
  set setPosition(position) => this.position = position;

  toMap() {
    return {
      "firstname": fName,
      "lastname": lName,
      'jobtitle': position,
      'phone': number,
      'email': email,
    };
  }
}
