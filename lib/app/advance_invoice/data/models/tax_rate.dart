import 'package:equatable/equatable.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class TaxRate extends Equatable {
  /// Generated locally
  final String id;
  final String name;

  /// optional. [min: 0, max:100] - allow 0% for tax-exempt items
  final num? rate;
  final num? amount;
  // final String? retailOutletId;

  TaxRate({
    String? id,
    required this.name,
    this.rate,
    this.amount,
  }) : id = id ?? generateRandomId();

  TaxRate copyWith({
    String? name,
    num? rate,
    num? amount,
  }) {
    return TaxRate(
      id: id,
      name: name ?? this.name,
      rate: rate ?? this.rate,
      amount: amount ?? this.amount,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'rate': rate,
      'amount': amount,
    };
  }

  Map<String, dynamic> toData() {
    return <String, dynamic>{
      'name': name,
      // 'rate': rate,
      // 'amount': amount,
    };
  }

  factory TaxRate.fromMap(Map<String, dynamic> map) {
    return TaxRate(
      id: map['_id'] ?? map['id'] ?? generateRandomId(),
      name: map['name'] as String,
      rate: map['rate'],
      amount: map['amount'],
    );
  }

  factory TaxRate.defaultValue() {
    return TaxRate(
      id: 'id',
      name: 'name',
      rate: 10,
      amount: 1000,
    );
  }

  @override
  List<Object?> get props => [id, name, rate, amount];
}
