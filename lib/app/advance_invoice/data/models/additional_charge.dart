import 'package:equatable/equatable.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class AdditionalCharge extends Equatable {
  /// Generated locally
  final String id;
  final String name;
  final num? amount;

  AdditionalCharge({
    String? id,
    required this.name,
    this.amount,
  }) : id = id ?? generateRandomId();

  AdditionalCharge copyWith({
    String? name,
    num? amount,
  }) {
    return AdditionalCharge(
      id: id,
      name: name ?? this.name,
      amount: amount ?? this.amount,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'amount': amount,
    };
  }

  Map<String, dynamic> toData() {
    return <String, dynamic>{
      'name': name,
      // 'amount': amount,
    };
  }

  Map<String, dynamic> toFullData() {
    return <String, dynamic>{
      'name': name,
      'amount': amount,
    };
  }

  factory AdditionalCharge.fromMap(Map<String, dynamic> map) {
    return AdditionalCharge(
      id: map['_id'] ?? map['id'] ?? generateRandomId(),
      name: map['name'] as String,
      amount: map['amount'],
    );
  }

  factory AdditionalCharge.defaultValue() {
    return AdditionalCharge(
      id: 'id',
      name: 'name',
      amount: 1000,
    );
  }

  @override
  List<Object?> get props => [id, name, amount];
}
