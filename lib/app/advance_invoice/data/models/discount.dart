import 'package:equatable/equatable.dart';

class Discount extends Equatable {
  final DiscountType type;
  final num value;

  const Discount({
    required this.type,
    required this.value,
  });

  Discount copyWith({
    DiscountType? type,
    num? value,
  }) {
    return Discount(
      type: type ?? this.type,
      value: value ?? this.value,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'type': type.name,
      'value': value,
    };
  }

  factory Discount.fromMap(Map<String, dynamic> map) {
    return Discount(
      type: DiscountType.fromString(map['type'] as String),
      value: map['value'] as num,
    );
  }

  @override
  List<Object> get props => [type, value];
}

enum DiscountType {
  fixed('fixed'),
  percentage('percentage');

  final String name;
  const DiscountType(this.name);

  static DiscountType fromString(String value) {
    return DiscountType.values.firstWhere(
      (e) => e.name.toLowerCase() == value.toLowerCase(),
    );
  }
}
