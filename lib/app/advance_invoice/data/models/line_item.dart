import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_procurement/app/advance_invoice/data/models/discount.dart';

class LineItem extends Equatable {
  final String name;
  final num quantity;
  final num price;
  final num taxRate;
  final num itemTotal;
  final Discount? discount;

  const LineItem({
    String? id,
    required this.name,
    required this.quantity,
    required this.price,
    required this.taxRate,
    required this.itemTotal,
    this.discount,
  });

  LineItem copyWith({
    String? name,
    num? quantity,
    num? price,
    num? taxRate,
    num? itemTotal,
    Discount? discount,
  }) {
    return LineItem(
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      taxRate: taxRate ?? this.taxRate,
      itemTotal: itemTotal ?? this.itemTotal,
      discount: discount ?? this.discount,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'name': name,
      'quantity': quantity,
      'price': price,
      'taxRate': taxRate,
      'itemTotal': itemTotal,
      'discount': discount?.toMap(),
    };
  }

  factory LineItem.fromMap(Map<String, dynamic> map) {
    return LineItem(
      name: map['name'] as String,
      quantity: map['quantity'] as num,
      price: map['price'] as num,
      taxRate: map['taxRate'] as num,
      itemTotal: map['itemTotal'] ?? map['totalPrice'] ?? 0,
      discount: map['discount'] != null
          ? Discount.fromMap(map['discount'] as Map<String, dynamic>)
          : null,
    );
  }

  @override
  List<Object?> get props {
    return [
      name,
      quantity,
      price,
      taxRate,
      itemTotal,
      discount,
    ];
  }

  String toJson() => json.encode(toMap());

  factory LineItem.fromJson(String source) =>
      LineItem.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  factory LineItem.defaultValue() {
    return const LineItem(
      name: 'name',
      quantity: 1,
      price: 100,
      taxRate: 10,
      itemTotal: 110,
      discount: null,
    );
  }
}
