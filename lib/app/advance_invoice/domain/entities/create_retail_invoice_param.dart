import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/core/models/optional.dart';
import 'package:td_procurement/src/extensions/index.dart';

import 'line_item_param.dart';

class CreateRetailInvoiceParam extends Equatable {
  final String id;
  final String issuerOutletId;
  final RetailOutlet? recipientOutlet;
  final String? note;
  final List<LineItemParam> lineItems;
  final List<TaxRate>? taxes;
  final List<AdditionalCharge>? charges;
  final Discount? discount;
  final bool isAdvance;
  final WalletBank? settlementBank;
  final String currencyCode;
  final Currency? currency;
  final String? disbursementAccountId;
  final DateTime? dueDate;

  const CreateRetailInvoiceParam({
    required this.id,
    required this.issuerOutletId,
    this.recipientOutlet,
    this.note,
    required this.lineItems,
    this.taxes,
    this.charges,
    this.discount,
    required this.isAdvance,
    this.settlementBank,
    required this.currencyCode,
    this.currency,
    this.disbursementAccountId,
    this.dueDate,
  });

  bool get isComplete =>
      lineItems.isNotEmpty &&
      issuerOutletId.isNotEmpty &&
      recipientOutlet != null &&
      settlementBank != null &&
      dueDate != null &&
      (isAdvance
          ? (disbursementAccountId != null && disbursementAccountId!.isNotEmpty)
          : settlementBank != null);

  /// Calculate the total invoice amount including line items, taxes, charges, and discounts
  num get invoiceTotal {
    // Calculate subtotal from line items
    final subtotal = lineItems.fold<num>(0, (sum, item) => sum + item.total);

    // Calculate taxes
    final taxAmount = (taxes ?? []).fold<num>(0, (sum, tax) {
      final taxRate = tax.rate ?? 0;
      return sum + (subtotal * (taxRate / 100));
    });

    // Calculate charges
    final chargeAmount = (charges ?? []).fold<num>(0, (sum, charge) {
      return sum + (charge.amount ?? 0);
    });

    // Calculate discount
    num discountAmount = 0;
    if (discount != null) {
      discountAmount = discount!.type == DiscountType.fixed
          ? discount!.value
          : (subtotal * (discount!.value / 100));
    }

    return subtotal + taxAmount + chargeAmount - discountAmount;
  }

  CreateRetailInvoiceParam copyWith({
    String? issuerOutletId,
    Optional<RetailOutlet?>? recipientOutlet,
    Optional<String?>? note,
    List<LineItemParam>? lineItems,
    List<TaxRate>? taxes,
    List<AdditionalCharge>? charges,
    Optional<Discount?>? discount,
    bool? isAdvance,
    Optional<WalletBank?>? settlementBank,
    String? currencyCode,
    Currency? currency,
    Optional<String?>? disbursementAccountId,
    Optional<DateTime?>? dueDate,
  }) {
    return CreateRetailInvoiceParam(
      id: id,
      issuerOutletId: issuerOutletId ?? this.issuerOutletId,
      recipientOutlet: recipientOutlet != null
          ? recipientOutlet.value
          : this.recipientOutlet,
      note: note != null ? note.value : this.note,
      lineItems: lineItems ?? this.lineItems,
      taxes: taxes ?? this.taxes,
      charges: charges ?? this.charges,
      discount: discount != null ? discount.value : this.discount,
      isAdvance: isAdvance ?? this.isAdvance,
      settlementBank:
          settlementBank != null ? settlementBank.value : this.settlementBank,
      currencyCode: currencyCode ?? this.currencyCode,
      currency: currency ?? this.currency,
      disbursementAccountId: disbursementAccountId != null
          ? disbursementAccountId.value
          : this.disbursementAccountId,
      dueDate: dueDate != null ? dueDate.value : this.dueDate,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'issuerOutletId': issuerOutletId,
      'recipientOutlet': recipientOutlet?.toMap(),
      'note': note,
      'lineItems': lineItems.map((x) => x.toMap()).toList(),
      'taxes': taxes?.map((x) => x.toMap()).toList(),
      'charges': charges?.map((x) => x.toMap()).toList(),
      'discount': discount?.toMap(),
      'isAdvance': isAdvance,
      'bankAccount': settlementBank?.toMap(),
      'currency': currency?.toMap(),
      'disbursementAccountId': disbursementAccountId,
      'dueDate': dueDate?.millisecondsSinceEpoch,
    };
  }

  Map<String, dynamic> toData() {
    // final otherTaxes = (taxes ?? [])
    //     .where((x) => x.name.toLowerCase() != 'vat')
    //     .map((x) => x.toData())
    //     .toList();
    final otherCharges = (charges ?? [])
        .where((x) =>
            x.name.toLowerCase() != 'shipping fee' &&
            x.name.toLowerCase() != 'processing fee')
        .map((x) => x.toFullData())
        .toList();

    return <String, dynamic>{
      'issuerOutletId': issuerOutletId,
      'recipientOutletId':
          recipientOutlet?.id ?? recipientOutlet?.retailOutletId,
      if (note != null) 'note': note,
      'items': lineItems.map((x) => x.toData()).toList(),
      'shippingCost': (charges ?? [])
              .firstWhereOrNull((x) => x.name.toLowerCase() == 'shipping fee')
              ?.amount ??
          0,
      'processingCost': (charges ?? [])
              .firstWhereOrNull((x) => x.name.toLowerCase() == 'processing fee')
              ?.amount ??
          0,
      'taxRate': (taxes ?? [])
              .firstWhereOrNull((x) => x.name.toLowerCase() == 'vat')
              ?.rate ??
          0,
      // if (otherTaxes.isNotEmpty) 'otherTaxes': otherTaxes,
      if (otherCharges.isNotEmpty) 'otherCharges': otherCharges,
      if (discount != null) 'discount': discount!.toMap(),
      'isAdvance': isAdvance,
      'bankAccount': settlementBank?.toData(),
      'currency': currency?.toMap(),
      if (isAdvance && disbursementAccountId != null)
        'disbursementAccountId': disbursementAccountId!,
      'dueDate': dueDate!.toDate(),
      'createdBy': issuerOutletId, //TODO: Remove after backend fix
    };
  }

  // factory CreateRetailInvoiceParam.fromMap(Map<String, dynamic> map) {
  //   return CreateRetailInvoiceParam(
  //     id: map['_id'] ?? map['id'] ?? '',
  //     issuerOutletId: map['issuerOutletId'] as String,
  //     recipientOutletId: map['recipientOutletId'] as String,
  //     note: map['note'] as String?,
  //     lineItems: List<LineItem>.from(
  //       (map['lineItems'] as List).map<LineItem>(
  //         (x) => LineItem.fromMap(x as Map<String, dynamic>),
  //       ),
  //     ),
  //     taxes: map['taxes'] != null
  //         ? List<TaxRate>.from(
  //             (map['taxes'] as List).map<TaxRate?>(
  //               (x) => TaxRate.fromMap(x as Map<String, dynamic>),
  //             ),
  //           )
  //         : null,
  //     charges: map['charges'] != null
  //         ? List<AdditionalCharge>.from(
  //             (map['charges'] as List).map<AdditionalCharge?>(
  //               (x) => AdditionalCharge.fromMap(x as Map<String, dynamic>),
  //             ),
  //           )
  //         : null,
  //     discount: map['discount'] != null
  //         ? Discount.fromMap(map['discount'] as Map<String, dynamic>)
  //         : null,
  //     isFinanced: map['isFinanced'] as bool,
  //     bankAccount: map['bankAccount'] != null
  //         ? WalletBank.fromMap(map['bankAccount'])
  //         : null,
  //     currencyCode: map['currencyCode'] as String,
  //     currency: map['currency'] != null
  //         ? Currency.fromMap(map['currency'] as Map<String, dynamic>)
  //         : null,
  //   );
  // }

  @override
  List<Object?> get props {
    return [
      id,
      issuerOutletId,
      recipientOutlet,
      note,
      lineItems,
      taxes,
      charges,
      discount,
      isAdvance,
      settlementBank,
      currencyCode,
      currency,
      disbursementAccountId,
      dueDate,
    ];
  }
}
