import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/core/models/index.dart';

class FetchAdvanceInvoiceParams {
  final String invoiceStatus;
  final List<DateTime?> selectedDates;
  // final List<String> selectedOutlets;
  final String searchText;
  final int currentPage;
  int perPage;
  int totalPages;
  bool loaded;
  bool loadingMore;
  final int activeIndex;

  FetchAdvanceInvoiceParams({
    required this.invoiceStatus,
    required this.selectedDates,
    // required this.selectedOutlets,
    required this.searchText,
    required this.currentPage,
    this.perPage = 10,
    this.totalPages = 1,
    this.loaded = false,
    this.loadingMore = false,
    this.activeIndex = 0,
  });

  factory FetchAdvanceInvoiceParams.defaultValue() {
    return FetchAdvanceInvoiceParams(
      invoiceStatus: 'all',
      selectedDates: [],
      // selectedOutlets: [],
      searchText: '',
      currentPage: 1,
      perPage: 10,
      totalPages: 0,
      loaded: false,
      loadingMore: false,
      activeIndex: 0,
    );
  }

  int get totalCount => totalPages * perPage;

  FetchAdvanceInvoiceParams copyWith({
    String? invoiceStatus,
    Optional<List<DateTime?>>? selectedDates,
    // List<String>? selectedOutlets,
    String? searchText,
    int? currentPage,
    int? perPage,
    int? totalPages,
    bool? loaded,
    bool? loadingMore,
    int? activeIndex,
  }) {
    return FetchAdvanceInvoiceParams(
      invoiceStatus: invoiceStatus ?? this.invoiceStatus,
      selectedDates: selectedDates?.value ?? this.selectedDates,
      // selectedOutlets: selectedOutlets ?? this.selectedOutlets,
      searchText: searchText ?? this.searchText,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      totalPages: totalPages ?? this.totalPages,
      loaded: loaded ?? this.loaded,
      loadingMore: loadingMore ?? this.loadingMore,
      activeIndex: activeIndex ?? this.activeIndex,
    );
  }

  FetchAdvanceInvoiceParams fromQueryParams(QueryParameters params) {
    return FetchAdvanceInvoiceParams(
      invoiceStatus: invoiceStatus,
      selectedDates: selectedDates,
      // selectedOutlets: selectedOutlets,
      searchText: searchText,
      loaded: loaded,
      loadingMore: loadingMore,
      currentPage: params.page,
      perPage: params.perPage,
      totalPages: params.totalPages,
      activeIndex: activeIndex,
    );
  }
}

class FetchAdvanceInvoicesResponse {
  final List<RetailInvoice> retailInvoices;
  final QueryParameters queryParams;

  FetchAdvanceInvoicesResponse({
    required this.retailInvoices,
    required this.queryParams,
  });
}

String getQueryString(FetchAdvanceInvoiceParams params) {
  final filters = <String, dynamic>{};

  if (params.invoiceStatus.isNotEmpty && params.invoiceStatus != 'all') {
    if (params.invoiceStatus == 'awaiting') {
      filters['status'] = 'pending';
    } else if (params.invoiceStatus == 'accepted') {
      filters['status'] = 'accepted';
    } else if (params.invoiceStatus == 'processing') {
      filters['status'] = 'processing';
    } else if (params.invoiceStatus == 'completed') {
      filters['status'] = 'completed';
    } else {
      filters['status'] = params.invoiceStatus;
    }
  }

  if (params.searchText.isNotEmpty) {
    filters['searchText'] = params.searchText;
    // if (params.searchText.startsWith('01')) {
    //   filters['reference'] = params.searchText;
    // }
  }

  if (params.selectedDates.length == 2) {
    filters['startDate'] = params.selectedDates[0]?.toIso8601String();
    filters['endDate'] = params.selectedDates[1]
        ?.add(const Duration(
            hours: 23, minutes: 59, seconds: 59, milliseconds: 999))
        .toIso8601String();
  }

  filters['page'] = params.currentPage.toString();
  filters['perPage'] = params.perPage.toString();

  return Uri(queryParameters: filters).query;
}

class LinkBankParams {
  final String accountNumber;
  final String bankCode;
  final String accountName;
  final String bankName;
  final String? accountBVN;
  final LinkBankType? type;

  LinkBankParams(this.accountNumber, this.bankCode, this.accountName,
      this.bankName, this.accountBVN, this.type);

  Map<String, dynamic> toMap() {
    return {
      "accountNumber": accountNumber,
      "bankCode": bankCode,
      "accountName": accountName,
      "bankName": bankName,
      "accountBVN": accountBVN,
      if (type != null) "type": type!.name
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

enum LinkBankType { terminal }
