import 'package:equatable/equatable.dart';
import 'package:td_procurement/app/advance_invoice/data/models/discount.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class LineItemParam extends Equatable {
  /// Generated locally
  final String id;
  final String name;
  final num quantity;
  final num unitPrice;

  /// optional. [min: 0, max:100] - allow 0% for tax-exempt items
  final num taxRate;
  final Discount? discount;
  final int? index;

  LineItemParam({
    String? id,
    required this.name,
    required this.quantity,
    required this.unitPrice,
    required this.taxRate,
    this.discount,
    this.index,
  }) : id = id ?? generateRandomId();

  num get total => quantity * unitPrice;
  num get tax => (total * (taxRate / 100));
  num get totalWithTaxAndDiscount {
    final subtotal = total;
    if (discount != null) {
      final discountAmount = discount!.type == DiscountType.fixed
          ? discount!.value
          : (subtotal * (discount!.value / 100));
      return subtotal + tax - discountAmount;
    }
    return subtotal + tax;
  }

  LineItemParam copyWith({
    String? name,
    num? quantity,
    num? unitPrice,
    num? taxRate,
    Discount? discount,
    int? index,
  }) {
    return LineItemParam(
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      taxRate: taxRate ?? this.taxRate,
      discount: discount ?? this.discount,
      index: index ?? this.index,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'taxRate': taxRate,
      'discount': discount?.toMap(),
    };
  }

  Map<String, dynamic> toData() {
    return {
      'name': name,
      'quantity': quantity,
      'price': unitPrice,
      'taxRate': taxRate,
      if (discount != null) 'discount': discount?.toMap(),
    };
  }

  // factory LineItemParam.fromMap(Map<String, dynamic> map) {
  //   return LineItemParam(
  //       name: map['name'] as String,
  //       quantity: map['quantity'] as num,
  //       unitPrice: map['unitPrice'] as num,
  //       taxRate: map['taxRate'] as num,
  //       discount: map['discount'] != null
  //           ? Discount.fromMap(map['discount'] as Map<String, dynamic>)
  //           : null);
  // }

  @override
  List<Object?> get props => [
        name,
        quantity,
        unitPrice,
        taxRate,
        discount,
        index,
      ];
}
