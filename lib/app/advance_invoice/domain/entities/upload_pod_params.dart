import 'package:equatable/equatable.dart';
import 'package:td_procurement/app/business_verification/domain/entities/file_upload_params.dart';

class UploadPodParams extends Equatable {
  final String invoiceId;
  final List<FileUploadParams> documents;

  const UploadPodParams({
    required this.invoiceId,
    required this.documents,
  });

  Map<String, dynamic> toMap() {
    return {
      'documents': documents.map((doc) => doc.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [invoiceId, documents];
}
