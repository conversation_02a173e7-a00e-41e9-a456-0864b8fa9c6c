import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/upload_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/file_upload_state.dart';
import 'package:td_procurement/app/business_verification/domain/entities/file_upload_params.dart';
import 'package:td_procurement/app/business_verification/presentation/controllers/file_upload.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

// Future<dynamic> _uploadPod(UploadPodParams params) async {
//   final container = ProviderContainer();
//   try {
//     final response = await container.read(uploadPodUseCaseProvider(params));
//     return response;
//   } finally {
//     container.dispose();
//   }
// }

class UploadPodWidget extends ConsumerStatefulWidget {
  final String invoiceId;
  final VoidCallback? onPodUploaded;

  const UploadPodWidget(
      {super.key, required this.invoiceId, this.onPodUploaded});

  @override
  ConsumerState<UploadPodWidget> createState() => _UploadPodModalState();
}

class _UploadPodModalState extends ConsumerState<UploadPodWidget>
    with FileUpload {
  final ValueNotifier<bool> _loader = ValueNotifier(false);
  final String podDocumentKey = "POD Document";

  @override
  Widget build(BuildContext context) {
    final fileUploadState = ref.watch(fileUploadProvider);
    final textTheme = Theme.of(context).textTheme;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 420,
          minWidth: 380,
          maxHeight: 520,
        ),
        child: Material(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Tooltip(
                      message: 'Close',
                      preferBelow: false,
                      child: IconButton(
                        icon: SvgPicture.asset(kCloseSvg),
                        onPressed: () {
                          Navigator.of(context).pop();
                          _removeFile(podDocumentKey);
                        },
                        // tooltip: 'Close',
                      ),
                    ),
                    const Gap(8),
                    Text(
                      'POD',
                      style: textTheme.titleMedium?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const Gap(8),
                Text(
                  'Upload Proof of Delivery',
                  style: textTheme.headlineSmall,
                ),
                const Gap(8),
                Text(
                  'Please upload the proof of delivery document for this invoice.',
                  style: textTheme.bodyMedium?.copyWith(
                    color: Palette.blackSecondary,
                  ),
                ),
                const Gap(24),
                _buildFileUploadSection(fileUploadState),
                const Spacer(),
                Row(
                  children: [
                    Expanded(
                      child: CustomFilledButton(
                        onPressed:
                            _canSubmit(fileUploadState) ? _submit : () {},
                        text: 'Upload POD',
                        loaderNotifier: _loader,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFileUploadSection(FileUploadState fileUploadState) {
    final textTheme = Theme.of(context).textTheme;
    final hasFile = fileUploadState.files.containsKey(podDocumentKey);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'POD Document',
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (hasFile)
                IconButton(
                  onPressed: () => _removeFile(podDocumentKey),
                  icon: SvgPicture.asset(
                    '$kSvgDir/order/delete.svg',
                    colorFilter:
                        const ColorFilter.mode(Colors.red, BlendMode.srcIn),
                  ),
                ),
            ],
          ),
          const Gap(8),
          if (hasFile) ...[
            Row(
              children: [
                Container(
                  height: 120,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Palette.kF7F7F7,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Image.memory(
                      fileUploadState.files[podDocumentKey]!.file.bytes!,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                Flexible(
                  child: Text(
                    fileUploadState.files[podDocumentKey]!.file.name,
                    style: textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ] else ...[
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Palette.stroke,
                  style: BorderStyle.solid,
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.upload),
                  const Gap(8),
                  Text(
                    'Click to upload',
                    style: textTheme.bodyMedium?.copyWith(
                      color: Palette.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ).onTap(() => _fetchDoc(podDocumentKey)),
            const Gap(8),
            Text(
              'Only upload .pdf, .jpg or .png file, maximum file size is 1mb',
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  bool _canSubmit(FileUploadState fileUploadState) {
    return fileUploadState.files.containsKey(podDocumentKey) &&
        !fileUploadState.isLoading;
  }

  void _fetchDoc(String key) async {
    final allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      withData: true,
      allowedExtensions: allowedExtensions,
    );

    if (result != null) {
      PlatformFile file = result.files.single;
      String? ext = file.extension!.toLowerCase();

      if (!allowedExtensions.contains(ext)) {
        if (mounted) {
          Toast.error("Unsupported File format", context);
        }
        return;
      }

      if (mounted && !isValidFileSize(file.bytes!, context)) {
        return;
      }

      final fileUploadParams = FileUploadParams(
        file.extension!,
        file.bytes!,
        file,
        file.extension,
        key,
      );

      ref.read(fileUploadProvider.notifier).addFile(key, fileUploadParams);
    }
  }

  void _removeFile(String key) {
    ref.read(fileUploadProvider.notifier).removeFile(key);
  }

  void _submit() async {
    final fileUploadState = ref.read(fileUploadProvider);
    final documents = fileUploadState.files.values.toList();

    if (documents.isEmpty) {
      Toast.error('Please select a POD document', context);
      return;
    }

    _loader.value = true;
    ref.read(fileUploadProvider.notifier).setLoading(true);

    final params = UploadPodParams(
      invoiceId: widget.invoiceId,
      documents: documents,
    );

    final response = await ref.read(uploadPodUseCaseProvider(params));
    // final response = await compute(_uploadPod, params);

    if (!mounted) return;

    response.when(
      success: (data) {
        _loader.value = false;
        ref.read(fileUploadProvider.notifier).setLoading(false);
        ref.read(fileUploadProvider.notifier).clearFiles();
        widget.onPodUploaded?.call();
        Toast.success('POD uploaded successfully', context);
        Navigator.of(context).pop();
      },
      failure: (error, code) {
        _loader.value = false;
        ref.read(fileUploadProvider.notifier).setLoading(false);
        Toast.apiError(error, context);
      },
    );
  }

  @override
  void dispose() {
    _loader.dispose();
    super.dispose();
  }
}
