import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AdvanceInvoiceSummaryTableWidget extends StatelessWidget {
  final RetailInvoice invoice;
  const AdvanceInvoiceSummaryTableWidget(this.invoice, {super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final currencyCode = invoice.currency.iso!;
    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.only(top: 30, bottom: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            // constraints: const BoxConstraints(
            //     minHeight: 552, maxWidth: double.maxFinite),
            // padding: const EdgeInsets.all(16.0),
            // decoration: BoxDecoration(
            //   color: Colors.white,
            //   border: Border.all(color: Palette.stroke, width: 1),
            //   boxShadow: [
            //     BoxShadow(
            //       color: const Color(0xFF000000).withValues(alpha: 0.04),
            //       offset: const Offset(0, 2),
            //       blurRadius: 2,
            //       spreadRadius: -1,
            //     ),
            //   ],
            //   borderRadius: BorderRadius.circular(8),
            // ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Table(
                  columnWidths: const {
                    0: FlexColumnWidth(4),
                    1: FlexColumnWidth(1),
                    2: FlexColumnWidth(1),
                    3: FlexColumnWidth(1),
                    4: FlexColumnWidth(1),
                    5: FlexColumnWidth(1),
                  },
                  children: [
                    TableRow(
                      children: [
                        _buildTableHeader('Description', context),
                        _buildTableHeader('Qty', context),
                        _buildTableHeader('Unit Price', context),
                        _buildTableHeader('Discount', context),
                        _buildTableHeader('Tax', context),
                        _buildTableHeader('Amount', context),
                      ],
                      decoration: BoxDecoration(
                        border: Border.symmetric(
                          horizontal: BorderSide(
                              color: Palette.kE7E7E7.withValues(alpha: 0.5),
                              width: 1),
                        ),
                      ),
                    ),
                    const TableRow(
                      children: [
                        SizedBox(height: 8),
                        SizedBox(),
                        SizedBox(),
                        SizedBox(),
                        SizedBox(),
                        SizedBox(),
                      ],
                    ),
                    ...invoice.items.map((item) {
                      final baseTotal = item.quantity * item.price;
                      num discount = 0;
                      if (item.discount != null) {
                        if (item.discount!.type == DiscountType.fixed) {
                          discount = item.discount!.value;
                        } else if (item.discount!.type ==
                            DiscountType.percentage) {
                          discount = baseTotal * item.discount!.value / 100;
                        }
                      }
                      final taxed = baseTotal - discount;
                      final tax =
                          item.taxRate > 0 ? taxed * item.taxRate / 100 : 0;
                      final total = taxed + tax;
                      return TableRow(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(item.name,
                                style: textTheme.bodyMedium?.copyWith(
                                    color: Palette.primaryBlack,
                                    fontWeight: FontWeight.w500)),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(item.quantity.toString(),
                                style: textTheme.bodyMedium
                                    ?.copyWith(color: Palette.blackSecondary)),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                                CurrencyWidget.value(
                                    context, currencyCode, item.price),
                                style: textTheme.bodyMedium
                                    ?.copyWith(color: Palette.blackSecondary)),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                                discount > 0
                                    ? CurrencyWidget.value(
                                        context, currencyCode, discount)
                                    : '-',
                                style: textTheme.bodyMedium
                                    ?.copyWith(color: Palette.blackSecondary)),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                                item.taxRate > 0 ? '${item.taxRate}%' : '-',
                                style: textTheme.bodyMedium
                                    ?.copyWith(color: Palette.blackSecondary)),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                                CurrencyWidget.value(
                                    context, currencyCode, total),
                                style: textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w600)),
                          ),
                        ],
                      );
                    }),
                  ],
                ),
                const Gap(18),
                Padding(
                  padding: const EdgeInsets.only(left: 550, right: 45),
                  child: Column(
                    // left: 580, right: 38
                    children: [
                      _buildSummaryRow(
                          context,
                          'Subtotal',
                          invoice.subtotal > 0
                              ? invoice.subtotal
                              : (invoice.total - invoice.totalTax),
                          currencyCode),
                      const Gap(8),
                      _buildSummaryRow(
                          context, 'Tax', invoice.totalTax, currencyCode),
                      const Gap(8),
                      _buildSummaryRow(context, 'Shipping',
                          invoice.shippingCost, currencyCode),
                      const Gap(8),
                      _buildSummaryRow(context, 'Processing',
                          invoice.processingCost, currencyCode),
                      if (invoice.discount != null) ...[
                        const Gap(8),
                        _buildSummaryRow(context, 'Discount',
                            -invoice.discount!.value, currencyCode),
                      ],
                      const Gap(8),
                      _buildSummaryRow(
                          context, 'Total', invoice.total, currencyCode,
                          isTotal: true),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(String text, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
              fontWeight: FontWeight.w400,
            ),
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    num amount,
    String currencyCode, {
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal ? const TextStyle(fontWeight: FontWeight.bold) : null,
        ),
        Text(
          CurrencyWidget.value(context, currencyCode, amount),
          style: isTotal ? const TextStyle(fontWeight: FontWeight.bold) : null,
        ),
      ],
    );
  }

  // Color _getStatusColor(String status) {
  //   switch (status.toLowerCase()) {
  //     case 'approved':
  //       return Colors.green;
  //     case 'pending':
  //       return Colors.orange;
  //     case 'rejected':
  //       return Colors.red;
  //     default:
  //       return Colors.grey;
  //   }
  // }
}
