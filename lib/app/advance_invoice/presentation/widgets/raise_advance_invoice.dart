import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/items_options_widget.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/items_widget.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/order_search_auto_complete.dart';
import 'package:td_procurement/app/order/presentation/widgets/outlet_search_auto_complete.dart';

import 'payment_financing.dart';

class RaiseAdvanceInvoiceWidget extends ConsumerWidget {
  const RaiseAdvanceInvoiceWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen for order selection and populate items
    ref.listen(selectedOrderProvider, (previous, next) {
      if (next != previous) {
        if (next != null) {
          _populateItemsFromOrder(ref, next);
        } else if (previous != null) {
          _removeOrderItems(ref, previous);
        }
      }
    });

    final selectedCustomer = ref.watch(selectedCustomerProvider);
    final isCustomerSelected = selectedCustomer != null;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 100),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Gap(40),
          // Customer Search Section
          const OutletSearchAutoCompleteWidget(isAdvanceInvoice: true),
          const Gap(40),
          // Order Search Section
          Tooltip(
            message: isCustomerSelected ? '' : 'Select a customer first',
            child: IgnorePointer(
              ignoring: !isCustomerSelected,
              child: Opacity(
                opacity: isCustomerSelected ? 1.0 : 0.5,
                child: const OrderSearchAutoCompleteWidget(),
              ),
            ),
          ),
          const Gap(40),
          // Items Section
          const ItemsWidget(key: ValueKey('items')),
          const Gap(40),
          // Service Options Section (for Taxes/Charges)
          const ItemsOptionsWidget(
            key: ValueKey('items_options'),
          ),
          const Gap(40),
          // Payment & Financing Section
          const PaymentFinancingWidget(
            key: ValueKey('payment_financing'),
          ),
          const Gap(40),
        ],
      ),
    );
  }

  void _populateItemsFromOrder(WidgetRef ref, Order order) {
    final notifier = ref.read(advanceInvoiceControllerProvider.notifier);

    // Remove any existing order items first
    _removeAllOrderItems(ref);

    // Convert Order items to LineItemParam and add them with order tracking
    for (final item in (order.items ?? [])) {
      final lineItem = LineItemParam(
        id: 'order_${order.id}_${item.id}', // Prefix with order info for tracking
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.price,
        taxRate: 0, // Default tax rate, can be adjusted later
      );
      notifier.addLineItem(lineItem);
    }
  }

  void _removeOrderItems(WidgetRef ref, Order order) {
    final notifier = ref.read(advanceInvoiceControllerProvider.notifier);
    final currentItems = ref.read(createAdvanceInvoiceProvider).lineItems;

    // Remove items that belong to this order
    final itemsToRemove = currentItems
        .where((item) => item.id.startsWith('order_${order.id}_'))
        .toList();

    for (final item in itemsToRemove) {
      notifier.removeLineItem(item);
    }
  }

  void _removeAllOrderItems(WidgetRef ref) {
    final notifier = ref.read(advanceInvoiceControllerProvider.notifier);
    final currentItems = ref.read(createAdvanceInvoiceProvider).lineItems;

    // Remove all items that came from orders (have order_ prefix)
    final itemsToRemove =
        currentItems.where((item) => item.id.startsWith('order_')).toList();

    for (final item in itemsToRemove) {
      notifier.removeLineItem(item);
    }
  }
}
