import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/data/models/retail_invoice.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:timelines_plus/timelines_plus.dart';

class AdvanceInvoiceTrackingWidget extends StatelessWidget {
  final RetailInvoice invoice;
  const AdvanceInvoiceTrackingWidget(this.invoice, {super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      height: 552,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Palette.stroke, width: 1),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.04),
            offset: const Offset(0, 2),
            blurRadius: 2,
            spreadRadius: -1,
          ),
        ],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Invoice Tracking',
            style:
                textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
          const Gap(32),
          Expanded(
            child: FixedTimeline.tileBuilder(
              theme: TimelineThemeData(
                nodePosition: 0,
                indicatorPosition: 0,
                nodeItemOverlap: false,
                connectorTheme: ConnectorThemeData(
                  thickness: 1,
                  color: Palette.stroke,
                ),
                indicatorTheme: IndicatorThemeData(
                  position: 0.3,
                  size: 10.0,
                  color: Palette.stroke,
                ),
              ),
              builder: TimelineTileBuilder(
                itemCount: 3,
                contentsBuilder: (_, index) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTrackingContent(context, index, invoice),
                      if (index < 2)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Divider(
                            thickness: 1,
                            color: Palette.stroke,
                          ),
                        ),
                    ],
                  );
                },
                indicatorBuilder: (context, index) {
                  final isActive = [
                    true, // Created is always active
                    invoice.approvalStatus.toLowerCase() == 'approved',
                    invoice.isAdvance,
                  ][index];
                  return SvgPicture.asset(
                    '$kSvgDir/shipment/circle.svg',
                    colorFilter: isActive
                        ? const ColorFilter.mode(Colors.green, BlendMode.srcIn)
                        : const ColorFilter.mode(
                            Colors.orange, BlendMode.srcIn),
                  );
                },
                endConnectorBuilder: (_, __) {
                  return SolidLineConnector(
                    color: Palette.stroke,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingContent(
      BuildContext context, int index, RetailInvoice invoice) {
    switch (index) {
      case 0:
        return _buildTrackingStep(
          context,
          'Invoice Created',
          _formatDate(invoice.createdAt),
          isActive: true,
        );
      case 1:
        return _buildTrackingStep(
          context,
          'Approved',
          invoice.approvalStatus.toLowerCase() == 'approved'
              ? _formatDate(invoice.createdAt)
              : '',
          isActive: invoice.approvalStatus.toLowerCase() == 'approved',
          status: invoice.approvalStatus,
        );
      case 2:
        return _buildTrackingStep(
          context,
          'Financed',
          invoice.isAdvance ? _formatDate(invoice.createdAt) : '',
          isActive: invoice.isAdvance,
          status: invoice.isAdvance ? 'Financed' : 'Not Financed',
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildTrackingStep(
    BuildContext context,
    String label,
    String date, {
    bool isActive = false,
    String? status,
    Widget? extraContent,
  }) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: textTheme.labelMedium?.copyWith(fontSize: 16),
          ),
          const Gap(4),
          Row(
            children: [
              if (date.isNotEmpty)
                SvgPicture.asset(
                  '$kSvgDir/shipment/calendar.svg',
                  height: 16,
                  width: 16,
                ),
              const Gap(4),
              Text(
                date,
                style: textTheme.bodyMedium?.copyWith(
                  color: Palette.blackSecondary,
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          if (extraContent != null) ...[
            const Gap(4),
            extraContent,
          ],
          if (status != null && label != 'Invoice Created')
            Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                status,
                style: textTheme.bodySmall?.copyWith(
                  color: isActive ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
