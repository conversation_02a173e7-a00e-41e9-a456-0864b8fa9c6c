import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/business_verification/presentation/controllers/file_upload.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class LogoUploadWidget extends ConsumerStatefulWidget {
  const LogoUploadWidget({super.key, this.updating = false});

  final bool updating;

  @override
  ConsumerState<LogoUploadWidget> createState() => _LogoUploadWidgetState();
}

class _LogoUploadWidgetState extends ConsumerState<LogoUploadWidget>
    with FileUpload {
  String? _selectedImage;
  final _isLoading = ValueNotifier<bool>(false);
  final _disableImageUploadButton = ValueNotifier<bool>(true);

  @override
  void dispose() {
    _isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 380,
          minWidth: 320,
          maxHeight: 520,
        ),
        child: Material(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Tooltip(
                      message: 'Close',
                      preferBelow: false,
                      child: IconButton(
                        icon: SvgPicture.asset(kCloseSvg),
                        onPressed: () => Navigator.of(context).pop(),
                        // tooltip: 'Close',
                      ),
                    ),
                    const Gap(8),
                    Text(
                      widget.updating ? 'Update Logo' : 'Upload Logo',
                      style: textTheme.titleMedium?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const Gap(8),
                Text(
                  'Supported: .jpg, .png, max 1MB',
                  style: textTheme.bodySmall?.copyWith(color: Palette.k6B797C),
                ),
                const Gap(16),
                Center(
                  child: Container(
                    width: 140,
                    height: 140,
                    decoration: BoxDecoration(
                      border: Border.all(color: Palette.stroke),
                      borderRadius: BorderRadius.circular(8),
                      color: Palette.kFCFCFC,
                    ),
                    child: _selectedImage != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.memory(
                              base64Decode(_selectedImage!),
                              fit: BoxFit.contain,
                            ),
                          )
                        : const Icon(Icons.add_photo_alternate, size: 48),
                  ),
                ),
                const Gap(16),
                Center(
                  child: CustomFilledButton(
                    text: 'Choose Image',
                    onPressed: _pickImage,
                    loaderNotifier: _isLoading,
                  ),
                ),
                const Gap(16),
                MultiValueListenableBuilder<bool, bool, Null>(
                  valueListenable1: _disableImageUploadButton,
                  valueListenable2: _isLoading,
                  builder: (context, disabled, loading, _, __) {
                    return CustomFilledButton(
                      text: widget.updating ? 'Update' : 'Upload',
                      disabledNotifier: _disableImageUploadButton,
                      loaderNotifier: _isLoading,
                      onPressed: () async {
                        _isLoading.value = true;
                        final res = await ref
                            .read(uploadLogoUseCaseProvider(_selectedImage!));
                        res.when(
                          success: (_) {
                            ref
                                .read(advanceInvoiceControllerProvider.notifier)
                                .fetchAdvanceInvoiceSettings();
                            Navigator.maybePop(context);
                            // _isLoading.value = false;
                            // Navigator.pop(context, _selectedImage!);
                          },
                          failure: (e, _) {
                            _isLoading.value = false;
                            Toast.apiError(e, context);
                          },
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
          type: FileType.image, allowMultiple: false, withData: true);

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          setState(() {
            _selectedImage = base64Encode(file.bytes!);
            _disableImageUploadButton.value = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        Toast.error("Error picking image: $e", context);
      }
    }
  }
}
