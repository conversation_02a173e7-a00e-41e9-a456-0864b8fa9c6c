import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/email_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/upload_pod_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class PodChoiceDialog extends ConsumerStatefulWidget {
  final String invoiceId;
  final VoidCallback? onPodProcessed;

  const PodChoiceDialog({
    super.key,
    required this.invoiceId,
    this.onPodProcessed,
  });

  @override
  ConsumerState<PodChoiceDialog> createState() => _PodChoiceDialogState();
}

class _PodChoiceDialogState extends ConsumerState<PodChoiceDialog> {
  final _isEmailLoading = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.white,
        child: Container(
          width: 520,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(textTheme),
              const Gap(24),
              _buildDescription(textTheme),
              const Gap(32),
              _buildChoiceButtons(),
              const Gap(24),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Palette.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.receipt_long,
            color: Palette.primary,
            size: 24,
          ),
        ),
        const Gap(12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Proof of Delivery (POD)',
                style: textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
              ),
              const Gap(4),
              Text(
                'Choose how you want to submit your POD',
                style: textTheme.bodyMedium?.copyWith(
                  color: Palette.blackSecondary,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          color: Palette.blackSecondary,
        ),
      ],
    );
  }

  Widget _buildDescription(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Palette.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Palette.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Palette.primary,
            size: 20,
          ),
          const Gap(12),
          Expanded(
            child: Text(
              'You can either upload the POD document directly or send it electronically to the customer\'s email for approval.',
              style: textTheme.bodySmall?.copyWith(
                color: Palette.primaryBlack,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChoiceButtons() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isEmailLoading,
      builder: (context, isLoading, child) {
        return Column(
          children: [
            _buildChoiceOption(
              icon: Icons.upload_file,
              title: 'Upload POD Document',
              description: 'Upload the POD document directly from your device',
              onTap: isLoading ? null : _handleUploadPod,
            ),
            const Gap(16),
            _buildChoiceOption(
              icon: Icons.email_outlined,
              title: 'Send Electronic POD',
              description: 'Send POD via email to customer for approval',
              onTap: isLoading ? null : _handleEmailPod,
              trailing: isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Palette.primary),
                      ),
                    )
                  : Icon(
                      Icons.arrow_forward_ios,
                      color: Palette.blackSecondary,
                      size: 16,
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildChoiceOption({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback? onTap,
    Widget? trailing,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Opacity(
        opacity: onTap == null ? 0.6 : 1.0,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Palette.stroke),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Palette.primary,
                  size: 24,
                ),
              ),
              const Gap(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Palette.primaryBlack,
                          ),
                    ),
                    const Gap(4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Palette.blackSecondary,
                          ),
                    ),
                  ],
                ),
              ),
              trailing ??
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Palette.blackSecondary,
                    size: 16,
                  ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Palette.stroke),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: Text(
              'Cancel',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Palette.primaryBlack,
                  ),
            ),
          ),
        ),
      ],
    );
  }

  void _handleUploadPod() {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UploadPodWidget(
        invoiceId: widget.invoiceId,
        onPodUploaded: widget.onPodProcessed,
      ),
    );
  }

  void _handleEmailPod() async {
    _isEmailLoading.value = true;

    final params = EmailPodParams(invoiceId: widget.invoiceId);
    final response = await ref.read(emailPodUseCaseProvider(params));

    _isEmailLoading.value = false;

    if (!mounted) return;

    response.when(
      success: (data) {
        Navigator.of(context).pop();
        Toast.success('Electronic POD sent successfully', context);
        widget.onPodProcessed?.call();
      },
      failure: (error, code) {
        Toast.apiError(error, context);
      },
    );
  }

  @override
  void dispose() {
    _isEmailLoading.dispose();
    super.dispose();
  }
}
