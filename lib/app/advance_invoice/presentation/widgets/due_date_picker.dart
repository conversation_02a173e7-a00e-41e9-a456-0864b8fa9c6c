import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class DueDatePickerOverlay extends StatefulWidget {
  final DateTime? selectedDate;
  final ValueChanged<DateTime> onDateSelected;
  final int maxMonths;
  final String label;

  const DueDatePickerOverlay({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
    this.maxMonths = 24,
    this.label = 'Payment Due Date',
  });

  @override
  State<DueDatePickerOverlay> createState() => _DueDatePickerOverlayState();
}

class _DueDatePickerOverlayState extends State<DueDatePickerOverlay> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  final GlobalKey _fieldKey = GlobalKey();

  void _showOverlay() {
    final RenderBox renderBox =
        _fieldKey.currentContext!.findRenderObject() as RenderBox;
    final Size fieldSize = renderBox.size;
    final Offset fieldOffset = renderBox.localToGlobal(Offset.zero);
    final double screenHeight = MediaQuery.of(context).size.height;
    const double overlayHeight = 360;
    const double minCalendarWidth = 360;
    final double overlayWidth =
        fieldSize.width < minCalendarWidth ? minCalendarWidth : fieldSize.width;
    final double spaceBelow =
        screenHeight - (fieldOffset.dy + fieldSize.height);
    final double spaceAbove = fieldOffset.dy;

    // Calculate offset for CompositedTransformFollower (like advanced_dropdown.dart)
    Offset menuOffset;
    if (spaceBelow < overlayHeight && spaceAbove > overlayHeight) {
      // Open above
      menuOffset = const Offset(0.0, -(overlayHeight + 4.0));
    } else {
      // Open below
      menuOffset = Offset(0.0, fieldSize.height + 4.0);
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: _closeOverlay,
        child: Stack(
          children: [
            Positioned.fill(
              child: GestureDetector(
                onTap: _closeOverlay,
                child: Container(color: Colors.transparent),
              ),
            ),
            Positioned(
              left: 0,
              top: 0,
              width: overlayWidth,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: menuOffset,
                child: Material(
                  elevation: 8.0,
                  borderRadius: BorderRadius.circular(12),
                  child: GestureDetector(
                    onTap: () {}, // Prevent tap from closing overlay
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Palette.stroke),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(16),
                      width: overlayWidth,
                      height: overlayHeight,
                      child: _buildCalendar(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildCalendar() {
    final now = DateTime.now();
    final lastDate = DateTime(now.year, now.month + widget.maxMonths, now.day);
    final initialMonth = widget.selectedDate ?? now;
    return StatefulBuilder(
      builder: (context, setState) {
        return CalendarDatePicker2(
          config: CalendarDatePicker2Config(
            calendarType: CalendarDatePicker2Type.single,
            firstDate: now,
            lastDate: lastDate,
            selectedDayHighlightColor: Palette.primary,
            controlsHeight: 40,
            dayTextStyle: const TextStyle(fontSize: 16),
            weekdayLabelTextStyle: const TextStyle(fontWeight: FontWeight.w600),
            yearTextStyle: const TextStyle(fontWeight: FontWeight.w600),
            selectedDayTextStyle: const TextStyle(color: Colors.white),
            dayBorderRadius: BorderRadius.circular(8),
            lastMonthIcon:
                SvgPicture.asset(kChevronLeftSvg, width: 20, height: 20),
            nextMonthIcon:
                SvgPicture.asset(kChevronRightSvg, width: 20, height: 20),
            todayTextStyle: const TextStyle(fontWeight: FontWeight.bold),
            disableModePicker: true,
            centerAlignModePicker: true,
            controlsTextStyle: const TextStyle(fontWeight: FontWeight.bold),
          ),
          value: [widget.selectedDate ?? now],
          displayedMonthDate: initialMonth,
          onValueChanged: (dates) {
            if (dates.isNotEmpty) {
              widget.onDateSelected(dates.first);
              _closeOverlay();
            }
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _closeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(widget.label, style: textTheme.bodyMedium),
        const Gap(4),
        CompositedTransformTarget(
          link: _layerLink,
          child: GestureDetector(
            key: _fieldKey,
            onTap: _showOverlay,
            child: Container(
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              decoration: BoxDecoration(
                border: Border.all(color: Palette.stroke),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.selectedDate != null
                        ? widget.selectedDate!.toDate()
                        : 'Select due date',
                    style: textTheme.bodyMedium?.copyWith(
                      color: widget.selectedDate != null
                          ? Palette.primaryBlack
                          : Palette.placeholder,
                    ),
                  ),
                  const Gap(8),
                  Icon(Icons.calendar_today,
                      size: 18,
                      color: widget.selectedDate != null
                          ? Palette.primary
                          : Palette.placeholder),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
