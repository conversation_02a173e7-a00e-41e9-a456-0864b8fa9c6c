import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/action_buttons.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class ItemsOptionsWidget extends ConsumerStatefulWidget {
  const ItemsOptionsWidget({super.key});
  @override
  ConsumerState<ItemsOptionsWidget> createState() =>
      _ServiceOptionsSectionState();
}

class _ServiceOptionsSectionState extends ConsumerState<ItemsOptionsWidget>
    with TickerProviderStateMixin {
  final List<TaxRate> selectedTaxes = [];
  final List<AdditionalCharge> selectedCharges = [];
  bool showAddOptions = false;
  bool showTaxForm = false;
  bool showChargeForm = false;
  bool showTaxDropdown = false;
  bool showChargeDropdown = false;
  String? editingTaxId;
  String? editingChargeId;
  final _taxNameController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _chargeNameController = TextEditingController();
  final _chargeAmountController = TextEditingController();

  // Discount/Note state
  bool showDiscountForm = false;
  bool editingDiscount = false;
  DiscountType selectedType = DiscountType.fixed;
  final _discountValueController = TextEditingController();
  bool showNoteInput = false;
  final _noteController = TextEditingController();
  final _optionsFormController = ExpandableController();

  // Persistent controllers for each input
  final Map<String, TextEditingController> _taxControllers = {};
  final Map<String, TextEditingController> _chargeControllers = {};

  @override
  void dispose() {
    _taxNameController.dispose();
    _taxRateController.dispose();
    _chargeNameController.dispose();
    _chargeAmountController.dispose();
    _discountValueController.dispose();
    _noteController.dispose();
    for (var c in _taxControllers.values) {
      c.dispose();
    }
    for (var c in _chargeControllers.values) {
      c.dispose();
    }
    super.dispose();
  }

  bool _isValidTaxRate(String? value) {
    if (value == null || value.isEmpty) return false;
    final rate = double.tryParse(value.replaceAll(',', '').trim());
    if (rate == null) return false;
    return rate >= 0 && rate <= 100;
  }

  bool _isValidAmount(String? value) {
    if (value == null || value.isEmpty) return false;
    final amount = double.tryParse(value.replaceAll(',', '').trim());
    if (amount == null) return false;
    return amount >= 0;
  }

  double _calculateTaxAmount(TaxRate tax) {
    final lineItems = ref.read(createAdvanceInvoiceProvider).lineItems;
    final subtotal = lineItems.fold<double>(
        0, (sum, item) => sum + (item.quantity * item.unitPrice));
    return (subtotal * (tax.rate ?? 0)) / 100;
  }

  void _startEditingTax(TaxRate tax) {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      editingTaxId = tax.id;
      _taxNameController.text = tax.name;
      _taxRateController.text = tax.rate != null ? tax.rate.toString() : '';
      showTaxForm = true;
      showTaxDropdown = false;
      showChargeForm = false;
      showChargeDropdown = false;
    });
  }

  void _startEditingCharge(AdditionalCharge charge) {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      editingChargeId = charge.id;
      _chargeNameController.text = charge.name;
      _chargeAmountController.text =
          charge.amount != null ? charge.amount.toString() : '';
      showChargeForm = true;
      showChargeDropdown = false;
      showTaxForm = false;
      showTaxDropdown = false;
    });
  }

  void _startEditingDiscount() {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      showDiscountForm = !showDiscountForm;
      showNoteInput = false;
      showTaxDropdown = false;
      showChargeDropdown = false;
      showTaxForm = false;
      showChargeForm = false;
      // Set up discount form state
      final discount = ref.read(createAdvanceInvoiceProvider).discount;
      editingDiscount = discount != null;
      if (discount != null) {
        selectedType = discount.type;
        _discountValueController.text = discount.value.toString();
      } else {
        selectedType = DiscountType.fixed;
        _discountValueController.clear();
      }
    });
  }

  void _startEditingNote() {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      showNoteInput = !showNoteInput;
      showDiscountForm = false;
      showTaxDropdown = false;
      showChargeDropdown = false;
      showTaxForm = false;
      showChargeForm = false;
      // Set up note form state
      final note = ref.read(createAdvanceInvoiceProvider).note;
      _noteController.text = note ?? '';
    });
  }

  void _prepareTaxForm(TaxRate tax) {
    setState(() {
      _taxNameController.text = tax.name;
      _taxRateController.text = tax.rate != null ? tax.rate.toString() : '';
      showTaxForm = true;
      showTaxDropdown = false;
      showChargeForm = false;
      showChargeDropdown = false;
    });
  }

  void _prepareChargeForm(AdditionalCharge charge) {
    setState(() {
      _chargeNameController.text = charge.name;
      _chargeAmountController.text =
          charge.amount != null ? charge.amount.toString() : '';
      showChargeForm = true;
      showChargeDropdown = false;
      showTaxForm = false;
      showTaxDropdown = false;
    });
  }

  void _saveTax() {
    final name = _taxNameController.text.trim();
    final rate =
        double.tryParse(_taxRateController.text.replaceAll(',', '').trim()) ??
            0;

    if (name.isNotEmpty && _isValidTaxRate(_taxRateController.text)) {
      if (editingTaxId != null) {
        // Update existing tax
        final updatedTax = TaxRate(
          id: editingTaxId!,
          name: name,
          rate: rate,
          amount: 0,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateTax(updatedTax);
        setState(() {
          final index = selectedTaxes.indexWhere((t) => t.id == editingTaxId);
          if (index != -1) {
            selectedTaxes[index] = updatedTax;
          }
          editingTaxId = null;
          showTaxForm = false;
          showAddOptions = false;
          _taxNameController.clear();
          _taxRateController.clear();
        });
      } else {
        // Add new tax
        final newTax = TaxRate(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          rate: rate,
          amount: 0,
        );
        ref.read(advanceInvoiceControllerProvider.notifier).addTax(newTax);
        setState(() {
          selectedTaxes.add(newTax);
          showTaxForm = false;
          showAddOptions = false;
          _taxNameController.clear();
          _taxRateController.clear();
        });

        // add tax to the backend in the background
        createTask(newTax);
      }
    }
  }

  Future<void> createTask(TaxRate newTax) async {
    await ref.read(createTaxRateUseCaseProvider(newTax));
  }

  void _saveCharge() {
    final name = _chargeNameController.text.trim();
    final amount = double.tryParse(
            _chargeAmountController.text.replaceAll(',', '').trim()) ??
        0;

    if (name.isNotEmpty && _isValidAmount(_chargeAmountController.text)) {
      if (editingChargeId != null) {
        // Update existing charge
        final updatedCharge = AdditionalCharge(
          id: editingChargeId!,
          name: name,
          amount: amount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateCharge(updatedCharge);
        setState(() {
          final index =
              selectedCharges.indexWhere((c) => c.id == editingChargeId);
          if (index != -1) {
            selectedCharges[index] = updatedCharge;
          }
          editingChargeId = null;
          showChargeForm = false;
          showAddOptions = false;
          _chargeNameController.clear();
          _chargeAmountController.clear();
        });
      } else {
        // Add new charge
        final newCharge = AdditionalCharge(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          amount: amount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addCharge(newCharge);
        setState(() {
          selectedCharges.add(newCharge);
          showChargeForm = false;
          showAddOptions = false;
          _chargeNameController.clear();
          _chargeAmountController.clear();
        });

        // add charge to the backend in the background
        createCharge(newCharge);
      }
    }
  }

  Future<void> createCharge(AdditionalCharge newCharge) async {
    await ref.read(createAdditionalChargeUseCaseProvider(newCharge));
  }

  void _cancelEdit() {
    setState(() {
      editingTaxId = null;
      editingChargeId = null;
      showTaxForm = false;
      showChargeForm = false;
      showAddOptions = false;
      _taxNameController.clear();
      _taxRateController.clear();
      _chargeNameController.clear();
      _chargeAmountController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final settings =
        ref.watch(advanceInvoiceControllerProvider).settings.asData?.value;

    final textTheme = context.textTheme;
    final currencyCode = ref.read(currencyCodeProvider);

    // Filter out already selected taxes and charges
    final availableTaxes = (settings?.taxes ?? [])
        .where((tax) => !selectedTaxes.any((t) => t.name == tax.name))
        .toList();
    final availableCharges = (settings?.charges ?? [])
        .where((charge) => !selectedCharges.any((c) => c.name == charge.name))
        .toList();

    // Ensure persistent controllers for each tax/charge
    for (final tax in selectedTaxes) {
      _taxControllers.putIfAbsent(
          tax.id,
          () => TextEditingController(
              text: tax.rate != null ? tax.rate.toString() : ''));
    }
    for (final charge in selectedCharges) {
      _chargeControllers.putIfAbsent(
          charge.id,
          () => TextEditingController(
              text: charge.amount != null ? charge.amount.toString() : ''));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Items Options',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: Palette.primaryBlack,
          ),
        ),
        const Gap(16),
        SmoothExpandable(
          controller: _optionsFormController,
          title: 'Add Items Option',
          initiallyExpanded: showAddOptions,
          onExpansionChanged: (expanded) {
            setState(() {
              showAddOptions = expanded;
              if (!showAddOptions) {
                showTaxForm = false;
                showChargeForm = false;
                showTaxDropdown = false;
                showChargeDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
              }
            });
          },
          content: _buildServiceOptionsContent(context, textTheme, currencyCode,
              availableTaxes, availableCharges),
        ),
        const Gap(16),
        // Display selected taxes and charges
        _buildSelectedItems(context, textTheme, currencyCode),
      ],
    );
  }

  Widget _buildServiceOptionsContent(
    BuildContext context,
    TextTheme textTheme,
    String currencyCode,
    List<TaxRate> availableTaxes,
    List<AdditionalCharge> availableCharges,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Service option buttons
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildServiceOptionButton(
              'Add Tax',
              'Add a tax to your invoice. Set the rate as a percentage (0 - 100%).',
              showTaxDropdown,
              () => setState(() {
                showTaxDropdown = !showTaxDropdown;
                showChargeDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
                showTaxForm = false;
                showChargeForm = false;
              }),
            ),
            _buildServiceOptionButton(
              'Add Charge',
              'Add a charge to your invoice eg Shipping fee, Processing fee, etc.',
              showChargeDropdown,
              () => setState(() {
                showChargeDropdown = !showChargeDropdown;
                showTaxDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
                showTaxForm = false;
                showChargeForm = false;
              }),
            ),
            _buildServiceOptionButton(
              'Add Discount',
              'Add a discount to your invoice. Choose fixed or percentage.',
              showDiscountForm,
              _startEditingDiscount,
            ),
            _buildServiceOptionButton(
              'Add Note',
              'Add a note to your invoice.',
              showNoteInput,
              _startEditingNote,
            ),
          ],
        ),
        const Gap(16),
        // Tax selection
        if (showTaxDropdown) _buildTaxSelection(availableTaxes),
        // Charge selection
        if (showChargeDropdown) _buildChargeSelection(availableCharges),
        // Forms
        if (showTaxForm) _buildTaxForm(textTheme),
        if (showChargeForm) _buildChargeForm(textTheme, currencyCode),
        if (showDiscountForm) _buildDiscountForm(textTheme, currencyCode),
        if (showNoteInput) _buildNoteForm(textTheme),
      ],
    );
  }

  Widget _buildServiceOptionButton(
      String title, String tooltip, bool showingForm, VoidCallback onPressed) {
    return Tooltip(
      message: tooltip,
      child: OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          minimumSize: const Size(0, 40),
          side: BorderSide(color: Palette.stroke),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onPressed: onPressed,
        icon: Icon(showingForm ? Icons.remove : Icons.add,
            size: 16, color: Palette.primary),
        label: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Palette.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildTaxSelection(List<TaxRate> availableTaxes) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Palette.kF7F7F7,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Tax',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
          ),
          const Gap(12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...availableTaxes.map((tax) => ActionChip(
                    backgroundColor: Colors.white,
                    side: BorderSide(color: Palette.stroke),
                    label: Text(
                      tax.name,
                      style: const TextStyle(fontSize: 13),
                    ),
                    onPressed: () => _prepareTaxForm(tax),
                  )),
              ActionChip(
                backgroundColor: Palette.primary.withValues(alpha: 0.1),
                side: BorderSide(color: Palette.primary),
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, size: 16, color: Palette.primary),
                    const Gap(4),
                    Text(
                      'Add new tax',
                      style: TextStyle(
                        fontSize: 13,
                        color: Palette.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                onPressed: () {
                  setState(() {
                    showTaxForm = true;
                    showTaxDropdown = false;
                    _taxNameController.clear();
                    _taxRateController.clear();
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChargeSelection(List<AdditionalCharge> availableCharges) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Palette.kF7F7F7,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Charge',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
          ),
          const Gap(12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...availableCharges.map((charge) => ActionChip(
                    backgroundColor: Colors.white,
                    side: BorderSide(color: Palette.stroke),
                    label: Text(
                      charge.name,
                      style: const TextStyle(fontSize: 13),
                    ),
                    onPressed: () => _prepareChargeForm(charge),
                  )),
              ActionChip(
                backgroundColor: Palette.primary.withValues(alpha: 0.1),
                side: BorderSide(color: Palette.primary),
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, size: 16, color: Palette.primary),
                    const Gap(4),
                    Text(
                      'Add new charge',
                      style: TextStyle(
                        fontSize: 13,
                        color: Palette.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                onPressed: () {
                  setState(() {
                    showChargeForm = true;
                    showChargeDropdown = false;
                    _chargeNameController.clear();
                    _chargeAmountController.clear();
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTaxForm(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            editingTaxId != null ? 'Edit Tax' : 'Add New Tax',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          Row(
            children: [
              Expanded(
                child: CompactInput(
                  controller: _taxNameController,
                  hint: 'Tax Name',
                  autofocus: _taxNameController.text.isEmpty,
                ),
              ),
              const Gap(12),
              Expanded(
                child: CompactInput(
                  controller: _taxRateController,
                  hint: 'Rate (0 - 100%)',
                  suffix: '%',
                  keyboardType: TextInputType.number,
                  inputFormatters: [Validators.formatRange(0, 100)],
                  autofocus: _taxNameController.text.isNotEmpty,
                  onEditingComplete: _saveTax,
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CancelButton(
                onPressed: _cancelEdit,
                text: 'Cancel',
              ),
              const Gap(8),
              SaveButton(
                onPressed: _saveTax,
                text: editingTaxId != null ? 'Update' : 'Save',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChargeForm(TextTheme textTheme, String currencyCode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            editingChargeId != null ? 'Edit Charge' : 'Add New Charge',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          Row(
            children: [
              Expanded(
                child: CompactInput(
                  controller: _chargeNameController,
                  hint: 'Charge Name',
                  autofocus: _chargeNameController.text.isEmpty,
                ),
              ),
              const Gap(12),
              Expanded(
                child: CompactInput(
                  controller: _chargeAmountController,
                  hint: 'Amount',
                  prefix: '${CurrencyWidget.symbol(context, currencyCode)} ',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    Validators.formatCurrency(decimalPlaces: 2)
                  ],
                  autofocus: _chargeNameController.text.isNotEmpty,
                  onEditingComplete: _saveCharge,
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CancelButton(
                onPressed: _cancelEdit,
                text: 'Cancel',
              ),
              const Gap(8),
              SaveButton(
                onPressed: _saveCharge,
                text: editingChargeId != null ? 'Update' : 'Save',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountForm(TextTheme textTheme, String currencyCode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            editingDiscount ? 'Edit Discount' : 'Add Discount',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          Row(
            children: [
              Expanded(
                child: AdvancedDropdown<DiscountType>(
                  selectedValue: selectedType,
                  options: DiscountType.values,
                  hint: 'Type',
                  itemToString: (type) =>
                      type == DiscountType.fixed ? 'Fixed' : 'Percentage',
                  onChanged: (type) {
                    if (type != null) {
                      setState(() {
                        selectedType = type;
                        _discountValueController.clear();
                      });
                    }
                  },
                  searchable: false,
                  menuMaxHeight: 120,
                  height: 34,
                ),
              ),
              const Gap(12),
              Expanded(
                child: CompactInput(
                  controller: _discountValueController,
                  hint: selectedType == DiscountType.fixed
                      ? 'Amount'
                      : 'Value (%)',
                  prefix: selectedType == DiscountType.fixed
                      ? '${CurrencyWidget.symbol(context, currencyCode)} '
                      : null,
                  suffix: selectedType == DiscountType.percentage ? '%' : null,
                  keyboardType: TextInputType.number,
                  inputFormatters: selectedType == DiscountType.percentage
                      ? [Validators.formatRange(0, 100)]
                      : [Validators.formatCurrency(decimalPlaces: 2)],
                  onEditingComplete: _saveDiscount,
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CancelButton(
                onPressed: () {
                  setState(() {
                    showDiscountForm = false;
                    editingDiscount = false;
                    _discountValueController.clear();
                  });
                },
                text: 'Cancel',
              ),
              const Gap(8),
              SaveButton(
                onPressed: _saveDiscount,
                text: editingDiscount ? 'Update' : 'Save',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNoteForm(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            ref.read(createAdvanceInvoiceProvider).note != null
                ? 'Edit Note'
                : 'Add Note',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          CompactInput(
            controller: _noteController,
            hint: 'Enter note...',
            autofocus: true,
            height: 34,
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CancelButton(
                onPressed: () {
                  setState(() {
                    showNoteInput = false;
                    _noteController.clear();
                  });
                },
                text: 'Cancel',
              ),
              const Gap(8),
              SaveButton(
                onPressed: _saveNote,
                text: ref.read(createAdvanceInvoiceProvider).note != null
                    ? 'Update'
                    : 'Save',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedItems(
      BuildContext context, TextTheme textTheme, String currencyCode) {
    final selectedTaxes = ref.watch(createAdvanceInvoiceProvider).taxes ?? [];
    final selectedCharges =
        ref.watch(createAdvanceInvoiceProvider).charges ?? [];
    final params = ref.watch(createAdvanceInvoiceProvider);

    if (selectedTaxes.isEmpty &&
        selectedCharges.isEmpty &&
        (params.discount == null) &&
        (params.note == null || params.note!.isEmpty)) {
      return Center(
        child: Text(
          'No service options added yet',
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.placeholder,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    return Column(
      children: [
        // Selected Taxes
        ...selectedTaxes.map((tax) => ListTile(
              dense: true,
              title: Text(
                tax.name,
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
              ),
              subtitle: Text(
                'Rate: ${tax.rate}%',
                style: textTheme.bodySmall?.copyWith(
                  color: Palette.blackSecondary,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Consumer(
                    builder: (context, ref, _) {
                      ref.watch(createAdvanceInvoiceProvider);
                      return Text(
                        CurrencyWidget.value(
                            context, currencyCode, _calculateTaxAmount(tax)),
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Palette.primaryBlack,
                        ),
                      );
                    },
                  ),
                  const Gap(8),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                    // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                    onPressed: () => _startEditingTax(tax),
                  ),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                    // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                    onPressed: () {
                      ref
                          .read(advanceInvoiceControllerProvider.notifier)
                          .removeTax(tax);
                    },
                  ),
                ],
              ),
              contentPadding: EdgeInsets.zero,
            )),
        // Selected Charges
        ...selectedCharges.map((charge) => ListTile(
              dense: true,
              title: Text(
                charge.name,
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
              ),
              subtitle: Text(
                'Amount: ${CurrencyWidget.value(context, currencyCode, charge.amount ?? 0)}',
                style: textTheme.bodySmall?.copyWith(
                  color: Palette.blackSecondary,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    CurrencyWidget.value(
                        context, currencyCode, charge.amount ?? 0),
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
                  ),
                  const Gap(8),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                    // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                    onPressed: () => _startEditingCharge(charge),
                  ),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                    // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                    onPressed: () {
                      ref
                          .read(advanceInvoiceControllerProvider.notifier)
                          .removeCharge(charge);
                    },
                  ),
                ],
              ),
              contentPadding: EdgeInsets.zero,
            )),
        // Selected Discount
        if (params.discount != null)
          ListTile(
            dense: true,
            title: Text(
              'Discount',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.primaryBlack,
              ),
            ),
            subtitle: Text(
              'Type: ${params.discount!.type.name}',
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Palette.blackSecondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    params.discount!.type == DiscountType.fixed
                        ? CurrencyWidget.value(
                            context, currencyCode, params.discount!.value)
                        : '${params.discount!.value}%',
                    style: textTheme.bodySmall?.copyWith(
                      color: Palette.blackSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Gap(8),
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                  // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                  onPressed: _startEditingDiscount,
                ),
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                  // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                  onPressed: () {
                    ref
                        .read(advanceInvoiceControllerProvider.notifier)
                        .removeDiscount();
                  },
                ),
              ],
            ),
            contentPadding: EdgeInsets.zero,
          ),
        // Selected Note
        if (params.note != null && params.note!.isNotEmpty)
          ListTile(
            dense: true,
            title: Text(
              'Note',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.primaryBlack,
              ),
            ),
            subtitle: Text(
              params.note!,
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                  // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                  onPressed: _startEditingNote,
                ),
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                  // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                  onPressed: () {
                    ref
                        .read(advanceInvoiceControllerProvider.notifier)
                        .removeNote();
                  },
                ),
              ],
            ),
            isThreeLine: true,
            contentPadding: EdgeInsets.zero,
          ),
      ],
    );
  }

  void _saveDiscount() {
    final value = num.tryParse(
            _discountValueController.text.replaceAll(',', '').trim()) ??
        0;
    if (value > 0 ||
        (selectedType == DiscountType.percentage &&
            value >= 0 &&
            value <= 100)) {
      final newDiscount = Discount(type: selectedType, value: value);
      if (editingDiscount) {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateDiscount(newDiscount);
      } else {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addDiscount(newDiscount);
      }
      setState(() {
        showDiscountForm = false;
        editingDiscount = false;
        _discountValueController.clear();
      });
    }
  }

  void _saveNote() {
    final value = _noteController.text.trim();
    if (value.isNotEmpty) {
      final note = ref.read(createAdvanceInvoiceProvider).note;
      if (note != null) {
        ref.read(advanceInvoiceControllerProvider.notifier).updateNote(value);
      } else {
        ref.read(advanceInvoiceControllerProvider.notifier).addNote(value);
      }
      setState(() {
        showNoteInput = false;
        _noteController.clear();
      });
    }
  }
}
