import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/advance_invoice/data/models/retail_invoice.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/accept_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AcceptPodScreen extends ConsumerStatefulWidget {
  final String invoiceId;
  final String token;

  const AcceptPodScreen({
    super.key,
    required this.invoiceId,
    required this.token,
  });

  @override
  ConsumerState<AcceptPodScreen> createState() => _AcceptPodScreenState();
}

class _AcceptPodScreenState extends ConsumerState<AcceptPodScreen> {
  final ValueNotifier<bool> _isLoading = ValueNotifier(false);
  final ValueNotifier<bool> _isLoadingInvoice = ValueNotifier(true);
  RetailInvoice? _invoice;

  @override
  void initState() {
    super.initState();
    _fetchInvoice();
  }

  Future<void> _fetchInvoice() async {
    try {
      final response =
          await ref.read(fetchAdvanceInvoiceUseCaseProvider(widget.invoiceId));

      if (mounted) {
        response.when(
          success: (invoice) {
            _invoice = invoice;
            _isLoadingInvoice.value = false;
          },
          failure: (error, code) {
            _isLoadingInvoice.value = false;
            Toast.apiError(error, context);
          },
        );
      }
    } catch (e) {
      if (mounted) {
        _isLoadingInvoice.value = false;
        Toast.error('Failed to load invoice details', context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: ValueListenableBuilder<bool>(
          valueListenable: _isLoadingInvoice,
          builder: (context, isLoadingInvoice, _) {
            if (isLoadingInvoice) {
              return Container(
                color: Colors.grey.shade50,
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      Gap(16),
                      Text(
                        'Loading invoice details...',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildHeader(textTheme),
                  const Gap(24),
                  if (_invoice != null) ...[
                    _buildInvoiceDetails(textTheme),
                    const Gap(24),
                  ],
                  _buildDescription(textTheme),
                  const Gap(32),
                  _buildActionButtons(),
                  const Gap(20), // Bottom padding
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildInvoiceDetails(TextTheme textTheme) {
    if (_invoice == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Invoice Details',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          _buildDetailRow(
              'Invoice Number', _invoice!.invoiceNumber.toString(), textTheme),
          const Gap(8),
          _buildDetailRow(
              'Amount',
              '${_invoice!.currency.symbol}${_invoice!.total.toStringAsFixed(2)}',
              textTheme),
          const Gap(8),
          _buildDetailRow(
              'Recipient', _invoice!.recipientBusinessName ?? 'N/A', textTheme),
          const Gap(8),
          _buildDetailRow(
              'Issuer', _invoice!.issuerBusinessName ?? 'N/A', textTheme),
          const Gap(8),
          _buildDetailRow(
              'Created Date',
              '${_invoice!.createdAt.day}/${_invoice!.createdAt.month}/${_invoice!.createdAt.year}',
              textTheme),
          const Gap(8),
          _buildDetailRow('Status', _invoice!.status, textTheme),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, TextTheme textTheme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Palette.primary.withValues(alpha: 0.1),
            Palette.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Palette.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Palette.primary.withValues(alpha: 0.15),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.verified_outlined,
              color: Palette.primary,
              size: 40,
            ),
          ),
          const Gap(20),
          Text(
            'Proof of Delivery Approval',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: Palette.primaryBlack,
            ),
            textAlign: TextAlign.center,
          ),
          const Gap(8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Palette.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Invoice ID: ${widget.invoiceId}',
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.shade200,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.info_outline,
              color: Colors.blue.shade700,
              size: 28,
            ),
          ),
          const Gap(16),
          Text(
            'Important Information',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.blue.shade800,
            ),
          ),
          const Gap(12),
          Text(
            'You have received a Proof of Delivery (POD) request for this invoice. By clicking "Approve", you confirm that you have received the goods/services as described in the invoice.',
            style: textTheme.bodyMedium?.copyWith(
              color: Colors.blue.shade700,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
          const Gap(16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.amber.shade200),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.amber.shade700,
                  size: 20,
                ),
                const Gap(8),
                Expanded(
                  child: Text(
                    'Please ensure you have received all items before approving.',
                    style: textTheme.bodySmall?.copyWith(
                      color: Colors.amber.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isLoading,
      builder: (context, isLoading, _) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Palette.stroke),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              SizedBox(
                width: double.infinity,
                height: 52,
                child: ElevatedButton(
                  onPressed: isLoading ? null : _handleAcceptPod,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    disabledBackgroundColor: Colors.grey.shade300,
                  ),
                  child: isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.check_circle_outline, size: 20),
                            const Gap(8),
                            Text(
                              'Approve POD',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                            ),
                          ],
                        ),
                ),
              ),
              const Gap(12),
              SizedBox(
                width: double.infinity,
                height: 52,
                child: OutlinedButton(
                  onPressed: isLoading ? null : () => context.pop(),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.close, size: 20, color: Colors.grey.shade600),
                      const Gap(8),
                      Text(
                        'Cancel',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade600,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _handleAcceptPod() async {
    _isLoading.value = true;

    final params = AcceptPodParams(
      invoiceId: widget.invoiceId,
      token: widget.token,
    );

    final response = await ref.read(acceptPodUseCaseProvider(params));

    _isLoading.value = false;

    if (!mounted) return;

    response.when(
      success: (data) {
        Toast.success('POD approved successfully', context);
        context.pop();
      },
      failure: (error, code) {
        Toast.apiError(error, context);
      },
    );
  }

  @override
  void dispose() {
    _isLoading.dispose();
    _isLoadingInvoice.dispose();
    super.dispose();
  }
}
