import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/accept_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AcceptPodScreen extends ConsumerStatefulWidget {
  final String invoiceId;
  final String token;

  const AcceptPodScreen({
    super.key,
    required this.invoiceId,
    required this.token,
  });

  @override
  ConsumerState<AcceptPodScreen> createState() => _AcceptPodScreenState();
}

class _AcceptPodScreenState extends ConsumerState<AcceptPodScreen> {
  final ValueNotifier<bool> _isLoading = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildHeader(textTheme),
                    const Gap(32),
                    _buildDescription(textTheme),
                    const Gap(48),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Palette.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.receipt_long,
            color: Palette.primary,
            size: 48,
          ),
        ),
        const Gap(24),
        Text(
          'Approve Proof of Delivery',
          style: textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Palette.primaryBlack,
          ),
          textAlign: TextAlign.center,
        ),
        const Gap(8),
        Text(
          'Invoice ID: ${widget.invoiceId}',
          style: textTheme.bodyLarge?.copyWith(
            color: Palette.blackSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDescription(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Palette.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Palette.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            color: Palette.primary,
            size: 24,
          ),
          const Gap(12),
          Text(
            'You have received a Proof of Delivery (POD) request for this invoice. By clicking "Approve", you confirm that you have received the goods/services as described in the invoice.',
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isLoading,
      builder: (context, isLoading, _) {
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: CustomFilledButton(
                onPressed: _handleAcceptPod,
                loaderNotifier: _isLoading,
                text: 'Approve POD',
              ),
            ),
            const Gap(16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: isLoading ? null : () => context.pop(),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Palette.stroke),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'Cancel',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Palette.primaryBlack,
                      ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleAcceptPod() async {
    _isLoading.value = true;

    final params = AcceptPodParams(
      invoiceId: widget.invoiceId,
      token: widget.token,
    );

    final response = await ref.read(acceptPodUseCaseProvider(params));

    _isLoading.value = false;

    if (!mounted) return;

    response.when(
      success: (data) {
        Toast.success('POD approved successfully', context);
        context.pop();
      },
      failure: (error, code) {
        Toast.apiError(error, context);
      },
    );
  }

  @override
  void dispose() {
    _isLoading.dispose();
    super.dispose();
  }
}
