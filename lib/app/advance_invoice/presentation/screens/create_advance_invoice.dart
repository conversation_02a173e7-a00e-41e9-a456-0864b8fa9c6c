import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/advance_invoice_preview.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/raise_advance_invoice.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_action_bar.dart';
import 'package:td_procurement/app/order/presentation/widgets/outlet_search_auto_complete.dart';
import 'package:td_procurement/app/order/presentation/widgets/product_search_auto_complete.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

class CreateAdvanceInvoiceScreen extends ConsumerStatefulWidget {
  const CreateAdvanceInvoiceScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      CreateAdvanceInvoiceScreenState();
}

class CreateAdvanceInvoiceScreenState
    extends ConsumerState<CreateAdvanceInvoiceScreen> {
  final rightButton1 = ValueNotifier<bool>(false);
  final rightButton2 = ValueNotifier<bool>(false);

  final randomNumber = generateRandomId();

  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      final notifier = ref.read(advanceInvoiceControllerProvider.notifier);
      notifier.fetchAdvanceInvoiceSettings();
      notifier.fetchLoanContract(forced: true);
      notifier.fetchBankAccounts();
      notifier.fetchSettlementBanks();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Listen for customer changes and update only recipientOutletId
    ref.listen(selectedCustomerProvider, (prev, next) {
      if (next != null && next.id != null) {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateRecipientCustomer(next);
      }
    });

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, __) {
        Future.microtask(() {
          resetSalesOrderCustomer(ref);
          ref
              .read(advanceInvoiceControllerProvider.notifier)
              .resetCreateAdvanceInvoiceParams();
        });
      },
      child: GestureDetector(
        // reset focused filed on export cart items
        onTap: () => ref.read(focusedFieldProvider.notifier).state = null,
        child: Scaffold(
          body: Column(
            children: [
              MultiValueListenableBuilder<bool, bool, bool>(
                valueListenable1: rightButton1,
                valueListenable2: rightButton2,
                builder: (context, loading1, loading2, _, __) {
                  return Consumer(
                    builder: (context, ref, child) {
                      final isComplete =
                          ref.watch(createAdvanceInvoiceProvider).isComplete;
                      return OrderActionBarWidget(
                        // leftText: 'Create Advance Invoice',
                        leftText: 'Create Advance Invoice',
                        leftIconAction: () => context.pop(),
                        rightButton2Text: 'Create Advance Invoice',
                        rightButton2Disabled: !isComplete,
                        rightButton2Loading: loading2,
                        rightButton2Action: _createAdvanceInvoice,
                      );
                    },
                  );
                },
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 24.0, horizontal: 0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Expanded(
                          child: RaiseAdvanceInvoiceWidget(),
                        ),
                        Expanded(
                          child: Container(
                            color: Palette.kFCFCFC,
                            child: AdvanceInvoicePreviewWidget(
                                randomNumber: randomNumber.substring(0, 9)),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _createAdvanceInvoice() async {
    rightButton2.value = true;

    var params = ref.read(createAdvanceInvoiceProvider);

    // setup currency
    params = params.copyWith(
        currency: Currency(
            iso: params.currencyCode,
            symbol: CurrencyWidget.symbol(context, params.currencyCode)));

    final res = await ref.read(createRetailInvoiceUseCaseProvider(params));

    res.when(
      success: (data) {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .fetchAdvanceInvoices(
                ref
                    .read(advanceInvoiceControllerProvider)
                    .fetchAdvanceInvoiceParams,
                forced: true);
        context.goNamed(kAdvanceInvoiceListRoute);
        Toast.success('Advance invoice created successfully', context,
            title: 'Advance invoice created');
        rightButton2.value = false;
      },
      failure: (e, s) {
        Toast.apiError(e, context, title: 'Cannot create advance invoice');
        rightButton2.value = false;
      },
    );
  }
}
