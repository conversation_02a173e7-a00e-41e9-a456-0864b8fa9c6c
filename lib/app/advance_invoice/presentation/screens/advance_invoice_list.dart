import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_state.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/advance_invoice_table.dart';
import 'package:td_procurement/app/order/presentation/widgets/orders_table.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/date_picker.dart';
import 'package:td_procurement/core/models/optional.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class AdvanceInvoiceListScreen extends ConsumerStatefulWidget {
  const AdvanceInvoiceListScreen({super.key});

  @override
  ConsumerState<AdvanceInvoiceListScreen> createState() =>
      _AdvanceInvoiceListScreenState();
}

class _AdvanceInvoiceListScreenState
    extends ConsumerState<AdvanceInvoiceListScreen> {
  final _searchController = TextEditingController();
  Timer? _searchDebounceTimer;
  int _activeStatusIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeState();
    Future.microtask(() => _fetchInitialData());
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  void _initializeState() {
    final invoiceState = ref.read(advanceInvoiceControllerProvider);
    _activeStatusIndex = invoiceState.fetchAdvanceInvoiceParams.activeIndex;
    _searchController.text = invoiceState.fetchAdvanceInvoiceParams.searchText;
  }

  void _fetchInitialData() {
    final params =
        ref.read(advanceInvoiceControllerProvider).fetchAdvanceInvoiceParams;
    ref
        .read(advanceInvoiceControllerProvider.notifier)
        .fetchAdvanceInvoices(params);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final invoiceState = ref.watch(advanceInvoiceControllerProvider);
    final statusOptions = [
      'All Invoices',
      'Pending',
      'Accepted',
      'Processing',
      'Completed'
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(textTheme),
        _buildStatusFilterBar(invoiceState, statusOptions),
        _buildSearchAndOptionsBar(invoiceState),
        _buildInvoicesContent(invoiceState),
      ],
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Padding(
          padding: const EdgeInsets.only(left: 40),
          child: Text(
            'Advance Invoices',
            style: textTheme.headlineMedium,
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildStatusFilterBar(
    AdvanceInvoiceState invoiceState,
    List<String> statusOptions,
  ) {
    return Container(
      padding: const EdgeInsets.only(left: 40, bottom: 12),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Palette.stroke)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            flex: 3,
            child: _buildStatusFilters(statusOptions),
          ),
          Flexible(
            flex: 2,
            child: _buildDateFilterAndOptions(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilters(List<String> statusOptions) {
    return Row(
      children: statusOptions.map((status) {
        final index = statusOptions.indexOf(status);
        final isSelected = _activeStatusIndex == index;

        return Flexible(
          child: StatusFilteringWidget(
            status,
            isSelected,
            width: 130,
            padding: const EdgeInsets.symmetric(vertical: 19),
            onPressed: () => _handleStatusFilter(index),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDateFilterAndOptions() {
    final invoiceState = ref.watch(advanceInvoiceControllerProvider);

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        DatePickerWidget(
          initialDate:
              invoiceState.fetchAdvanceInvoiceParams.selectedDates.isNotEmpty
                  ? invoiceState.fetchAdvanceInvoiceParams.selectedDates[0]
                  : null,
          selectedStartDate:
              invoiceState.fetchAdvanceInvoiceParams.selectedDates.isNotEmpty
                  ? invoiceState.fetchAdvanceInvoiceParams.selectedDates[0]
                  : null,
          selectedEndDate:
              invoiceState.fetchAdvanceInvoiceParams.selectedDates.length > 1
                  ? invoiceState.fetchAdvanceInvoiceParams.selectedDates[1]
                  : null,
          getValueLabel: () => getFormattedDateRange(
            invoiceState.fetchAdvanceInvoiceParams.selectedDates.isNotEmpty
                ? invoiceState.fetchAdvanceInvoiceParams.selectedDates[0]
                : null,
            invoiceState.fetchAdvanceInvoiceParams.selectedDates.length > 1
                ? invoiceState.fetchAdvanceInvoiceParams.selectedDates[1]
                : null,
          ),
          onDatesSelected: _handleDateSelection,
          onCancel: _handleDateFilterClear,
        ),
        const Gap(20),
      ],
    );
  }

  Widget _buildSearchAndOptionsBar(AdvanceInvoiceState invoiceState) {
    return Row(
      children: [
        Expanded(
          child: _buildSearchBar(invoiceState),
        ),
      ],
    );
  }

  Widget _buildSearchBar(AdvanceInvoiceState invoiceState) {
    return Container(
      height: 48,
      padding: const EdgeInsets.only(left: 40),
      child: TextFormField(
        controller: _searchController,
        decoration: _buildSearchInputDecoration(),
        cursorColor: Palette.strokePressed,
        cursorHeight: 18,
        onChanged: _handleSearchInput,
      ),
    );
  }

  InputDecoration _buildSearchInputDecoration() {
    return InputDecoration(
      hintText: 'Type to search by invoice number',
      prefixIcon: Padding(
        padding: const EdgeInsets.only(top: 2.0, right: 10),
        child: SvgPicture.asset(
          '$kSvgDir/order/search.svg',
          fit: BoxFit.cover,
        ),
      ),
      prefixIconConstraints: const BoxConstraints(
        minWidth: 22,
        minHeight: 22,
      ),
      hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Palette.placeholder,
            fontWeight: FontWeight.w400,
          ),
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
    );
  }

  void _handleStatusFilter(int index) {
    if (_activeStatusIndex == index) return;

    setState(() => _activeStatusIndex = index);
    _updateInvoicesForStatus(index);
  }

  void _updateInvoicesForStatus(int index) {
    final statusMap = [
      'all',
      'awaiting',
      'accepted',
      'processing',
      'completed'
    ];

    final status = statusMap[index];

    final currentParams =
        ref.read(advanceInvoiceControllerProvider).fetchAdvanceInvoiceParams;
    final newParams = currentParams.copyWith(
      invoiceStatus: status,
      activeIndex: index,
      currentPage: 1,
    );

    ref.read(advanceInvoiceControllerProvider.notifier).fetchAdvanceInvoices(
          newParams,
          forced: true,
        );
  }

  void _handleDateSelection({DateTime? startDate, DateTime? endDate}) {
    final currentParams =
        ref.read(advanceInvoiceControllerProvider).fetchAdvanceInvoiceParams;
    final newParams = currentParams.copyWith(
      selectedDates: Optional([startDate, endDate]),
      currentPage: 1,
    );

    ref.read(advanceInvoiceControllerProvider.notifier).fetchAdvanceInvoices(
          newParams,
          forced: true,
        );
  }

  Future<void> _handleDateFilterClear() async {
    final currentParams =
        ref.read(advanceInvoiceControllerProvider).fetchAdvanceInvoiceParams;

    if (!currentParams.selectedDates.any((date) => date != null)) return;

    final params = FetchAdvanceInvoiceParams.defaultValue().copyWith(
      invoiceStatus: currentParams.invoiceStatus,
      searchText: currentParams.searchText,
      activeIndex: currentParams.activeIndex,
    );

    final invoiceNotifier = ref.read(advanceInvoiceControllerProvider.notifier);
    invoiceNotifier.setAdvanceInvoiceDateFilter(null, null);
    await Future.delayed(Duration.zero);

    invoiceNotifier.fetchAdvanceInvoices(
      params,
      forced: true,
    );
  }

  void _handleSearchInput(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(
      const Duration(milliseconds: 500),
      () => _performSearch(query.trim()),
    );
  }

  void _performSearch(String query) {
    final currentParams =
        ref.read(advanceInvoiceControllerProvider).fetchAdvanceInvoiceParams;
    final newParams = currentParams.copyWith(
      searchText: query,
      currentPage: 1,
    );

    ref.read(advanceInvoiceControllerProvider.notifier).fetchAdvanceInvoices(
          newParams,
          forced: true,
        );
  }

  Widget _buildInvoicesContent(AdvanceInvoiceState invoiceState) {
    return Expanded(
      child: invoiceState.advanceInvoices.when(
        data: (invoices) => invoices.isEmpty
            ? _buildEmptyState()
            : _buildInvoicesList(invoices),
        loading: () => _buildLoadingState(),
        error: (e, s) => _buildErrorState(e),
      ),
    );
  }

  Widget _buildInvoicesList(List<RetailInvoice> invoices) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTableHeader(),
        Expanded(child: AdvanceInvoiceTableWidget(invoices)),
        const Gap(10),
        const AdvanceInvoicePagination(),
        const Gap(10),
      ],
    );
  }

  Widget _buildTableHeader() {
    final textTheme = Theme.of(context).textTheme;

    return buildTableHeader(
      tableHeader: Table(
        columnWidths: const {
          0: FlexColumnWidth(0.31),
          1: FlexColumnWidth(1.4), // Invoice Number
          2: FlexColumnWidth(1.4), // Amount
          3: FlexColumnWidth(1), // Financed
          4: FlexColumnWidth(1.4), // POD Status
          5: FlexColumnWidth(1.4), // Created Date
          6: FlexColumnWidth(1.2), // Status
          7: FlexColumnWidth(0.5), // Actions
        },
        children: [
          TableRow(
            children: [
              Container(),
              buildHeaderCell('Invoice Number', textTheme),
              buildHeaderCell('Amount', textTheme),
              buildHeaderCell('Financed', textTheme),
              buildHeaderCell('Proof Of Delivery', textTheme),
              buildHeaderCell('Created on', textTheme),
              buildHeaderCell('Status', textTheme),
              Container(), // Empty space for actions
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: AdvanceInvoiceTableWidget(
              List.filled(10, RetailInvoice.defaultValue()),
            ),
          ),
          const AdvanceInvoicePagination(),
          const Gap(20),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        EmptyWidget(
          icon: kReceiptSvg,
          routeName: kCreateAdvanceInvoiceRoute,
          title: 'No Advance Invoices',
          routeInfo: 'Create a new advance invoice →',
          subTitle:
              'There are no advance invoices matching your filter criteria',
        ),
      ],
    );
  }

  Widget _buildErrorState(Object error) {
    return FailureWidget(
      fullScreen: true,
      heightFactor: 0.7,
      e: error,
      retry: () {
        final params = ref
            .read(advanceInvoiceControllerProvider)
            .fetchAdvanceInvoiceParams;
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .fetchAdvanceInvoices(params);
      },
    );
  }
}

class AdvanceInvoicePagination extends ConsumerWidget {
  const AdvanceInvoicePagination({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final invoiceState = ref.watch(advanceInvoiceControllerProvider);
    final params = invoiceState.fetchAdvanceInvoiceParams;
    final perPage = params.perPage;
    final start = ((params.currentPage - 1) * perPage) + 1;
    final end = params.currentPage * perPage;
    final currentPageData = invoiceState.advanceInvoices.maybeWhen(
      data: (data) => data,
      orElse: () => [],
    );
    final totalCount =
        params.totalCount > 0 ? params.totalCount : currentPageData.length;

    return Padding(
      padding: const EdgeInsets.only(left: 38, right: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$start - ${end > totalCount ? totalCount : end} of $totalCount',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: Palette.blackSecondary),
          ),
          if (params.totalPages > 0)
            IntrinsicWidth(
              child: NumberPagination(
                onPageChanged: (int pageNumber) {
                  ref
                      .read(advanceInvoiceControllerProvider.notifier)
                      .fetchAdvanceInvoices(
                          params.copyWith(currentPage: pageNumber),
                          forced: true);
                },
                visiblePagesCount: perPage > 5 || perPage == 0 ? 5 : perPage,
                buttonElevation: 0.5,
                totalPages: params.totalPages,
                currentPage: params.currentPage,
                buttonRadius: 6,
                selectedButtonColor: Palette.primaryBlack,
                selectedNumberColor: Colors.white,
                unSelectedButtonColor: Colors.white,
                unSelectedNumberColor: Palette.blackSecondary,
                fontSize: 14,
                numberButtonSize: const Size(35, 35),
                controlButtonSize: const Size(40, 40),
                firstPageIcon: SvgPicture.asset(kDoubleChevronLeftSvg,
                    width: 25, height: 25),
                previousPageIcon: SvgPicture.asset(kChevronLeftSvg,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn),
                    width: 16,
                    height: 16),
                lastPageIcon: SvgPicture.asset(kDoubleChevronRightSvg,
                    width: 25, height: 25),
                nextPageIcon: SvgPicture.asset(kChevronRightSvg,
                    width: 16,
                    height: 16,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn)),
              ),
            ),
        ],
      ),
    );
  }
}
