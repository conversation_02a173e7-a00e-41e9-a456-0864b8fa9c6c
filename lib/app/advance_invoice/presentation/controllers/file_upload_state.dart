import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/business_verification/domain/entities/file_upload_params.dart';

class FileUploadState {
  final Map<String, FileUploadParams> files;
  final bool isLoading;

  const FileUploadState({
    this.files = const {},
    this.isLoading = false,
  });

  FileUploadState copyWith({
    Map<String, FileUploadParams>? files,
    bool? isLoading,
  }) {
    return FileUploadState(
      files: files ?? this.files,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class FileUploadController extends Notifier<FileUploadState> {
  @override
  FileUploadState build() {
    return const FileUploadState();
  }

  void addFile(String key, FileUploadParams file) {
    final newFiles = Map<String, FileUploadParams>.from(state.files);
    newFiles[key] = file;
    state = state.copyWith(files: newFiles);
  }

  void removeFile(String key) {
    final newFiles = Map<String, FileUploadParams>.from(state.files);
    newFiles.remove(key);
    state = state.copyWith(files: newFiles);
  }

  void clearFiles() {
    state = state.copyWith(files: {});
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }
}

final fileUploadProvider = NotifierProvider<FileUploadController, FileUploadState>(
  () => FileUploadController(),
);
