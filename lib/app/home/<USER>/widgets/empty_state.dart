import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'dart:ui' as ui show PlaceholderAlignment;
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class ProcurementAppEmptyState {
  static Widget frequentlyOrdered(BuildContext context) {
    final theme = Theme.of(context);
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 360, maxHeight: 250),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SvgPicture.asset(
            kNoInvoiceSvg,
            width: 36,
          ),
          const Gap(15),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'There\'s nothing to show',
                style: theme.textTheme.headlineSmall?.copyWith(fontSize: 16),
              ),
              Text.rich(
                TextSpan(
                  text:
                      "Products you purchase frequently will start to \n show here once you start ",
                  style: theme.textTheme.bodyMedium
                      ?.copyWith(color: Palette.blackSecondary),
                  children: [
                    WidgetSpan(
                      child: InkWell(
                        onTap: () => context.goNamed(kOrdersRoute,
                            extra: {'isRefreshing': true}),
                        child: Text(
                          "creating orders",
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Palette.primary,
                          ),
                        ),
                      ),
                      baseline: TextBaseline.alphabetic,
                      alignment: ui.PlaceholderAlignment.baseline,
                    ),
                  ],
                ),
              ),
              const Gap(6),
              InkWell(
                onTap: () {
                  context.goNamed(kOrdersRoute, extra: {'isRefreshing': true});
                },
                child: Row(
                  children: [
                    Text(
                      'View all orders',
                      style: theme.textTheme.bodyMedium
                          ?.copyWith(color: Palette.statusPending),
                    ),
                    const Gap(5),
                    Icon(
                      Icons.arrow_forward_outlined,
                      size: 14,
                      color: Palette.statusPending,
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  static Widget unPaidInvoices(BuildContext context) {
    final theme = Theme.of(context);
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Divider(
        color: Palette.kE7E7E7,
      ),
       ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 360, minHeight: 250),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                kNoInvoiceSvg,
                width: 36,
              ),
              const Gap(15),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'You currently have no unpaid invoices',
                    style:
                        theme.textTheme.headlineSmall?.copyWith(fontSize: 16),
                  ),
                  Text.rich(
                    TextSpan(
                      text: "Unpaid invoices will show up here once a \n",
                      style: theme.textTheme.bodyMedium
                          ?.copyWith(color: Palette.blackSecondary),
                      children: [
                        WidgetSpan(
                          child: InkWell(
                            onTap: () {},
                            child: Text(
                              "supplier ",
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Palette.primary,
                              ),
                            ),
                          ),
                          baseline: TextBaseline.alphabetic,
                          alignment: ui.PlaceholderAlignment.baseline,
                        ),
                        const TextSpan(
                          text: "processes an ",
                        ),
                        WidgetSpan(
                          child: InkWell(
                            onTap: () {
                              context.goNamed(kOrdersRoute,
                                  extra: {'isRefreshing': true});
                            },
                            child: Text(
                              "order",
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Palette.primary,
                              ),
                            ),
                          ),
                          baseline: TextBaseline.alphabetic,
                          alignment: ui.PlaceholderAlignment.baseline,
                        ),
                      ],
                    ),
                  ),
                  const Gap(6),
                  InkWell(
                    onTap: () {
                      context.goNamed(kInvoicesRoute);
                    },
                    child: Row(
                      children: [
                        Text(
                          'View all invoices',
                          style: theme.textTheme.bodyMedium
                              ?.copyWith(color: Palette.statusPending),
                        ),
                        const Gap(5),
                        Icon(
                          Icons.arrow_forward_outlined,
                          size: 14,
                          color: Palette.statusPending,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
    ]);
  }

  static Widget recentOrders(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Divider(
          color: Palette.kE7E7E7,
        ),
        ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 360, minHeight: 250),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                kNoInvoiceSvg,
                width: 36,
              ),
              const Gap(15),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'You currently have no orders',
                    style:
                        theme.textTheme.headlineSmall?.copyWith(fontSize: 16),
                  ),
                  Text.rich(
                    TextSpan(
                      text: "Recent orders will show up here once \n",
                      style: theme.textTheme.bodyMedium
                          ?.copyWith(color: Palette.blackSecondary),
                      children: [
                        const TextSpan(
                          text: "you create an ",
                        ),
                        WidgetSpan(
                          child: InkWell(
                            onTap: () {
                              context.goNamed(kOrdersRoute,
                                  extra: {'isRefreshing': true});
                            },
                            child: Text(
                              "order",
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Palette.primary,
                              ),
                            ),
                          ),
                          baseline: TextBaseline.alphabetic,
                          alignment: ui.PlaceholderAlignment.baseline,
                        ),
                      ],
                    ),
                  ),
                  const Gap(6),
                  InkWell(
                    onTap: () {
                      context
                          .goNamed(kOrdersRoute, extra: {'isRefreshing': true});
                    },
                    child: Row(
                      children: [
                        Text(
                          'View all orders',
                          style: theme.textTheme.bodyMedium
                              ?.copyWith(color: Palette.statusPending),
                        ),
                        const Gap(5),
                        Icon(
                          Icons.arrow_forward_outlined,
                          size: 14,
                          color: Palette.statusPending,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

//
