import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/home/<USER>/models/procurement_stats_data.dart';
import 'package:td_procurement/app/home/<USER>/entities/stat_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';

abstract class HomeDataSource {
  Future<ProcurementStatsData> getStats(StatParams params);
}

final homeDataProvider = Provider<HomeDataSource>((ref) {
  return HomeDataSourceImplementation(ref);
});

final authDataProvider = Provider<HomeDataSource>((ref) {
  return HomeDataSourceImplementation(ref);
});

class HomeDataSourceImplementation extends HomeDataSource {
  final Ref _ref;
  HomeDataSourceImplementation(this._ref);

  late final TdApiClient _apiClient = _ref.read(apiClientProvider);
  late final config = _ref.read(appConfigProvider);

  @override
  Future<ProcurementStatsData> getStats(StatParams params) async {
    // Format the DateTime in ISO 8601 format

    final res = await _apiClient.get(
      "${config.awsApiUrlV3}/procurement-stats?retailOutletId=${params.outletId}&startDate=${getEncodedIsoFormat(params.startDate)}&endDate=${getEncodedIsoFormat(params.endDate)}",
    );

    return ProcurementStatsData.fromJson(res.data['body']);
  }

  String getEncodedIsoFormat(DateTime dateTime) {
    // Ensure the DateTime is in the local time zone
    DateTime localDateTime = dateTime.toLocal();

    // Format the DateTime without the time zone offset
    String isoFormat =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss").format(localDateTime);

    // Calculate the time zone offset
    Duration offset = localDateTime.timeZoneOffset;
    String offsetHours = offset.inHours.abs().toString().padLeft(2, '0');
    String offsetMinutes =
        (offset.inMinutes.abs() % 60).toString().padLeft(2, '0');
    String offsetSign = offset.isNegative ? '-' : '+';
    String timeZoneOffset = "$offsetSign$offsetHours:$offsetMinutes";

    // Combine the formatted date and time zone offset
    String finalIsoFormat = "$isoFormat$timeZoneOffset";

    return Uri.encodeComponent(finalIsoFormat);
  }
}
