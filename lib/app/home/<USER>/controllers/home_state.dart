import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/home/<USER>/models/procurement_stats_data.dart';
import 'package:td_procurement/app/home/<USER>/entities/stat_params.dart';
import 'package:td_procurement/app/home/<USER>/use_cases/home_use_cases.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class HomeController extends Notifier<HomeState> {
  //initial value
  DateTime presentDay = DateTime.now();
  DateTime pastDay = DateTime.now().subtract(const Duration(days: 30));
  String selectedTimeFilter = "Last 30 Days";
  @override
  build() {
    state = HomeState(
        loading: true,
        pastDay: pastDay,
        presentDay: presentDay,
        selectedTimeFilter: selectedTimeFilter);
    initHome().then((data) {
      state = data;
    });
    return state;
  }

  Future<HomeState> initHome() async {
    final response = await ref.read(
      getStatsUseCaseProvider(StatParams(
          outletId: ref.read(userControllerProvider)!.currentRetailOutlet!.id!,
          startDate: getXMonthsBefore(DateTime.now(), 1),
          endDate: DateTime.now())),
    );
    switch (response) {
      case Success(data: var data):
        return HomeState(
            loading: false,
            data: data,
            pastDay: pastDay,
            presentDay: presentDay,
            selectedTimeFilter: selectedTimeFilter);
      case Failure error:
        {
          return HomeState(
              loading: false,
              data: null,
              pastDay: pastDay,
              presentDay: presentDay,
              selectedTimeFilter: selectedTimeFilter);
        }
    }
  }

  fetchStats(
      DateTime startDatee, DateTime endDatee, String selectedTimeFilter) async {
    state = HomeState(
        loading: true,
        data: state.data,
        presentDay: endDatee,
        pastDay: startDatee,
        selectedTimeFilter: selectedTimeFilter);

    final response = await ref.read(
      getStatsUseCaseProvider(StatParams(
          outletId: ref.read(userControllerProvider)!.currentRetailOutlet!.id!,
          startDate: startDatee,
          endDate: endDatee)),
    );
    switch (response) {
      case Success(data: var data):
        state = HomeState(
            loading: false,
            data: data,
            presentDay: endDatee,
            pastDay: startDatee,
            selectedTimeFilter: selectedTimeFilter);

      case Failure error:
        {
          state = HomeState(
              loading: false,
              data: null,
              presentDay: endDatee,
              pastDay: startDatee,
              selectedTimeFilter: selectedTimeFilter);
        }
    }
  }
}

class HomeState {
  final ProcurementStatsData? data;
  final bool loading;
  final DateTime presentDay;
  final DateTime pastDay;
  final String selectedTimeFilter;
  HomeState(
      {required this.loading,
      this.data,
      required this.presentDay,
      required this.pastDay,
      required this.selectedTimeFilter});
}

final homeProvider = NotifierProvider<HomeController, HomeState>(() {
  return HomeController();
});
