import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/home/<USER>/models/procurement_stats_data.dart';
import 'package:td_procurement/app/home/<USER>/entities/stat_params.dart';
import 'package:td_procurement/app/home/<USER>/repositories/home_repo.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final getStatsUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<ProcurementStatsData>>, StatParams>(
  (ref, arg) => UseCase<ProcurementStatsData>().call(
    () => ref.read(homeRepoProvider).getStats(arg),
  ),
);
