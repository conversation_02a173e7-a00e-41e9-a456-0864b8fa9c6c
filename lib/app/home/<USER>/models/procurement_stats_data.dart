import 'package:td_commons_flutter/models/variant.dart';

class ProcurementStatsData {
  final List<Invoice>? openInvoices;
  final List<Product>? frequentlyOrdered;
  final List<OrderVolume>? orderVolume;
  final List<OpenOrder>? openOrders;

  ProcurementStatsData({
    this.openInvoices,
    this.frequentlyOrdered,
    this.orderVolume,
    this.openOrders,
  });

  factory ProcurementStatsData.fromJson(Map<String, dynamic> json) {
    return ProcurementStatsData(
      openInvoices: json['openInvoices'] != null
          ? (json['openInvoices'] as List)
              .map((item) => Invoice.fromJson(item))
              .toList()
          : null,
      frequentlyOrdered: json['frequentlyOrdered'] != null
          ? (json['frequentlyOrdered'] as List)
              .map((item) => Product.fromJson(item))
              .toList()
          : null,
      orderVolume: json['orderVolume'] != null
          ? (json['orderVolume'] as List)
              .map((item) => OrderVolume.fromJson(item))
              .toList()
          : null,
      openOrders: json['openOrders'] != null
          ? (json['openOrders'] as List)
              .map((item) => OpenOrder.fromJson(item))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'openInvoices': openInvoices?.map((e) => e.toJson()).toList(),
      'frequentlyOrdered': frequentlyOrdered?.map((e) => e.toJson()).toList(),
      'orderVolume': orderVolume?.map((e) => e.toJson()).toList(),
      'openOrders': openOrders?.map((e) => e.toJson()).toList(),
    };
  }
}

class Invoice {
  final num? amount;
  final Currency? currency;
  final String? invoiceNumber;

  Invoice({this.amount, this.currency, this.invoiceNumber});

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
        amount: json['amount'],
        currency: json['currency'] != null
            ? Currency.fromJson(json['currency'])
            : null,
        invoiceNumber: json['invoiceNumber']);
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'currency': currency?.toJson(),
    };
  }
}

class Currency {
  final String? iso;
  final String? symbol;

  Currency({this.iso, this.symbol});

  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      iso: json['iso'] as String?,
      symbol: json['symbol'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'iso': iso,
      'symbol': symbol,
    };
  }
}

class Product {
  final String? id;
  // final double? totalQuantityOrdered;
  final String? variantHexCode;
  final bool? blocked;
  final String? brandId;
  final String? brandName;
  final String? category;
  final String? categoryGroup;
  final String? categoryGroupId;
  final String? categoryId;
  final String? code;
  final Currency? currency;
  final DateTime? dateAdded;
  final String? description;
  final bool? enabled;
  final bool? hasHalfQuantity;
  final bool? hasQuarterQuantity;
  final String? hexCode;
  final String? itemId;
  final String? name;
  final bool? onlineOnly;
  final num? price;
  final DateTime? productCreatedAt;
  final String? productId;
  final String? productName;
  final String? retailPriceListId;
  final SubUnit? subUnit;
  final List<String>? tags;
  final List<UnitDescription>? unitDescription;
  final DateTime? variantCreatedAt;
  final String? variantId;
  final List<ProductSupplier>? suppliers;
  //final CustomerGroupPrices? customerGroupPrices;
  final dynamic isInclusive;
  final List<dynamic>? outletTypeIds;
  // final List<TaxRateOverride>? taxRateOverride;
  //final ExtVariants? extvariants;

  Product(
      {this.id,
      // this.totalQuantityOrdered,
      this.variantHexCode,
      this.blocked,
      this.brandId,
      this.brandName,
      this.category,
      this.categoryGroup,
      this.categoryGroupId,
      this.categoryId,
      this.code,
      this.currency,
      this.dateAdded,
      this.description,
      this.enabled,
      this.hasHalfQuantity,
      this.hasQuarterQuantity,
      this.hexCode,
      this.itemId,
      this.name,
      this.onlineOnly,
      this.price,
      this.productCreatedAt,
      this.productId,
      this.productName,
      this.retailPriceListId,
      this.subUnit,
      this.tags,
      this.unitDescription,
      this.variantCreatedAt,
      this.variantId,
      // this.customerGroupPrices,
      this.isInclusive,
      this.outletTypeIds,
      this.suppliers
      // this.taxRateOverride,
      //  this.extvariants,
      });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['_id'] as String?,
      //  totalQuantityOrdered: json['totalQuantityOrdered'] as double?,
      variantHexCode: json['variantHexCode'] as String?,
      blocked: json['blocked'] as bool?,
      brandId: json['brandId'] as String?,
      brandName: json['brandName'] as String?,
      category: json['category'] as String?,
      categoryGroup: json['categoryGroup'] as String?,
      categoryGroupId: json['categoryGroupId'] as String?,
      categoryId: json['categoryId'] as String?,
      code: json['code'] as String?,
      currency:
          json['currency'] != null ? Currency.fromJson(json['currency']) : null,
      dateAdded:
          json['dateAdded'] != null ? DateTime.parse(json['dateAdded']) : null,
      description: json['description'] as String?,
      enabled: json['enabled'] as bool?,
      hasHalfQuantity: json['hasHalfQuantity'] as bool?,
      hasQuarterQuantity: json['hasQuarterQuantity'] as bool?,
      hexCode: json['hexCode'] as String?,
      itemId: json['itemId'] as String?,
      name: json['name'] as String?,
      onlineOnly: json['onlineOnly'] as bool?,
      price: json['price'],
      productCreatedAt: json['productCreatedAt'] != null
          ? DateTime.parse(json['productCreatedAt'])
          : null,
      productId: json['productId'] as String?,
      productName: json['productName'] as String?,
      retailPriceListId: json['retailPriceListId'] as String?,
      subUnit:
          json['subUnit'] != null ? SubUnit.fromJson(json['subUnit']) : null,
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      unitDescription: json['unitDescription'] != null
          ? (json['unitDescription'] as List)
              .map((item) => UnitDescription.fromJson(item))
              .toList()
          : null,
      variantCreatedAt: json['variantCreatedAt'] != null
          ? DateTime.parse(json['variantCreatedAt'])
          : null,
      variantId: json['variantId'] as String?,
      suppliers: (json['suppliers'] != null &&
              json['suppliers'] is List &&
              json['suppliers'].isNotEmpty)
          ? List<ProductSupplier>.from(
              json['suppliers']?.map((x) => ProductSupplier.fromMap(x)))
          : [],
      /* customerGroupPrices: json['customerGroupPrices'] != null
          ? CustomerGroupPrices.fromJson(json['customerGroupPrices'])
          : null,*/
      isInclusive: json['isInclusive'],
      outletTypeIds: json['outletTypeIds'] != null
          ? List<dynamic>.from(json['outletTypeIds'])
          : null,

      /*taxRateOverride: json['taxRateOverride'] != null
          ? (json['taxRateOverride'] as List)
              .map((item) => TaxRateOverride.fromJson(item))
              .toList()
          : null,*/
      /* extvariants: json['extvariants'] != null
          ? ExtVariants.fromJson(json['extvariants'])
          : null,*/
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      //'totalQuantityOrdered': totalQuantityOrdered,
      'variantHexCode': variantHexCode,
      'blocked': blocked,
      'brandId': brandId,
      'brandName': brandName,
      'category': category,
      'categoryGroup': categoryGroup,
      'categoryGroupId': categoryGroupId,
      'categoryId': categoryId,
      'code': code,
      'currency': currency?.toJson(),
      'dateAdded': dateAdded?.toIso8601String(),
      'description': description,
      'enabled': enabled,
      'hasHalfQuantity': hasHalfQuantity,
      'hasQuarterQuantity': hasQuarterQuantity,
      'hexCode': hexCode,
      'itemId': itemId,
      'name': name,
      'onlineOnly': onlineOnly,
      'price': price,
      'productCreatedAt': productCreatedAt?.toIso8601String(),
      'productId': productId,
      'productName': productName,
      'retailPriceListId': retailPriceListId,
      'subUnit': subUnit?.toJson(),
      'tags': tags,
      'unitDescription': unitDescription?.map((e) => e.toJson()).toList(),
      'variantCreatedAt': variantCreatedAt?.toIso8601String(),
      'variantId': variantId,
      'suppliers': suppliers?.map((x) => x.toMap()).toList(),
      // 'customerGroupPrices': customerGroupPrices?.toJson(),
      'isInclusive': isInclusive,
      'outletTypeIds': outletTypeIds,
      //  'taxRateOverride': taxRateOverride?.map((e) => e.toJson()).toList(),
      //  'extvariants': extvariants?.toJson(),
    };
  }
}

class OrderVolume {
  final num? amount;
  final Currency? currency;

  OrderVolume({this.amount, this.currency});

  factory OrderVolume.fromJson(Map<String, dynamic> json) {
    return OrderVolume(
      amount: json['amount'],
      currency:
          json['currency'] != null ? Currency.fromJson(json['currency']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'currency': currency?.toJson(),
    };
  }
}

class OpenOrder {
  final num? amount;
  final Currency? currency;

  OpenOrder({this.amount, this.currency});

  factory OpenOrder.fromJson(Map<String, dynamic> json) {
    return OpenOrder(
      amount: json['amount'],
      currency:
          json['currency'] != null ? Currency.fromJson(json['currency']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'currency': currency?.toJson(),
    };
  }
}

class SubUnit {
  final String? upc;
  final DateTime? syncedAt;

  SubUnit({this.upc, this.syncedAt});

  factory SubUnit.fromJson(Map<String, dynamic> json) {
    return SubUnit(
      upc: json['upc'] as String?,
      syncedAt:
          json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'upc': upc,
      'syncedAt': syncedAt?.toIso8601String(),
    };
  }
}

class UnitDescription {
  final String? title;
  final String? description;

  UnitDescription({this.title, this.description});

  factory UnitDescription.fromJson(Map<String, dynamic> json) {
    return UnitDescription(
      title: json['title'] as String?,
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
    };
  }
}

/*class CustomerGroupPrices {
  final double? ROT;
  final double? MTO;
  final double? APP;
  final double? APC;

  CustomerGroupPrices({this.ROT, this.MTO, this.APP, this.APC});

  factory CustomerGroupPrices.fromJson(Map<String, dynamic> json) {
    return CustomerGroupPrices(
      ROT: json['ROT'] as double?,
      MTO: json['MTO'] as double?,
      APP: json['APP'] as double?,
      APC: json['APC'] as double?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ROT': ROT,
      'MTO': MTO,
      'APP': APP,
      'APC': APC,
    };
  }
}
*/
/*class TaxRateOverride {
  final String? country;
  final double? taxRate;

  TaxRateOverride({this.country, this.taxRate});

  factory TaxRateOverride.fromJson(Map<String, dynamic> json) {
    return TaxRateOverride(
      country: json['country'] as String?,
      taxRate: json['taxRate'] as double?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'taxRate': taxRate,
    };
  }
}*/

/*class ExtVariants {
  final String? id;
  final double? available;

  ExtVariants({this.id, this.available});

  factory ExtVariants.fromJson(Map<String, dynamic> json) {
    return ExtVariants(
      id: json['_id'] as String?,
      available: json['available'] as double?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'available': available,
    };
  }
}
*/

//push.