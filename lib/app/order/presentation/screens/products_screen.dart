import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/widgets/category_filter_dropdown.dart';
import 'package:td_procurement/app/order/presentation/widgets/category_selection_bar.dart';
import 'package:td_procurement/app/order/presentation/widgets/export_products.dart';
import 'package:td_procurement/app/order/presentation/widgets/non_export_products.dart';
import 'package:td_procurement/app/order/presentation/widgets/sales_products.dart';
import 'package:td_procurement/app/order/presentation/widgets/top_bar_header.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_grid_item.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class ProductsScreen extends ConsumerWidget {
  const ProductsScreen(this.orderType, {super.key});

  final OrderType orderType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isSalesOrder = orderType == OrderType.sales;
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    return isSalesOrder
        ? const SalesProductsWidget()
        : isExportCountry
            ? const ExportProductsWidget()
            : const NonExportProductsWidget();
  }
}

class TopBarWidget extends ConsumerWidget {
  const TopBarWidget(
    this.controller, {
    super.key,
    required this.onIndexChanged,
    required this.activeIndex,
    required this.selectedCategory,
    required this.onCategoryChanged,
    required this.variants,
    required this.moreProductVariants,
    required this.showingMoreProducts,
  });

  final TextEditingController controller;

  final int activeIndex;
  final Function(int) onIndexChanged;
  final String selectedCategory;
  final Function(String) onCategoryChanged;
  final AsyncValue<List<Variant>> variants;
  final List<Variant> moreProductVariants;
  final bool showingMoreProducts;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      height: 200,
      child: Column(
        children: [
          const TopBarHeader(),
          Container(
            height: 70,
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 40),
            alignment: Alignment.center,
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: SearchBar(
                    controller: controller,
                    leading: Icon(Icons.search, color: Palette.stroke),
                    elevation: WidgetStateProperty.all(0),
                    backgroundColor: WidgetStateProperty.all(Colors.white),
                    overlayColor: WidgetStateProperty.all(Colors.white),
                    shape: WidgetStatePropertyAll(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    constraints:
                        BoxConstraints.tight(const Size.fromHeight(42)),
                    side: WidgetStateProperty.all(
                      BorderSide(color: Palette.stroke),
                    ),
                  ),
                ),
                const Gap(10),
                Flexible(
                  flex: 1,
                  child: CategoryFilterDropdown(
                    categoryFilter: variants.when(
                      data: (data) {
                        List<Variant> variants = data;
                        if (showingMoreProducts) {
                          variants = [...data, ...moreProductVariants];
                        }
                        return ProductsManager(variants).getCategoryFilter();
                      },
                      loading: () => ['All', 'category'],
                      error: (e, s) => [],
                    ),
                    selectedCategory: selectedCategory,
                    onCategoryChanged: onCategoryChanged,
                    onIndexChanged: onIndexChanged,
                    loading: variants.isLoading,
                  ),
                ),
              ],
            ),
          ),
          CategorySelectionBar(
            categoryOptions: variants.when(
              data: (data) {
                List<Variant> variants = data;
                if (showingMoreProducts) {
                  variants = [...data, ...moreProductVariants];
                }
                return ProductsManager(variants).getCategoryNames();
              },
              loading: () => List.filled(5, 'category'),
              error: (e, s) => [],
            ),
            activeIndex: activeIndex,
            onIndexChanged: onIndexChanged,
            onCategoryChanged: onCategoryChanged,
            loading: variants.isLoading,
          ),
        ],
      ),
    );
  }
}

Widget loadingWidget(BuildContext context) {
  final textTheme = Theme.of(context).textTheme;
  final loadingListings = List.filled(
    6,
    Variant(variantId: 'variantId', name: 'name', createdAt: DateTime.now()),
  );
  return CustomScrollView(
    slivers: [
      SliverToBoxAdapter(
        child: Skeletonizer(
          enabled: true,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Gap(16),
                Text(
                  'Loading Listings',
                  style: textTheme.headlineSmall,
                ),
                const Gap(16),
              ],
            ),
          ),
        ),
      ),
      SliverPadding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        sliver: SliverGrid(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3, // Number of columns
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
          ),
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              return Skeletonizer(
                enabled: true,
                child:
                    VariantGridItem(loadingListings[index], key: UniqueKey()),
              );
            },
            childCount:
                loadingListings.length, // Number of items in the first grid
          ),
        ),
      ),
    ],
  );
}
