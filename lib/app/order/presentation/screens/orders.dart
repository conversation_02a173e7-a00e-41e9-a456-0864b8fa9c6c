import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_state.dart';
import 'package:td_procurement/app/order/presentation/widgets/date_branch_picker.dart';
import 'package:td_procurement/app/order/presentation/widgets/empty_orders.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_pagination.dart';
import 'package:td_procurement/app/order/presentation/widgets/orders_table.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen(this.refreshTransactions, this.fetchPending, {super.key});

  final bool refreshTransactions;
  final bool fetchPending;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _OrderScreenState();
}

class _OrderScreenState extends ConsumerState<OrdersScreen> {
  final _controller = TextEditingController();
  Timer? _debounce;
  int activeIndex = 0;

  @override
  void initState() {
    super.initState();
    final orderState = ref.read(orderControllerProvider);

    activeIndex = orderState.fetchTransactionsParam.activeIndex;

    _controller.text = orderState.fetchTransactionsParam.searchText;
    if (widget.fetchPending) {
      activeIndex = 2;
    }

    fetchTransactions();
  }

  @override
  void didUpdateWidget(covariant OrdersScreen oldWidget) {
    fetchTransactions();
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _controller.clear();
    super.dispose();
  }

  void fetchTransactions() {
    final params = widget.fetchPending
        ? ref
            .read(orderControllerProvider)
            .fetchTransactionsParam
            .copyWith(orderStatus: 'pending')
        : ref.read(orderControllerProvider).fetchTransactionsParam;
    Future.microtask(
      () => ref
          .read(orderControllerProvider.notifier)
          .fetchTransactions(params, forced: widget.refreshTransactions),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final orderState = ref.watch(orderControllerProvider);
    final statusOptions = orderState.orderStatusOptions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(20), // 40
        Padding(
          padding: const EdgeInsets.only(left: 40),
          child: Text(
            'Purchase Orders',
            style: textTheme.headlineMedium,
          ),
        ),
        const Gap(20),
        Container(
          padding: const EdgeInsets.only(left: 40).copyWith(bottom: 12),
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Palette.stroke)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 3,
                child: Row(
                  children: statusOptions.map((status) {
                    int index = statusOptions.indexOf(status);
                    bool isSelected = activeIndex == index;

                    return Flexible(
                      child: StatusFilteringWidget(
                        status,
                        isSelected,
                        width: 130,
                        padding: const EdgeInsets.symmetric(vertical: 19),
                        onPressed: () {
                          if (!orderState.transactions.isLoading &&
                              index != activeIndex) {
                            setState(() {
                              activeIndex = index;
                            });
                            handleOrderOption(index);
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
              const Flexible(
                flex: 2,
                child: DateAndBranchPickerWidget(),
              ),
            ],
          ),
        ),
        Skeletonizer(
          enabled: orderState.transactions.isLoading &&
              !orderState.fetchTransactionsParam.loaded,
          child: _buildSearchBar(orderState),
        ),
        Visibility(
          visible: orderState.transactions.isLoading ||
              (orderState.transactions.hasValue &&
                  orderState.transactions.value!.isNotEmpty),
          child: Skeletonizer(
            enabled: orderState.transactions.isLoading,
            child: _tableHeader(),
          ),
        ),
        Expanded(
          child: orderState.transactions.when(
            data: (orders) {
              if (orders.isEmpty) {
                return const EmptyOrdersWidget(OrderType.purchase);
              }
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: OrdersTableWidget(orders)),
                  const Gap(10),
                  const OrderPagination(),
                  const Gap(10),
                ],
              );
            },
            loading: () {
              return Skeletonizer(
                enabled: true,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: OrdersTableWidget(
                          List.filled(10, Transaction.defaultValue())),
                    ),
                    const OrderPagination(),
                    const Gap(20),
                  ],
                ),
              );
            },
            error: (e, s) {
              if (kIsWeb || kIsWasm) {
                return Skeletonizer(
                  enabled: true,
                  child: OrdersTableWidget(
                      List.filled(20, Transaction.defaultValue())),
                );
              }

              return FailureWidget(
                fullScreen: true,
                heightFactor: 0.7,
                e: e,
                retry: () => ref
                    .read(orderControllerProvider.notifier)
                    .fetchTransactions(ref
                        .read(orderControllerProvider)
                        .fetchTransactionsParam),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar(OrderState orderState) {
    final textTheme = Theme.of(context).textTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            height: 48,
            padding: const EdgeInsets.only(left: 40),
            child: TextFormField(
              controller: _controller,
              // initialValue: null,
              decoration: InputDecoration(
                hintText: 'Type to search by reference number or destination',
                prefixIcon: Padding(
                  padding: const EdgeInsets.only(top: 2.0, right: 10),
                  child: Skeleton.replace(
                    child: SvgPicture.asset(
                      '$kSvgDir/order/search.svg',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                prefixIconConstraints: const BoxConstraints(
                  minWidth: 22,
                  minHeight: 22,
                ),
                hintStyle: textTheme.bodyMedium?.copyWith(
                  color: Palette.placeholder,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
              ),
              cursorColor: Palette.strokePressed,
              cursorHeight: 18,
              onChanged: _onTextChanged,
            ),
          ),
        ),
      ],
    );
  }

  Widget _tableHeader() {
    final textTheme = Theme.of(context).textTheme;
    return buildTableHeader(
        tableHeader: Table(
      columnWidths: const {
        0: FlexColumnWidth(0.35),
        1: FlexColumnWidth(1.3),
        2: FlexColumnWidth(1.6),
        3: FlexColumnWidth(2.5),
        4: FlexColumnWidth(1.2),
        5: FlexColumnWidth(1.6),
        6: FlexColumnWidth(2.5),
        7: FlexColumnWidth(0.5),
      },
      children: [
        TableRow(
          children: [
            Container(),
            buildHeaderCell('Reference', textTheme),
            buildHeaderCell('Amount', textTheme),
            buildHeaderCell('Summary', textTheme),
            buildHeaderCell('Delivering to', textTheme),
            buildHeaderCell('Created on', textTheme),
            Container(), // Empty space for floating icon
            Container(),
          ],
        ),
      ],
    ));
  }

  handleOrderOption(int index) {
    final status = ref.read(orderControllerProvider).orderStatusOptions[index];

    final params = switch (status.toLowerCase()) {
      'all orders' =>
        FetchTransactionsParam.defaultValue().copyWith(orderStatus: 'all'),
      'draft' =>
        FetchTransactionsParam.defaultValue().copyWith(orderStatus: 'draft'),
      'pending' =>
        FetchTransactionsParam.defaultValue().copyWith(orderStatus: 'pending'),
      'completed' => FetchTransactionsParam.defaultValue()
          .copyWith(orderStatus: 'completed'),
      _ => FetchTransactionsParam.defaultValue()
          .copyWith(orderStatus: status.toLowerCase()),
    };

    final orderNotifier = ref.read(orderControllerProvider.notifier);

    orderNotifier.setDateFilter(null, null);
    orderNotifier.setSelectedBranches([]);

    orderNotifier.fetchTransactions(params.copyWith(activeIndex: index),
        forced: true);
  }

  void _onTextChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      final params = ref.read(orderControllerProvider).fetchTransactionsParam;
      ref.read(orderControllerProvider.notifier).fetchTransactions(
          params.copyWith(searchText: query.trim(), currentPage: 1),
          forced: true);
    });
  }
}
