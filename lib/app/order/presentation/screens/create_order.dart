import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/order_confirmation.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/domain/use_cases/order_use_cases.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/screens/preview_order.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_action_bar.dart';
import 'package:td_procurement/app/order/presentation/widgets/pallet_container.dart';
import 'package:td_procurement/app/order/presentation/widgets/product_search_auto_complete.dart';
import 'package:td_procurement/app/order/presentation/widgets/purchase_order.dart';
import 'package:td_procurement/app/order/presentation/widgets/raise_order.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/multi_value_listenable_builder.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

class CreateOrderScreen extends ConsumerStatefulWidget {
  const CreateOrderScreen({super.key, this.transaction});

  final Transaction? transaction;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CreateOrderScreenState();
}

class _CreateOrderScreenState extends ConsumerState<CreateOrderScreen> {
  final rightButton1 = ValueNotifier<bool>(false);
  final rightButton2 = ValueNotifier<bool>(false);
  late final rightButton2Disabled = ValueNotifier<bool>(globalOrderEnabled);

  String? hexCode;

  bool get isDraftOrigin => widget.transaction != null;

  bool get globalOrderEnabled =>
      ref.read(isFeatureEnabledProvider(AuthScope.globalOrder));

  @override
  void initState() {
    hexCode = ref.read(hexCodeProvider);
    Future.microtask(() {
      if (globalOrderEnabled) {
        ref.read(orderControllerProvider.notifier).fetchExportVariants();
      } else if (hexCode != null) {
        ref.read(orderControllerProvider.notifier).fetchCollections(hexCode!);
        ref.read(orderControllerProvider.notifier).fetchOutletVariants();
      }

      // update checkout button disabled state
      final previewDetail = ref.read(orderPreviewDetailProvider);
      if (previewDetail != null) {
        rightButton2Disabled.value = false;
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      processDraft();
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // ongoing update of checkout button disabled
    ref.listen(orderPreviewDetailProvider, (oldState, newState) {
      if (newState != null) {
        rightButton2Disabled.value = false;
      }
    });

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (_, __) {
        Future.microtask(() {
          ref.read(editingCartProvider.notifier).state = [];
          // resetCart(ref, forceClear: false);
        });
      },
      child: GestureDetector(
        // reset focused filed on export cart items
        onTap: () => ref.read(focusedFieldProvider.notifier).state = null,
        child: Scaffold(
          body: Column(
            children: [
              MultiValueListenableBuilder<bool, bool, bool>(
                valueListenable1: rightButton1,
                valueListenable2: rightButton2,
                valueListenable3: rightButton2Disabled,
                builder: (context, loading1, loading2, disabled2, __) {
                  return OrderActionBarWidget(
                    // leftText: globalOrderEnabled ? 'Cart' : 'Create Order',
                    leftText: 'Create Purchase Order',
                    leftIconAction: () => context.pop(),
                    rightButton1Text: globalOrderEnabled
                        ? null
                        : isDraftOrigin
                            ? 'Update Draft'
                            : 'Save Draft',
                    rightButton1Loading: loading1,
                    rightButton1Action: globalOrderEnabled
                        ? null
                        : () => handleCreateOrder(true, isDraftOrigin),
                    rightButton2Text:
                        globalOrderEnabled ? 'Checkout' : 'Create Order',
                    rightButton2Loading: loading2,
                    rightButton2Action: () => globalOrderEnabled
                        ? createExportOrder(false)
                        : handleCreateOrder(false, false),
                    rightButton2Disabled: disabled2,
                  );
                },
              ),
              // Expanded(
              //   child: SingleChildScrollView(
              //     child: Row(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         const Expanded(
              //           child: RaiseOrderWidget(),
              //         ),
              //         Expanded(
              //           child: isExportCountry
              //               ? const PalletContainerWidget()
              //               : const PurchaseOrderWidget(),
              //         ),
              //       ],
              //     ),
              //   ),
              // )
              Expanded(
                child: CustomScrollView(
                  slivers: [
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Expanded(
                            child: RaiseOrderWidget(),
                          ),
                          Expanded(
                            child: Container(
                              color: Palette.kFCFCFC,
                              child: globalOrderEnabled
                                  ? const PalletContainerWidget()
                                  : const PurchaseOrderWidget(
                                      OrderType.purchase),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  void processDraft() {
    if (widget.transaction == null) return;

    final cartNotifier = ref.read(cartProvider.notifier);
    final branches = ref.read(branchesProvider);

    final cartItems = orderItemsToCartItems(widget.transaction?.items ?? []);
    final branch = branches.firstWhere(
        (x) => x.id == widget.transaction?.retailOutletId,
        orElse: () => branches.first);

    cartNotifier.addItems(cartItems);
    cartNotifier.setBranch(branch);
    cartNotifier.setTransaction(widget.transaction!);
  }

  List<CartItem> orderItemsToCartItems(List<OrderItem> items) {
    return items
        .map((x) => CartItem(
            count: x.quantity ?? 0,
            variant: Variant(
                variantId: x.variantId!,
                name: x.name ?? x.productName ?? '',
                price: x.price,
                discount: x.discount,
                brandId: x.brandId,
                currency: widget.transaction?.currency,
                extVariant: ExtVariant(
                    id: '',
                    reservedStock: 0,
                    availableStock: x.quantityLeft ?? 0,
                    available: x.available ?? 0))))
        .toList();
  }

  Future<void> createExportOrder(bool isDraftOrder) async {
    final previewDetail = ref.read(orderPreviewDetailProvider);

    if (previewDetail == null) return;

    setLoading(isDraftOrder);

    final params = CreateOrderParams(orders: previewDetail.rawData!);
    final res = await ref.read(createExportOrderUseCaseProvider(params));

    res.when(
      success: (_) {
        clearLoading(isDraftOrder);
        context.goNamed(kOrdersRoute, extra: {'isRefreshing': true});
        Toast.success('Created your order successfully', context);
        resetCart(ref);
      },
      failure: (error, _) {
        clearLoading(isDraftOrder);
        Toast.apiError(error, context, title: 'Error Creating Orders');
      },
    );
  }

  Future<void> handleCreateOrder(bool isDraftOrder, bool updatingDraft) async {
    final cartState = ref.read(cartProvider);
    final text = isDraftOrder ? 'draft' : 'order';
    if (cartState.branch == null) {
      return Toast.error(
          'You need to select a location before creating $text', context,
          title: 'Cannot create $text');
    }

    if (cartState.uniqueCartItemsWithNonZeroCount.isEmpty) {
      return Toast.error(
          'You must select an item before creating $text', context,
          title: 'Cannot create $text');
    }

    //?TODO:  confirm the need to get user phone number

    // set loading
    setLoading(isDraftOrder);

    final preparedOrders =
        await prepareOrder(context, ref, isDraftOrigin, null, null);

    if (preparedOrders == null) {
      // clear loading
      clearLoading(isDraftOrder);
      return;
    }

    final fulfilledOrders = getFulfilledOrders(preparedOrders);
    final unfulfilledOrders = getUnFulfilledOrders(preparedOrders);

    final orderConfirmation = await ref.read(countryTypeProvider).when(
        export: () async =>
            await handleShippingRates(preparedOrders, cartState.branch!.id),
        nonExport: () => null);

    // clear loading
    clearLoading(isDraftOrder);

    if (mounted) {
      return showCustomGeneralDialog(
        context,
        dismissible: false,
        percentage: 0.40,
        child: PreviewOrderScreen(
          isDraftOrigin: isDraftOrigin,
          preparedOrder: preparedOrders,
          fulfilledOrders: fulfilledOrders,
          unfulfilledOrders: unfulfilledOrders,
          isDraftOrder: isDraftOrder,
          updatingDraftOrder: updatingDraft,
          orderConfirmation: orderConfirmation,
        ),
      );
    }

    //?TODO: confirm the need to verify-checkout for NG retailer who is not credit enabled

    // final Map<String, dynamic> doc = {};
    // List<dynamic> unFulfillableOrders = [];

    // final countryCode = ref.read(countryCodeProvider);
    // if (countryCode == kNgCountryCode) {
    //   unFulfillableOrders = getUnFulfilledOrders(preparedOrders);
    //   final fulfillableOrders = getFulfilledOrders(preparedOrders);
    // }

    // if (countryCode == kGbCountryCode) {
    //   final shippingRates =
    //       await handleShippingRates(preparedOrders, cartState.branch!.id);

    //   if (shippingRates == null) {
    //     // clear loading
    //     rightButton1.value = false;
    //     return;
    //   }
    // }
  }

  void setLoading(bool isDraftOrder) {
    if (isDraftOrder) {
      rightButton1.value = true;
    } else {
      rightButton2.value = true;
    }
  }

  void clearLoading(bool isDraftOrder) {
    if (isDraftOrder) {
      rightButton1.value = false;
    } else {
      rightButton2.value = false;
    }
  }

  Future<OrderConfirmation?> handleShippingRates(
      OrderPreviewDetail orderPreviewDetail, String outletId) async {
    final fulFillableOrders = getFulfilledOrders(orderPreviewDetail);

    if (fulFillableOrders.isEmpty) {
      Toast.error('Cannot create orders with unfulfillable items', context,
          title: 'Error Creating Orders');
      return null;
    }

    final params = ShippingRateParams(orderPreviewDetail.rawData!, outletId);
    final res = await ref.read(fetchShippingRateUseCaseProvider(params));
    return res.when(
      success: (data) => data,
      failure: (e, _) {
        Toast.apiError(e, context);
        return null;
      },
    );
  }
}
