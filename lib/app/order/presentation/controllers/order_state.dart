import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/collection.dart';
import 'package:td_commons_flutter/models/driver.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/core/models/index.dart';

@immutable
class OrderState extends Equatable {
  final List<String> orderStatusOptions;
  final List<String> salesOrderStatusOptions;
  final List<String> catalogCategoryOptions;
  final List<String> catalogBrandOptions;
  final AsyncValue<List<Transaction>> transactions;
  final FetchTransactionsParam fetchTransactionsParam;
  final AsyncValue<List<Order>> salesOrders;
  final FetchSalesOrdersParams fetchSalesOrdersParams;
  final FetchSalesLocationDriverParams fetchSalesLocationDriversParams;
  final Transaction? transactionInView;
  final Order? salesOrderInView;
  final AsyncValue<OrderDetails?> orderDetails;
  final bool? showOrderDetailsError;
  final bool? showShipmentError;
  final List<RetailBranch> selectedBranches;
  final DateTime? selectedStartDate;
  final DateTime? selectedEndDate;
  final DateTime? salesOrdersStartDate;
  final DateTime? salesOrdersEndDate;
  final LayerLink layerBranchLink;

  /// create-nonExport-order state-variable
  final AsyncValue<List<Collection>> collections;
  final AsyncValue<List<Collection>> exclusiveCollections;

  /// create-nonExport-order state-variable
  final AsyncValue<List<Variant>> outletVariants;

  final AsyncValue<List<Variant>> searchedVariants;

  /// create-export-order state-variables
  final AsyncValue<List<Variant>> exportVariants;
  final AsyncValue<List<Driver>> salesLocationDrivers;

  const OrderState({
    this.orderStatusOptions = const [
      "All orders",
      "Draft",
      "Pending",
      "Completed"
    ],
    this.salesOrderStatusOptions = const ["All orders", "Pending", "Completed"],
    this.catalogCategoryOptions = const ['All'],
    this.catalogBrandOptions = const [],
    required this.transactions,
    required this.fetchTransactionsParam,
    required this.salesOrders,
    required this.fetchSalesOrdersParams,
    required this.fetchSalesLocationDriversParams,
    this.transactionInView,
    this.salesOrderInView,
    required this.orderDetails,
    this.showOrderDetailsError = false,
    this.showShipmentError = false,
    this.selectedBranches = const [],
    this.selectedStartDate,
    this.selectedEndDate,
    this.salesOrdersStartDate,
    this.salesOrdersEndDate,
    required this.layerBranchLink,
    required this.collections,
    required this.exclusiveCollections,
    required this.outletVariants,
    required this.searchedVariants,
    required this.exportVariants,
    required this.salesLocationDrivers,
  });

  List<Variant> get variantsInCollections {
    final col = collections.value
            ?.expand((x) => x.variants ?? [])
            .whereType<Variant>()
            .toList() ??
        [];

    final exCol = exclusiveCollections.value
            ?.expand((x) => x.variants ?? [])
            .whereType<Variant>()
            .toList() ??
        [];

    return [...exCol, ...col];
  }

  List<Variant> get exclusiveVariants {
    return exclusiveCollections.value
            ?.expand((x) => x.variants ?? [])
            .whereType<Variant>()
            .toList() ??
        [];
  }

  factory OrderState.initial() {
    return OrderState(
      transactions: const AsyncData([]),
      fetchTransactionsParam: FetchTransactionsParam.defaultValue(),
      salesOrders: const AsyncData([]),
      fetchSalesOrdersParams: FetchSalesOrdersParams.defaultValue(),
      fetchSalesLocationDriversParams:
          FetchSalesLocationDriverParams.defaultValue(),
      orderDetails: const AsyncData(null),
      layerBranchLink: LayerLink(),
      collections: const AsyncData([]),
      exclusiveCollections: const AsyncData([]),
      outletVariants: const AsyncData([]),
      searchedVariants: const AsyncData([]),
      exportVariants: const AsyncData([]),
      salesLocationDrivers: const AsyncData([]),
    );
  }

  @override
  List<Object?> get props => [
        catalogCategoryOptions,
        catalogBrandOptions,
        transactions,
        fetchTransactionsParam,
        salesOrders,
        fetchSalesOrdersParams,
        fetchSalesLocationDriversParams,
        transactionInView,
        salesOrderInView,
        orderDetails,
        showOrderDetailsError,
        showShipmentError,
        selectedBranches,
        selectedStartDate,
        selectedEndDate,
        salesOrdersStartDate,
        salesOrdersEndDate,
        collections,
        exclusiveCollections,
        outletVariants,
        searchedVariants,
        exportVariants,
        salesLocationDrivers,
      ];

  OrderState copyWith({
    List<String>? catalogCategoryOptions,
    List<String>? catalogBrandOptions,
    AsyncValue<List<Transaction>>? transactions,
    FetchTransactionsParam? fetchTransactionsParam,
    AsyncValue<List<Order>>? salesOrders,
    FetchSalesOrdersParams? fetchSalesOrdersParams,
    FetchSalesLocationDriverParams? fetchSalesLocationDriversParams,
    Transaction? transactionInView,
    Order? salesOrderInView,
    AsyncValue<OrderDetails>? orderDetails,
    bool? showOrderDetailsError,
    bool? showShipmentError,
    List<RetailBranch>? selectedBranches,
    Optional<DateTime?>? selectedStartDate,
    Optional<DateTime?>? selectedEndDate,
    Optional<DateTime?>? salesOrdersStartDate,
    Optional<DateTime?>? salesOrdersEndDate,
    AsyncValue<List<Collection>>? collections,
    AsyncValue<List<Collection>>? exclusiveCollections,
    AsyncValue<List<Variant>>? outletVariants,
    AsyncValue<List<Variant>>? searchedVariants,
    AsyncValue<List<Variant>>? exportVariants,
    AsyncValue<List<Driver>>? salesLocationDrivers,
  }) {
    return OrderState(
      catalogCategoryOptions:
          catalogCategoryOptions ?? this.catalogCategoryOptions,
      catalogBrandOptions: catalogBrandOptions ?? this.catalogBrandOptions,
      transactions: transactions ?? this.transactions,
      fetchTransactionsParam:
          fetchTransactionsParam ?? this.fetchTransactionsParam,
      salesOrders: salesOrders ?? this.salesOrders,
      fetchSalesOrdersParams:
          fetchSalesOrdersParams ?? this.fetchSalesOrdersParams,
      fetchSalesLocationDriversParams: fetchSalesLocationDriversParams ??
          this.fetchSalesLocationDriversParams,
      transactionInView: transactionInView ?? this.transactionInView,
      salesOrderInView: salesOrderInView ?? this.salesOrderInView,
      orderDetails: orderDetails ?? this.orderDetails,
      showOrderDetailsError:
          showOrderDetailsError ?? this.showOrderDetailsError,
      showShipmentError: showShipmentError ?? this.showShipmentError,
      selectedBranches: selectedBranches ?? this.selectedBranches,
      selectedStartDate: selectedStartDate != null
          ? selectedStartDate.value
          : this.selectedStartDate,
      selectedEndDate: selectedEndDate != null
          ? selectedEndDate.value
          : this.selectedEndDate,
      salesOrdersStartDate: salesOrdersStartDate != null
          ? salesOrdersStartDate.value
          : this.salesOrdersStartDate,
      salesOrdersEndDate: salesOrdersEndDate != null
          ? salesOrdersEndDate.value
          : this.salesOrdersEndDate,
      layerBranchLink: layerBranchLink,
      collections: collections ?? this.collections,
      exclusiveCollections: exclusiveCollections ?? this.exclusiveCollections,
      outletVariants: outletVariants ?? this.outletVariants,
      searchedVariants: searchedVariants ?? this.searchedVariants,
      exportVariants: exportVariants ?? this.exportVariants,
      salesLocationDrivers: salesLocationDrivers ?? this.salesLocationDrivers,
    );
  }

  // Transaction? getTransaction(String orderId) {
  //   final transactions = this.transactions.valueOrNull;
  //   return (transactions != null && transactions.isEmpty)
  //       ? transactions.firstWhere((x) => x.id == orderId)
  //       : null;
  // }
}
