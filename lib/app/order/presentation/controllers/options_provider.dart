import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/index.dart';

class OptionsProvider extends Notifier<List<Order>> {
  @override
  List<Order> build() => [];

  void addItem(Order item) {
    state = [...state, item];
  }

  void removeItem(Order item) {
    state = state.where((element) => element.id != item.id).toList();
  }

  void clearList() {
    state = [];
  }

  void selectAllItems(List<Order> items) {
    state = items.where((element) => element.dispatchUser == null).toList();
  }
}

final optionsProvider = NotifierProvider<OptionsProvider, List<Order>>(
  () => OptionsProvider(),
);
