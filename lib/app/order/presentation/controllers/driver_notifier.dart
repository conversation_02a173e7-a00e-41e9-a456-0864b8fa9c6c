import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/driver.dart';

final driverProvider = NotifierProvider<DriverNotifier, Driver>(() {
  return DriverNotifier();
});

class DriverNotifier extends Notifier<Driver> {
  @override
  Driver build() => Driver(id: '');

  void setDriver(Driver driver) {
    state = driver;
  }

  void removeDriver() {
    state = Driver(id: '');
  }
}
