// cart_notifier.dart
import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/core/models/optional.dart';
import 'package:td_procurement/core/models/transaction.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

final editingCartProvider = StateProvider<List<CartItem>>((_) => []);

final cartProvider = NotifierProvider<CartNotifier, CartState>(() {
  return CartNotifier();
});

class CartNotifier extends Notifier<CartState> {
  String? _orderReference;

  @override
  CartState build() {
    return CartState.initial();
  }

  /// Add or update an item in the cart.
  /// When an item's count is zero or less, update its count to zero instead of removing it.
  void addItem(CartItem item) {
    final String key = item.variant.variantSupplierId;

    // Create a new list to hold the updated cart items
    final updatedCartItems = [...state.uniqueCartItems];

    final int itemIndex = updatedCartItems.indexWhere(
      (cartItem) => cartItem.variant.variantSupplierId == key,
    );

    if (itemIndex >= 0) {
      // Update the existing item with the new count.
      final updatedItem = updatedCartItems[itemIndex].copyWith(
        count: item.count ?? 0,
        variant: item.variant,
      );
      updatedCartItems[itemIndex] = updatedItem;
    } else if (item.count != null && item.count! > 0) {
      // Item does not exist, add as a new item.
      updatedCartItems.add(item);
    }

    // Trigger state update with a new list reference.
    state = state.copyWith(cartItems: updatedCartItems);
  }

  /// Add or update an item in the cart based on variant and count.
  /// When the new count is zero or less, update the item’s count to zero rather than removing it.
  void addVariant(Variant variant, num count) {
    final String key = variant.variantSupplierId;

    // Create a new list to hold the updated cart items
    final updatedCartItems = [...state.uniqueCartItems];

    final int itemIndex = updatedCartItems.indexWhere(
      (cartItem) => cartItem.variant.variantSupplierId == key,
    );

    if (itemIndex >= 0) {
      // Update the existing item with the new count.
      final updatedItem = updatedCartItems[itemIndex].copyWith(
        count: count,
        variant: variant,
      );
      updatedCartItems[itemIndex] = updatedItem;
    } else if (count > 0) {
      // Item does not exist in the cart, add as a new item.
      final newItem = CartItem(
        variant: variant,
        count: count,
      );
      updatedCartItems.add(newItem);
    }

    // Trigger state update with a new list reference.
    state = state.copyWith(cartItems: updatedCartItems);
  }

  // /// Add, update or remove an item in the cart
  // void addItem(CartItem item) {
  //   final String key = item.variant.variantSupplierId;

  //   // Create a new list to hold the updated cart items
  //   final updatedCartItems = [...state.uniqueCartItems];

  //   final int itemIndex = updatedCartItems.indexWhere(
  //     (cartItem) => cartItem.variant.variantSupplierId == key,
  //   );

  //   if (itemIndex >= 0) {
  //     // Update the existing item
  //     if (item.count != null && item.count! <= 0) {
  //       // Remove item if count is zero or less
  //       updatedCartItems.removeAt(itemIndex);
  //     } else {
  //       // Update count for existing item
  //       final updatedItem = updatedCartItems[itemIndex].copyWith(
  //         count: item.count,
  //         variant: item.variant,
  //       );
  //       updatedCartItems[itemIndex] = updatedItem;
  //     }
  //   } else if (item.count != null && item.count! > 0) {
  //     // Item does not exist, add as a new item
  //     updatedCartItems.add(item);
  //   }

  //   // Trigger state update with a new list reference
  //   state = state.copyWith(cartItems: updatedCartItems);
  // }

  // /// Add or update an item in the cart based on variant and count
  // void addVariant(Variant variant, num count) {
  //   final String key = variant.variantSupplierId;

  //   // Create a new list to hold the updated cart items
  //   final updatedCartItems = [...state.uniqueCartItems];

  //   final int itemIndex = updatedCartItems.indexWhere(
  //     (cartItem) => cartItem.variant.variantSupplierId == key,
  //   );

  //   if (itemIndex >= 0) {
  //     // Update count for existing item
  //     if (count <= 0) {
  //       // Remove item if new count is zero or less
  //       updatedCartItems.removeAt(itemIndex);
  //     } else {
  //       // Update the existing item with the new count
  //       final updatedItem = updatedCartItems[itemIndex]
  //           .copyWith(count: count, variant: variant);
  //       updatedCartItems[itemIndex] = updatedItem;
  //     }
  //   } else if (count > 0) {
  //     // Item does not exist in the cart, add as a new item
  //     final newItem = CartItem(
  //       variant: variant,
  //       count: count,
  //     );
  //     updatedCartItems.add(newItem);
  //   }

  //   // Trigger state update with a new list reference
  //   state = state.copyWith(cartItems: updatedCartItems);
  // }

  /// Remove an item from the cart
  void removeItem(String? variantSupplierId) {
    if (variantSupplierId == null) return;
    final filteredItems = state.uniqueCartItems
        .where((item) => item.variant.variantSupplierId != variantSupplierId)
        .toList();
    state = state.copyWith(cartItems: filteredItems);
  }

  /// Add or update items in the cart
  void addItems(List<CartItem> items) {
    // Create a map of existing cart items by their variantSupplierId for quick lookup
    final existingItemsMap = {
      for (final item in state.uniqueCartItems)
        item.variant.variantSupplierId: item
    };

    // Create a new list to hold the updated cart items
    final updatedCartItems = [...state.uniqueCartItems];

    // Update or add each item from the new list
    for (final newItem in items) {
      final String key = newItem.variant.variantSupplierId;

      if (existingItemsMap.containsKey(key)) {
        // If the item exists in the current state, update its count
        final existingItem = existingItemsMap[key]!;
        final updatedItem = existingItem.copyWith(
            count: newItem.count, variant: newItem.variant);

        // Replace the item in the new list
        final index = updatedCartItems.indexWhere(
          (cartItem) => cartItem.variant.variantSupplierId == key,
        );
        updatedCartItems[index] = updatedItem;
      } else if (newItem.count != null && newItem.count! > 0) {
        // If the item does not exist in the current state, add it to the new list
        updatedCartItems.add(newItem);
      }
    }

    // Trigger state update by assigning a new instance of the state
    state = state.copyWith(cartItems: updatedCartItems);
  }

  /// set the delivery location
  void setBranch(RetailBranch? branch) {
    state = state.copyWith(branch: Optional(branch));
  }

  void setTransaction(Transaction transaction) {
    state = state.copyWith(transaction: transaction);
  }

  /// Retrieve a CartItem by its variantSupplierId
  CartItem? item(String variantSupplierId) {
    return state.uniqueCartItems.firstWhereOrNull(
        (cartItem) => cartItem.variant.variantSupplierId == variantSupplierId);
  }

  /// Clear all items from the cart by resetting the state
  /// Optionally ignore the branch
  void clearCart([bool withBranch = true]) {
    state = CartState.initial(
      withBranch ? null : state.branch,
      state.showingExclusiveProducts,
      state.showingOtherProducts,
    );
  }

  void setShowingExclusiveProducts(bool value) {
    state = state.copyWith(showingExclusiveProducts: value);
  }

  void setShowingOtherProducts(bool value) {
    state = state.copyWith(showingOtherProducts: value);
  }

  void setNote(String? note) {
    state = state.copyWith(note: Optional(note));
  }

  /// Calculate the total price
  double get totalPrice {
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    if (isExportCountry) {
      return state.uniqueCartItemsWithNonZeroCount.fold(
          0.0,
          (total, item) =>
              total + (supplierPrice(ref, item.variant) * (item.count ?? 0)));
    } else {
      return state.uniqueCartItemsWithNonZeroCount.fold(
          0.0,
          (total, item) =>
              total + (item.variant.price ?? 0.0) * (item.count ?? 0));
    }
  }

  // calculate the amount due
  double get amountDue => totalPrice + taxes - discount;

  /// Calculate the total promo price
  double get promoTotal => state.uniqueCartItemsWithNonZeroCount.fold(
      0.0,
      (total, item) =>
          total + (item.variant.promoPrice ?? 0.0) * (item.count ?? 0));

  /// Calculate the total discount
  double get discount => state.uniqueCartItemsWithNonZeroCount.fold(
      0.0,
      (discount, item) =>
          discount + (item.variant.discount ?? 0.0) * (item.count ?? 0));

  // Calculate the total taxes
  double get taxes {
    return state.uniqueCartItems.fold(0.0, (taxes, item) {
      final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
      final outletCountry = outlet?.country;
      final taxRate = (item.variant.taxRateOverride
              ?.firstWhereOrNull((tax) => tax.country == outletCountry)
              ?.taxRate) ??
          0;
      final tax = taxRate > 0
          ? ((taxRate / 100) * (item.variant.price ?? 0.0)) * (item.count ?? 0)
          : 0;
      return taxes + tax;
    });
  }

  String populateReference() {
    _orderReference =
        state.transaction?.reference ?? getRandomAlphanumericNumbers();
    return _orderReference!.toUpperCase();
  }

  String get getReference => _orderReference!;

  String get currencyCode {
    final countryCode = ref.read(countryCodeProvider);

    final cartItems = state.uniqueCartItemsWithNonZeroCount;

    return cartItems.isNotEmpty
        ? cartItems.first.variant.currency?.iso ??
            countryToCurrency[countryCode]!
        : countryToCurrency[countryCode]!;
  }
}

@immutable
class CartState extends Equatable {
  final List<CartItem> cartItems;
  final RetailBranch? branch;
  final String? note;
  final Transaction? transaction;
  final bool showingExclusiveProducts;
  final bool showingOtherProducts;

  const CartState({
    required this.cartItems,
    this.branch,
    this.note,
    this.transaction,
    this.showingExclusiveProducts = false,
    this.showingOtherProducts = false,
  });

  CartState copyWith({
    List<CartItem>? cartItems,
    Optional<RetailBranch?>? branch,
    Optional<String?>? note,
    Transaction? transaction,
    bool? showingExclusiveProducts,
    bool? showingOtherProducts,
  }) {
    return CartState(
      cartItems: cartItems ?? this.cartItems,
      branch: branch != null ? branch.value : this.branch,
      note: note != null ? note.value : this.note,
      transaction: transaction ?? this.transaction,
      showingExclusiveProducts:
          showingExclusiveProducts ?? this.showingExclusiveProducts,
      showingOtherProducts: showingOtherProducts ?? this.showingOtherProducts,
    );
  }

  factory CartState.initial(
      [RetailBranch? branch,
      bool showingMoreProducts = false,
      bool showingOtherProducts = false]) {
    return CartState(
        cartItems: const [],
        branch: branch,
        showingExclusiveProducts: showingMoreProducts,
        showingOtherProducts: showingOtherProducts);
  }

  @override
  List<Object?> get props => [
        cartItems,
        branch,
        note,
        transaction,
        showingExclusiveProducts,
      ];

  List<CartItem> get uniqueCartItems {
    final List<CartItem> items = [];
    final Set<String> seenVariantSupplierIds = {};

    for (final item in cartItems) {
      if (!seenVariantSupplierIds.contains(item.variant.variantSupplierId)) {
        seenVariantSupplierIds.add(item.variant.variantSupplierId);
        items.add(item);
      }
    }

    return items;
  }

  List<CartItem> get uniqueCartItemsWithNonZeroCount {
    final List<CartItem> items = [];
    final Set<String> seenVariantSupplierIds = {};

    for (final item in cartItems) {
      // Only add items with count > 0
      if ((item.count ?? 0) > 0 &&
          !seenVariantSupplierIds.contains(item.variant.variantSupplierId)) {
        seenVariantSupplierIds.add(item.variant.variantSupplierId);
        items.add(item);
      }
    }

    return items;
  }
}
