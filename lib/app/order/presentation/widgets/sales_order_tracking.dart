import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:timelines_plus/timelines_plus.dart';

class SalesOrderTrackingWidget extends ConsumerStatefulWidget {
  const SalesOrderTrackingWidget(this.order, {super.key});

  final Order order;

  @override
  ConsumerState<SalesOrderTrackingWidget> createState() =>
      _SalesOrderTrackingWidgetState();
}

class _SalesOrderTrackingWidgetState
    extends ConsumerState<SalesOrderTrackingWidget> {
  final List<ExportOrderTracking> _exportOrderTrackings = [];

  bool get isExportCountry =>
      ref.read(countryTypeProvider) == CountryType.export;

  late OrderTracking tracking;

  @override
  void initState() {
    tracking = _getOrderTracking();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      height: 552,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Palette.stroke, width: 1),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.04),
            offset: const Offset(0, 2),
            blurRadius: 2,
            spreadRadius: -1,
          ),
        ],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order tracking',
            style:
                textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
          const Gap(32),
          Expanded(
            child: FixedTimeline.tileBuilder(
              theme: TimelineThemeData(
                nodePosition: 0,
                indicatorPosition: 0,
                nodeItemOverlap: false,
                connectorTheme: ConnectorThemeData(
                  thickness: 1,
                  color: Palette.stroke,
                ),
                indicatorTheme: IndicatorThemeData(
                  position: 0.3,
                  size: 10.0,
                  color: Palette.stroke,
                ),
              ),
              builder: TimelineTileBuilder(
                itemCount: 4,
                contentsBuilder: (_, index) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTrackingContent(context, index, tracking),
                      if (index < 4)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Divider(
                            thickness: 1,
                            color: Palette.stroke,
                          ),
                        ),
                    ],
                  );
                },
                indicatorBuilder: (context, index) {
                  final isActive = [
                    tracking.isOrderPlaced,
                    tracking.isOrderConfirmed,
                    tracking.isOrderOutForDelivery,
                    tracking.isOrderDelivered,
                  ][index];
                  return Skeleton.shade(
                    child: SvgPicture.asset(
                      '$kSvgDir/shipment/circle.svg',
                      colorFilter: isActive
                          ? const ColorFilter.mode(
                              Colors.green, BlendMode.srcIn)
                          : const ColorFilter.mode(
                              Colors.orange, BlendMode.srcIn),
                    ),
                  );
                },
                endConnectorBuilder: (_, __) {
                  return SolidLineConnector(
                    color: Palette.stroke,
                  );
                },
              ),
            ),

            // FixedTimeline.tileBuilder(
            //   theme: TimelineThemeData(
            //     nodePosition: 0,
            //     indicatorPosition: 0.35,
            //     nodeItemOverlap: false,
            //     connectorTheme: ConnectorThemeData(
            //       thickness: 1,
            //       color: Palette.stroke,
            //     ),
            //     indicatorTheme: IndicatorThemeData(
            //       position: 0.3,
            //       size: 10.0,
            //       color: Palette.stroke,
            //     ),
            //   ),
            //   builder: TimelineTileBuilder.connected(
            //     connectionDirection: ConnectionDirection.after,
            //     itemCount: 4,
            //     itemExtent: 110,
            //     contentsBuilder: (_, index) {
            //       return Column(
            //         children: [
            //           _buildTrackingContent(
            //               context, index, tracking, widget.order),
            //           const Divider(
            //             height: 1,
            //             thickness: 1,
            //             color: Colors.grey, // Customize color as needed
            //           ),
            //         ],
            //       );
            //     },
            //     indicatorBuilder: (context, index) {
            //       final isActive = [
            //         tracking.isOrderPlaced,
            //         tracking.isOrderConfirmed,
            //         tracking.isOrderOutForDelivery,
            //         tracking.isOrderDelivered,
            //       ][index];
            //       return Skeleton.shade(
            //         child: SvgPicture.asset(
            //           '$kSvgDir/shipment/circle.svg',
            //           colorFilter: isActive
            //               ? const ColorFilter.mode(
            //                   Colors.green, BlendMode.srcIn)
            //               : const ColorFilter.mode(
            //                   Colors.orange, BlendMode.srcIn),
            //         ),
            //       );
            //     },
            //     connectorBuilder: (context, index, connectorType) {
            //       return SolidLineConnector(
            //         color: Palette.stroke,
            //       );
            //     },
            //   ),
            // ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicator(bool isActive) {
    return Skeleton.shade(
      child: SvgPicture.asset(
        '$kSvgDir/shipment/circle.svg',
        colorFilter: isActive
            ? const ColorFilter.mode(Colors.green, BlendMode.srcIn)
            : const ColorFilter.mode(Colors.orange, BlendMode.srcIn),
      ),
    );
  }

  Widget _buildTrackingContent(
      BuildContext context, int index, OrderTracking tracking) {
    final textTheme = Theme.of(context).textTheme;
    final order = widget.order;

    print('tracking $tracking');

    switch (index) {
      case 0:
        return buildTrackingStep(
          context,
          'Order Placed',
          tracking.orderPlacedDate?.toFullDateTime() ?? '',
          isActive: tracking.isOrderPlaced,
          isLast: !tracking.isOrderConfirmed,
        );
      case 1:
        return buildTrackingStep(
          context,
          'Order Confirmed',
          tracking.orderConfirmedDate?.toFullDateTime() ?? '',
          isActive: tracking.isOrderConfirmed,
          isLast: !tracking.isOrderOutForDelivery,
        );
      case 2:
        return buildTrackingStep(
          context,
          'Out for Delivery',
          tracking.orderOutForDeliveryDate?.toFullDateTime() ?? '',
          extraContent: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (tracking.isOrderOutForDelivery &&
                  order.dispatchUser != null) ...[
                const Gap(10),
                Text(
                  'Driver name',
                  style: textTheme.bodyLarge?.copyWith(
                    color: Palette.blackSecondary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  order.dispatchUser!.name!,
                  style: textTheme.bodyLarge,
                ),
              ],
            ],
          ),
          isActive: tracking.isOrderOutForDelivery,
          isLast: tracking.isOrderDelivered,
        );
      case 3:
        return buildTrackingStep(
          context,
          'Delivered',
          tracking.orderDeliveredDate?.toFullDateTime() ?? '',
          isActive: tracking.isOrderDelivered,
          isLast: true,
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget buildTrackingStep(
    BuildContext context,
    String status,
    String dateTime, {
    bool isActive = false,
    bool isLast = false,
    Widget? extraContent,
  }) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            status,
            style: textTheme.labelMedium?.copyWith(fontSize: 16),
          ),
          const Gap(4),
          Row(
            children: [
              if (dateTime.isNotEmpty)
                Skeleton.shade(
                  child: SvgPicture.asset(
                    '$kSvgDir/shipment/calendar.svg',
                  ),
                ),
              const Gap(4),
              Text(
                dateTime,
                style: textTheme.bodyMedium?.copyWith(
                    color: Palette.blackSecondary,
                    fontWeight: FontWeight.w400,
                    fontSize: 12),
              ),
            ],
          ),
          if (extraContent != null) ...[
            const Gap(4),
            extraContent,
          ],
        ],
      ),
    );
  }

  OrderTracking _getOrderTracking() {
    final order = widget.order;

    final autoApprovalEvent = (order.history ?? [])
        .firstWhereOrNull((event) => event.event == "AUTO_APPROVAL");

    if (isExportCountry) {
      final orderPlaced = _exportOrderTrackings.firstWhere(
        (el) => el.status.toLowerCase() == "order placed",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );
      final orderScheduled = _exportOrderTrackings.firstWhere(
        (el) => el.status.toLowerCase() == "scheduled",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );
      final orderOutForDelivery = _exportOrderTrackings.firstWhere(
        (el) =>
            el.status.toLowerCase() == "out for delivery" ||
            el.status.toLowerCase() == "shipped",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );
      final orderDelivered = _exportOrderTrackings.firstWhere(
        (el) => el.status.toLowerCase() == "delivered",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );

      return OrderTracking(
        isOrderPlaced: orderPlaced.status != "unknown",
        orderPlacedDate: orderPlaced.createdAt ?? order.createdAt,
        isOrderConfirmed: orderScheduled.status != "unknown",
        orderConfirmedDate: orderScheduled.createdAt ??
            autoApprovalEvent?.createdAt ??
            order.scheduledAt,
        isOrderOutForDelivery: orderOutForDelivery.status != "unknown",
        orderOutForDeliveryDate:
            orderOutForDelivery.createdAt ?? order.dispatchedAt,
        isOrderDelivered: orderDelivered.status != "unknown",
        orderDeliveredDate: orderDelivered.createdAt ?? order.deliveredAt,
      );
    }

    // Fallback for non-UK orders
    final shippingStatus = (order.shippingStatus ?? '').toLowerCase();
    final isOrderDelivered = ["delivered", "shipped"].contains(shippingStatus);
    final isOrderOutForDelivery =
        ["dispatched"].contains(shippingStatus) || isOrderDelivered;
    final isOrderConfirmed =
        order.approvalStatus?.toLowerCase() == 'approved' ||
            autoApprovalEvent?.createdAt != null ||
            isOrderOutForDelivery;
    final isOrderPlaced =
        ["pending", "cancelled"].contains(shippingStatus) || isOrderConfirmed;

    return OrderTracking(
      isOrderPlaced: isOrderPlaced,
      orderPlacedDate: isOrderPlaced ? order.createdAt : null,
      isOrderConfirmed: isOrderConfirmed,
      orderConfirmedDate: isOrderConfirmed
          ? autoApprovalEvent?.createdAt ?? order.scheduledAt
          : null,
      isOrderOutForDelivery: isOrderOutForDelivery,
      orderOutForDeliveryDate:
          isOrderOutForDelivery ? order.dispatchedAt ?? order.shippedAt : null,
      isOrderDelivered: isOrderDelivered,
      orderDeliveredDate: isOrderDelivered ? order.deliveredAt : null,
    );
  }

  Future<void> getTrackingDetailsById() async {
    final tracking = OrderTracking(
      isOrderPlaced: true,
      orderPlacedDate: widget.order.createdAt,
      isOrderConfirmed: false,
      isOrderOutForDelivery: false,
      isOrderDelivered: false,
    );

    setState(() {
      this.tracking = tracking;
    });
  }

  OrderTracking processExportOrderTracking(
    List<ExportOrderTracking> exportOrderTrackings,
  ) {
    final orderPlaced = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "order placed",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );
    final orderScheduled = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "scheduled",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );
    final orderOutForDelivery = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "out for delivery",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );
    final orderDelivered = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "delivered",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );

    return OrderTracking(
      isOrderPlaced: orderPlaced.status != "unknown",
      orderPlacedDate: orderPlaced.createdAt,
      isOrderConfirmed: orderScheduled.status != "unknown",
      orderConfirmedDate: orderScheduled.createdAt,
      isOrderOutForDelivery: orderOutForDelivery.status != "unknown",
      orderOutForDeliveryDate: orderOutForDelivery.createdAt,
      isOrderDelivered: orderDelivered.status != "unknown",
      orderDeliveredDate: orderDelivered.createdAt,
    );
  }
}
