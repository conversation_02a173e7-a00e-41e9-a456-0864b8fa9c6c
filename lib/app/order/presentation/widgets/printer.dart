import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:printing/printing.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';

class OrderPrinter {
  final PrintOrderData printOrder;
  final RetailBranch branch;
  final bool isExportCountry;
  final String title;

  OrderPrinter(this.printOrder, this.branch, this.isExportCountry, this.title);

  PrintOrderCalculation get cal => printOrder.poOrderCalculation;

  String get address => (branch.contactPhone ?? '').startsWith('+44')
      ? 'Tradedepot Inc.\n60 Capitol way,\nColindale,\nLondon NW90BR'
      : 'TradeDepot \n3/4 Adewunmi Industrial Estate\nKudirat Abiola Way,\nOregun Ikeja,\nLagos Nigeria';

  Future<Uint8List> generatePdf([PdfPageFormat? format]) async {
    final pdf = Document();
    const double padding = 4;

    final svgRectangle = await rootBundle.loadString(kLogoSvg);

    String formatCurrency(num amount, String? isoCode) {
      final formattedAmount = amount < 0 ? 0 : amount;
      final format = NumberFormat.simpleCurrency(name: isoCode);
      return format.format(formattedAmount);
    }

    final roboto = TextStyle(
      font: await PdfGoogleFonts.robotoLight(),
    );

    final robotoBold = TextStyle(
      font: await PdfGoogleFonts.robotoBold(),
    );

    pdf.addPage(
      MultiPage(
        pageFormat: format ?? PdfPageFormat.a4.copyWith(marginTop: 40),
        header: (context) {
          if (context.pageNumber > 1) {
            return SizedBox.shrink(); // Header only on first page
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Flexible(
                    child: Text(
                      title,
                      style: robotoBold.copyWith(fontSize: 20),
                    ),
                  ),
                  Flexible(
                    child: SizedBox(
                      height: 24,
                      width: 24,
                      child: SvgImage(svg: svgRectangle, fit: BoxFit.scaleDown),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 40),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PageItemRow('Order number', printOrder.poOrderReference),
                  PageItemRow('Date',
                      printOrder.poOrders.first.createdAt?.toDate() ?? ''),
                  SizedBox(height: 30),
                  PageItemRow('Ship to', 'Supplier', useBoldFontWeight: true),
                  SizedBox(height: 3),
                  PageItemRow(
                      '${branch.outletBusinessName}\n${branch.streetName != null ? '${branch.streetName}' : ''}',
                      address),
                ],
              ),
              SizedBox(height: 60),
            ],
          );
        },
        footer: (context) {
          if (context.pagesCount > 1) {
            return Align(
              alignment: Alignment.bottomRight,
              child: Text(
                'Page ${context.pageNumber} of ${context.pagesCount}',
                style: roboto.copyWith(fontSize: 10, color: PdfColors.grey),
                textAlign: TextAlign.right,
              ),
            );
          }
          return SizedBox.shrink();
        },
        build: (Context context) => [
          // Table Header
          Table(
            columnWidths: {
              0: const FlexColumnWidth(8),
              1: const FlexColumnWidth(3),
              2: const FlexColumnWidth(5),
              3: const FlexColumnWidth(5),
              4: const FlexColumnWidth(3),
              5: const FlexColumnWidth(5),
            },
            children: [
              TableRow(
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(color: PageColors.primaryBlack))),
                children: [
                  Padding(
                      padding: const EdgeInsets.all(padding),
                      child: Text('Description')),
                  Padding(
                      padding: const EdgeInsets.all(padding),
                      child: Text('Qty')),
                  Padding(
                      padding: const EdgeInsets.all(padding),
                      child: Text('Unit Price')),
                  Padding(
                      padding: const EdgeInsets.all(padding),
                      child: Text('Discount')),
                  Padding(
                      padding: const EdgeInsets.all(padding),
                      child: Text('Tax')),
                  Padding(
                      padding: const EdgeInsets.all(padding),
                      child: Text('Amount')),
                ],
              ),
            ],
          ),

          // Dynamically paginated table content
          ...printOrder.poOrders.map((order) => Table(
                columnWidths: {
                  0: const FlexColumnWidth(8),
                  1: const FlexColumnWidth(3),
                  2: const FlexColumnWidth(5),
                  3: const FlexColumnWidth(5),
                  4: const FlexColumnWidth(3),
                  5: const FlexColumnWidth(5),
                },
                children: [
                  TableRow(
                    children: [
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(order.name)),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(order.quantity.toString())),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                              formatCurrency(order.price, order.currency.iso),
                              style: roboto)),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                              formatCurrency(
                                  order.discount, order.currency.iso),
                              style: roboto)),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                              formatCurrency(order.tax, order.currency.iso),
                              style: roboto)),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                              formatCurrency(order.total, order.currency.iso),
                              style: roboto)),
                    ],
                  ),
                ],
              )),

          // Summary Section
          SizedBox(height: 30),
          // if (cal.discount > 0)
          PageSummaryRow(
              'Subtotal', formatCurrency(cal.subtotal, printOrder.currency.iso),
              style: roboto),
          if (isExportCountry)
            PageSummaryRow('Shipping Fee',
                formatCurrency(cal.shippingFees, printOrder.currency.iso),
                style: roboto),
          PageSummaryRow(
              'Discount', formatCurrency(cal.discount, printOrder.currency.iso),
              style: roboto),
          if (cal.taxes > 0)
            PageSummaryRow(
                'Taxes', formatCurrency(cal.taxes, printOrder.currency.iso),
                style: roboto),
          PageSummaryRow(
              'Total', formatCurrency(cal.total, printOrder.currency.iso),
              style: roboto),
          SizedBox(height: 4),
          PageSummaryRow('Estimated Amount',
              '${formatCurrency(cal.amountDue, printOrder.currency.iso)} ${printOrder.currency.iso}',
              useBoldFontWeight: true, style: robotoBold),
          Spacer(),
          Divider(color: PageColors.kE7E7E7),
          SizedBox(height: 10),
          Text(
            '${printOrder.poOrderReference} - ${formatCurrency(cal.amountDue, printOrder.currency.iso)} ${printOrder.currency.iso}',
            style: roboto.copyWith(color: PageColors.k6B797C),
          ),
        ],
      ),
    );

    return pdf.save();
  }

  // ===========================================================================

  // Future<Uint8List> generatePdf([PdfPageFormat? format]) async {
  //   final pdf = Document();
  //   const double padding = 4;

  //   final svgRectangle =
  //       await rootBundle.loadString('$kSvgDir/order/rectangle.svg');

  //   String formatCurrency(num amount, String? isoCode) {
  //     final format =
  //         NumberFormat.simpleCurrency(name: isoCode);
  //     return format.format(amount);
  //   }

  //   final roboto = TextStyle(
  //     font: await PdfGoogleFonts.robotoLight(),
  //   );

  //   final robotoBold = TextStyle(
  //     font: await PdfGoogleFonts.robotoBold(),
  //   );

  //   // Calculate available height after top section on first page
  //   final pageHeight = (format ?? PdfPageFormat.a4).height;
  //   const topSectionHeight =
  //       145; // Estimate height of top section on first page
  //   const rowHeight = 50; // Approximate height of each row
  //   final maxRowsFirstPage =
  //       ((pageHeight - topSectionHeight) / rowHeight).floor();

  //   // Divide orders into pages based on available height for the first page
  //   final List<List<PrintOrderItem>> pages = [];
  //   int startIndex = 0;
  //   bool isFirstPage = true;

  //   while (startIndex < printOrder.poOrders.length) {
  //     final rowLimit = isFirstPage
  //         ? maxRowsFirstPage
  //         : ((pageHeight / rowHeight).floor() - 1);
  //     final endIndex = (startIndex + rowLimit < printOrder.poOrders.length)
  //         ? startIndex + rowLimit
  //         : printOrder.poOrders.length;
  //     pages.add(printOrder.poOrders.sublist(startIndex, endIndex));
  //     startIndex = endIndex;
  //     isFirstPage = false;
  //   }

  //   for (var i = 0; i < pages.length; i++) {
  //     pdf.addPage(
  //       Page(
  //         pageFormat: format ?? PdfPageFormat.a4.copyWith(marginTop: 40),
  //         build: (Context context) {
  //           return Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               if (i == 0) ...[
  //                 // Header content for the first page
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   crossAxisAlignment: CrossAxisAlignment.center,
  //                   children: [
  //                     Flexible(
  //                       child: Text(
  //                         'Purchase Order',
  //                         style: robotoBold.copyWith(fontSize: 20),
  //                       ),
  //                     ),
  //                     Flexible(
  //                       child: SizedBox(
  //                         height: 25,
  //                         width: 25,
  //                         child: SvgImage(
  //                             svg: svgRectangle, fit: BoxFit.scaleDown),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 SizedBox(height: 40),
  //                 Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     PageItemRow('Order number', printOrder.poOrderReference),
  //                     PageItemRow('Date',
  //                         printOrder.poOrders.first.createdAt?.toDate() ?? ''),
  //                     SizedBox(height: 30),
  //                     PageItemRow('Ship to', 'Supplier',
  //                         useBoldFontWeight: true),
  //                     SizedBox(height: 3),
  //                     PageItemRow(
  //                         branch.outletBusinessName +
  //                             (branch.streetName != null
  //                                 ? ', ${branch.streetName}'
  //                                 : ''),
  //                         address),
  //                   ],
  //                 ),
  //                 SizedBox(height: 60),
  //               ],

  //               // Table Header
  //               Table(
  //                 columnWidths: {
  //                   0: const FlexColumnWidth(8),
  //                   1: const FlexColumnWidth(3),
  //                   2: const FlexColumnWidth(5),
  //                   3: const FlexColumnWidth(5),
  //                   4: const FlexColumnWidth(3),
  //                   5: const FlexColumnWidth(5),
  //                 },
  //                 children: [
  //                   TableRow(
  //                     decoration: BoxDecoration(
  //                         border: Border(
  //                             bottom:
  //                                 BorderSide(color: PageColors.primaryBlack))),
  //                     children: [
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Description')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Qty')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Unit Price')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Discount')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Tax')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Amount')),
  //                     ],
  //                   ),
  //                 ],
  //               ),

  //               // Table content for the current page
  //               Table(
  //                 columnWidths: {
  //                   0: const FlexColumnWidth(8),
  //                   1: const FlexColumnWidth(3),
  //                   2: const FlexColumnWidth(5),
  //                   3: const FlexColumnWidth(5),
  //                   4: const FlexColumnWidth(3),
  //                   5: const FlexColumnWidth(5),
  //                 },
  //                 children: [
  //                   for (final order in pages[i])
  //                     TableRow(
  //                       children: [
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(order.name)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(order.quantity.toString())),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(
  //                                     order.price, order.currency.iso),
  //                                 style: roboto)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(
  //                                     order.discount, order.currency.iso),
  //                                 style: roboto)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(order.tax, order.currency.iso),
  //                                 style: roboto)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(
  //                                     order.total, order.currency.iso),
  //                                 style: roboto)),
  //                       ],
  //                     ),
  //                 ],
  //               ),

  //               if (i == pages.length - 1) ...[
  //                 SizedBox(height: 30),
  //                 if (cal.discount > 0)
  //                   PageSummaryRow('Discount',
  //                       formatCurrency(cal.discount, printOrder.currency.iso),
  //                       style: roboto),
  //                 PageSummaryRow('Subtotal',
  //                     formatCurrency(cal.subtotal, printOrder.currency.iso),
  //                     style: roboto),
  //                 if (cal.taxes > 0)
  //                   PageSummaryRow('Taxes',
  //                       formatCurrency(cal.taxes, printOrder.currency.iso),
  //                       style: roboto),
  //                 PageSummaryRow('Total',
  //                     formatCurrency(cal.total, printOrder.currency.iso),
  //                     style: roboto),
  //                 SizedBox(height: 4),
  //                 PageSummaryRow('Amount Due',
  //                     '${formatCurrency(cal.amountDue, printOrder.currency.iso)} ${printOrder.currency.iso}',
  //                     useBoldFontWeight: true, style: robotoBold),
  //                 Spacer(),
  //                 Divider(color: PageColors.kE7E7E7),
  //                 SizedBox(height: 10),
  //                 Text(
  //                   '${printOrder.poOrderReference} - ${formatCurrency(cal.amountDue, printOrder.currency.iso)} ${printOrder.currency.iso}',
  //                   style: roboto.copyWith(color: PageColors.k6B797C),
  //                 ),
  //               ],
  //             ],
  //           );
  //         },
  //       ),
  //     );
  //   }

  //   return pdf.save();
  // }

  // ===========================================================================

  // Future<Uint8List> generatePdf([PdfPageFormat? format]) async {
  //   final pdf = Document();
  //   const double padding = 4;

  //   final svgRectangle =
  //       await rootBundle.loadString('$kSvgDir/order/rectangle.svg');

  //   String formatCurrency(num amount, String? isoCode) {
  //     final format =
  //         NumberFormat.simpleCurrency(name: isoCode);
  //     return format.format(amount);
  //   }

  //   final roboto = TextStyle(
  //     font: await PdfGoogleFonts.robotoLight(),
  //   );

  //   final robotoBold = TextStyle(
  //     font: await PdfGoogleFonts.robotoBold(),
  //   );

  //   // Define the row limits for pagination
  //   const int firstPageRowLimit = 13; // Adjust based on top content
  //   const int otherPageRowLimit = 24; // Rows per page for subsequent pages

  //   // Split orders for pagination
  //   final List<List<PrintOrderItem>> pages = [];
  //   int startIndex = 0;
  //   bool isFirstPage = true;

  //   while (startIndex < printOrder.poOrders.length) {
  //     final rowLimit = isFirstPage ? firstPageRowLimit : otherPageRowLimit;
  //     final endIndex = (startIndex + rowLimit < printOrder.poOrders.length)
  //         ? startIndex + rowLimit
  //         : printOrder.poOrders.length;
  //     pages.add(printOrder.poOrders.sublist(startIndex, endIndex));
  //     startIndex = endIndex;
  //     isFirstPage = false;
  //   }

  //   for (var i = 0; i < pages.length; i++) {
  //     pdf.addPage(
  //       Page(
  //         pageFormat: format ??
  //             PdfPageFormat.a4.copyWith(
  //               marginTop: 30,
  //             ),
  //         build: (Context context) {
  //           return Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               if (i == 0) ...[
  //                 // Content for the first page header
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   crossAxisAlignment: CrossAxisAlignment.center,
  //                   children: [
  //                     Flexible(
  //                       child: Text(
  //                         'Purchase Order',
  //                         style: robotoBold.copyWith(fontSize: 20),
  //                       ),
  //                     ),
  //                     Flexible(
  //                       child: SizedBox(
  //                         height: 20,
  //                         width: 20,
  //                         child: SvgImage(
  //                             svg: svgRectangle, fit: BoxFit.scaleDown),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 SizedBox(height: 40),
  //                 Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     PageItemRow('Order number', printOrder.poOrderReference),
  //                     PageItemRow('Date',
  //                         printOrder.poOrders.first.createdAt?.toDate() ?? ''),
  //                     SizedBox(height: 30),
  //                     PageItemRow('Ship to', 'Supplier',
  //                         useBoldFontWeight: true),
  //                     SizedBox(height: 3),
  //                     PageItemRow(
  //                         branch.outletBusinessName +
  //                             (branch.streetName != null
  //                                 ? ', ${branch.streetName}'
  //                                 : ''),
  //                         address),
  //                   ],
  //                 ),
  //                 SizedBox(height: 60),
  //               ],

  //               // Table header
  //               Table(
  //                 columnWidths: {
  //                   0: const FlexColumnWidth(8),
  //                   1: const FlexColumnWidth(3),
  //                   2: const FlexColumnWidth(5),
  //                   3: const FlexColumnWidth(5),
  //                   4: const FlexColumnWidth(3),
  //                   5: const FlexColumnWidth(5),
  //                 },
  //                 children: [
  //                   TableRow(
  //                     decoration: BoxDecoration(
  //                         border: Border(
  //                             bottom:
  //                                 BorderSide(color: PageColors.primaryBlack))),
  //                     children: [
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Description')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Qty')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Unit Price')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Discount')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Tax')),
  //                       Padding(
  //                           padding: const EdgeInsets.all(padding),
  //                           child: Text('Amount')),
  //                     ],
  //                   ),
  //                 ],
  //               ),

  //               // Table content for current page
  //               Table(
  //                 columnWidths: {
  //                   0: const FlexColumnWidth(8),
  //                   1: const FlexColumnWidth(3),
  //                   2: const FlexColumnWidth(5),
  //                   3: const FlexColumnWidth(5),
  //                   4: const FlexColumnWidth(3),
  //                   5: const FlexColumnWidth(5),
  //                 },
  //                 children: [
  //                   for (final order in pages[i])
  //                     TableRow(
  //                       children: [
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(order.name)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(order.quantity.toString())),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(
  //                                     order.price, order.currency.iso),
  //                                 style: roboto)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(
  //                                     order.discount, order.currency.iso),
  //                                 style: roboto)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(order.tax, order.currency.iso),
  //                                 style: roboto)),
  //                         Padding(
  //                             padding: const EdgeInsets.all(padding),
  //                             child: Text(
  //                                 formatCurrency(
  //                                     order.total, order.currency.iso),
  //                                 style: roboto)),
  //                       ],
  //                     ),
  //                 ],
  //               ),

  //               if (i == pages.length - 1) ...[
  //                 SizedBox(height: 30),
  //                 if (cal.discount > 0)
  //                   PageSummaryRow('Discount',
  //                       formatCurrency(cal.discount, printOrder.currency.iso),
  //                       style: roboto),
  //                 PageSummaryRow('Subtotal',
  //                     formatCurrency(cal.subtotal, printOrder.currency.iso),
  //                     style: roboto),
  //                 if (cal.taxes > 0)
  //                   PageSummaryRow('Taxes',
  //                       formatCurrency(cal.taxes, printOrder.currency.iso),
  //                       style: roboto),
  //                 PageSummaryRow('Total',
  //                     formatCurrency(cal.total, printOrder.currency.iso),
  //                     style: roboto),
  //                 SizedBox(height: 4),
  //                 PageSummaryRow('Amount Due',
  //                     '${formatCurrency(cal.amountDue, printOrder.currency.iso)} ${printOrder.currency.iso}',
  //                     useBoldFontWeight: true, style: robotoBold),
  //                 Spacer(),
  //                 Divider(color: PageColors.kE7E7E7),
  //                 SizedBox(height: 10),
  //                 Text(
  //                   '${printOrder.poOrderReference} - ${formatCurrency(cal.amountDue, printOrder.currency.iso)} ${printOrder.currency.iso}',
  //                   style: roboto.copyWith(color: PageColors.k6B797C),
  //                 ),
  //               ],
  //             ],
  //           );
  //         },
  //       ),
  //     );
  //   }

  //   return pdf.save();
  // }

  /// Triggers the printing of the generated PDF
  Future<void> printPdf(Uint8List? uint8List) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async {
        // If uint8List is provided, use it; otherwise, generate a new PDF.
        return uint8List ?? await generatePdf(format);
      },
    );
  }
}

const textStyle = TextStyle();

class PageColors {
  static final blackSecondary = PdfColor.fromHex("#6B797C");
  static final primaryBlack = PdfColor.fromHex("#081F24");
  static final k6B797C = PdfColor.fromHex("#6B797C");
  static final kE7E7E7 = PdfColor.fromHex("#E7E7E7");
}

class PageItemRow extends StatelessWidget {
  final String title;
  final String content;
  final bool? useBoldFontWeight;
  PageItemRow(
    this.title,
    this.content, {
    this.useBoldFontWeight = false,
  });

  @override
  Widget build(Context context) {
    // const fontFamily = 'HelveticaNeue';

    final style = textStyle.copyWith(
      font: useBoldFontWeight! ? Font.helveticaBold() : Font.helvetica(),
      color: useBoldFontWeight!
          ? PageColors.primaryBlack
          : PageColors.blackSecondary,
    );

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: Text(
            title,
            style: useBoldFontWeight!
                ? style.copyWith(fontWeight: FontWeight.bold)
                : style,
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            content,
            style: useBoldFontWeight!
                ? style.copyWith(fontWeight: FontWeight.bold)
                : style,
          ),
        ),
        Expanded(
          flex: 3,
          child: Container(),
        )
      ],
    );
  }
}

class PageSummaryRow extends StatelessWidget {
  final String title;
  final String content;
  final bool? showBorder;
  final bool? useBoldFontWeight;
  final TextStyle style;

  PageSummaryRow(
    this.title,
    this.content, {
    this.showBorder = true,
    this.useBoldFontWeight = false,
    required this.style,
  });

  @override
  Widget build(Context context) {
    // const fontFamily = 'HelveticaNeue';
    final itemStyle = textStyle.copyWith(
      font: Font.helvetica(),
      color: useBoldFontWeight!
          ? PageColors.primaryBlack
          : PageColors.blackSecondary,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(),
          ),
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: showBorder!
                              ? BorderSide(color: PageColors.kE7E7E7)
                              : BorderSide.none)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          title,
                          style: useBoldFontWeight!
                              ? style.copyWith(fontWeight: FontWeight.bold)
                              : itemStyle,
                        ),
                      ),
                      Flexible(
                        child: Text(
                          content,
                          style: useBoldFontWeight!
                              ? style.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: useBoldFontWeight!
                                      ? PageColors.primaryBlack
                                      : PageColors.blackSecondary,
                                )
                              : style.copyWith(
                                  color: useBoldFontWeight!
                                      ? PageColors.primaryBlack
                                      : PageColors.blackSecondary,
                                ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
