import 'package:flutter/material.dart' hide SearchBar;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/screens/products_screen.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_grid_item.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/empty_widget.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class NonExportProductsWidget extends ConsumerStatefulWidget {
  const NonExportProductsWidget({super.key});

  @override
  ConsumerState<NonExportProductsWidget> createState() =>
      _NonExportProductsWidgetState();
}

class _NonExportProductsWidgetState
    extends ConsumerState<NonExportProductsWidget> {
  final _controller = TextEditingController();

  int activeIndex = -1;
  String selectedCategory = 'All';
  String? searchTerm;

  @override
  void initState() {
    _controller.addListener(_onTextChanged);
    super.initState();
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    setState(() {
      searchTerm = text.isEmpty ? null : text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final orderState = ref.watch(orderControllerProvider);
    final outletVariants = orderState.outletVariants;
    // final collections = orderState.collections;
    final exclusiveCollections = orderState.exclusiveCollections;
    final showingMoreProducts =
        ref.watch(cartProvider).showingExclusiveProducts;
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    final cartState = ref.watch(cartProvider);

    // log('collections: ${collections.valueOrNull?.expand((x) => x.validVariants).length}');
    // log('exclusiveCollections: ${exclusiveCollections.valueOrNull?.expand((x) => x.validVariants).length}');

    final showMoreProductsText = exclusiveCollections.isLoading
        ? 'Loading Products, please wait...'
        : exclusiveCollections.hasError
            ? 'Error occurred, retry'
            : 'View Catalog';

    void handlePressed() {
      if (exclusiveCollections.isLoading) return;

      if (exclusiveCollections.hasError) {
        final hexCode = ref.read(hexCodeProvider);
        if (hexCode != null) {
          ref
              .read(orderControllerProvider.notifier)
              .fetchCollections(hexCode, forced: true);
        }
      } else {
        ref.read(cartProvider.notifier).setShowingExclusiveProducts(true);
      }
    }

    return Scaffold(
      body: Column(
        children: [
          TopBarWidget(
            _controller,
            activeIndex: activeIndex,
            selectedCategory: selectedCategory,
            onIndexChanged: (index) {
              setState(() {
                activeIndex = index;
              });
            },
            onCategoryChanged: (category) {
              setState(() {
                selectedCategory = category;
              });
            },
            variants: isExportCountry
                ? orderState.exportVariants
                : orderState.outletVariants,
            moreProductVariants: orderState.exclusiveVariants,
            showingMoreProducts: showingMoreProducts,
          ),
          Expanded(
            child: Stack(
              children: [
                outletVariants.when(
                  data: (data) {
                    final isProductsEmpty =
                        cartState.showingExclusiveProducts &&
                            exclusiveProducts(orderState.exclusiveVariants,
                                    selectedCategory, searchTerm)
                                .isEmpty &&
                            getYourListings(data, selectedCategory, searchTerm)
                                .isEmpty;

                    if (data.isEmpty && !cartState.showingExclusiveProducts) {
                      return const CustomScrollView(
                        slivers: [
                          SliverFillRemaining(
                            hasScrollBody: false,
                            child: EmptyWidget(
                              icon: '$kSvgDir/order/cart.svg',
                              title: 'No items for your location.',
                              subTitle:
                                  "We're expanding quickly so please check back soon.",
                              // title: 'No products available',
                              // subTitle:
                              //     'There are currently no available\nproducts in the inventory',
                              baseline: false,
                            ),
                          ),
                        ],
                      );
                    }

                    return CustomScrollView(
                      slivers: [
                        const SliverToBoxAdapter(
                          child: Gap(20),
                        ),
                        if (getYourListings(data, selectedCategory, searchTerm)
                            .isNotEmpty) ...[
                          SliverToBoxAdapter(
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 40),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Your Listings',
                                    style: textTheme.headlineSmall,
                                  ),
                                  const Gap(10),
                                ],
                              ),
                            ),
                          ),
                          SliverPadding(
                            padding: const EdgeInsets.symmetric(horizontal: 40),
                            sliver: SliverGrid(
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3, // Number of columns
                                crossAxisSpacing: 10,
                                mainAxisSpacing: 10,
                                childAspectRatio: 1.2,
                              ),
                              delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  return VariantGridItem(
                                      getYourListings(data, selectedCategory,
                                          searchTerm)[index],
                                      key: UniqueKey());
                                },
                                childCount: getYourListings(
                                        data, selectedCategory, searchTerm)
                                    .length, // Number of items in the first grid
                              ),
                            ),
                          ),
                        ],
                        if (cartState.showingExclusiveProducts) ...[
                          if (isProductsEmpty)
                            const SliverFillRemaining(
                              hasScrollBody: false,
                              child: EmptyWidget(
                                icon: '$kSvgDir/order/cart.svg',
                                title: 'No products available',
                                subTitle:
                                    'There are currently no products matching your search\nin the inventory',
                                baseline: false,
                              ),
                            )
                          else if (exclusiveProducts(
                                  orderState.exclusiveVariants,
                                  selectedCategory,
                                  searchTerm)
                              .isNotEmpty)
                            for (MapEntry<String, List<Variant>> exclusive
                                in exclusiveProducts(
                                    orderState.exclusiveVariants,
                                    selectedCategory,
                                    searchTerm)) ...[
                              SliverToBoxAdapter(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 40),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        exclusive.key,
                                        style: textTheme.headlineSmall,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SliverPadding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 40),
                                sliver: SliverGrid(
                                  gridDelegate:
                                      const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 3, // Number of columns
                                    crossAxisSpacing: 10,
                                    mainAxisSpacing: 10,
                                    childAspectRatio: 1.2,
                                  ),
                                  delegate: SliverChildBuilderDelegate(
                                    (context, index) {
                                      return VariantGridItem(
                                          exclusive.value[index],
                                          key: UniqueKey());
                                    },
                                    childCount: exclusive.value
                                        .length, // Number of items in the first grid
                                  ),
                                ),
                              ),
                            ],
                        ],
                      ],
                    );
                  },
                  loading: () => loadingWidget(context),
                  error: (e, s) {
                    return CustomScrollView(
                      slivers: [
                        SliverFillRemaining(
                          hasScrollBody: false,
                          child: Center(
                            child: FailureWidget(
                              // fullScreen: true,
                              heightFactor: 0.7,
                              e: e,
                              retry: () => ref
                                  .read(orderControllerProvider.notifier)
                                  .fetchOutletVariants(),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                if (!cartState.showingExclusiveProducts &&
                    (exclusiveCollections is AsyncLoading ||
                        (exclusiveCollections is AsyncData &&
                            exclusiveCollections.value!.isNotEmpty)))
                  Positioned(
                    bottom: 0,
                    right: 0,
                    left: 0,
                    child: Container(
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Palette.stroke),
                      ),
                      child: Center(
                        child: TextButton(
                          onPressed: handlePressed,
                          child: Text(
                            showMoreProductsText,
                            style: textTheme.bodyMedium?.copyWith(
                              color: Palette.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

List<Variant> getYourListings(
    List<Variant> variants, String category, String? searchTerm) {
  final pm = ProductsManager(variants);
  return pm.getItemsByCategory(category, searchTerm);
}

List<MapEntry<String, List<Variant>>> exclusiveProducts(
    List<Variant> variants, String category, String? searchTerm) {
  final pm = ProductsManager(variants);
  return pm.getGroupedItemsByCategory(category, searchTerm).entries.toList();
}
