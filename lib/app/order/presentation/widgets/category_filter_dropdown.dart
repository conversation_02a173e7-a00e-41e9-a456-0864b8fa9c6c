import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class CategoryFilterDropdown extends StatelessWidget {
  final List<String> categoryFilter;
  final String selectedCategory;
  final Function(String) onCategoryChanged;
  final Function(int) onIndexChanged;
  final bool loading;

  const CategoryFilterDropdown({
    required this.categoryFilter,
    required this.selectedCategory,
    required this.onCategoryChanged,
    required this.onIndexChanged,
    this.loading = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 42,
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Skeletonizer(
        enabled: loading,
        child: DropdownButtonWidget<String>(
          isExpanded: true,
          menuMaxHeight: 300,
          hint: 'Select a Category',
          selectedValue: selectedCategory,
          onChanged: (String? newValue) {
            int index = categoryFilter.indexOf(newValue!);
            onIndexChanged(index - 1);
            onCategoryChanged(newValue);
          },
          options: categoryFilter,
          itemToString: (item) => item,
        ),
      ),
    );
  }
}
