import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/domain/use_cases/order_use_cases.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/widgets/delete_confirmation_dialog.dart';
import 'package:td_procurement/app/order/presentation/widgets/printer.dart';
import 'package:td_procurement/app/order/presentation/widgets/status_badge.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

final deletingIdProvider = StateProvider<String>((_) => '');

class OrdersTableWidget extends ConsumerStatefulWidget {
  final List<Transaction> transactions;

  const OrdersTableWidget(this.transactions, {super.key});

  @override
  ConsumerState<OrdersTableWidget> createState() => _OrdersTableWidgetState();
}

class _OrdersTableWidgetState extends ConsumerState<OrdersTableWidget> {
  final loading = ValueNotifier<bool>(false);

  void clearDeletingId() {
    ref.read(deletingIdProvider.notifier).state = '';
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final orderNotifier = ref.read(orderControllerProvider.notifier);
    final deletingId = ref.watch(deletingIdProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: _buildTransactionsList(textTheme, orderNotifier, deletingId),
        ),
      ],
    );
  }

  Widget _buildTransactionsList(
    TextTheme textTheme,
    OrderController orderNotifier,
    String deletingId,
  ) {
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => _buildTransactionItem(
              transaction: widget.transactions[index],
              textTheme: textTheme,
              orderNotifier: orderNotifier,
              deletingId: deletingId,
              index: index,
            ),
            childCount: widget.transactions.length,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionItem({
    required Transaction transaction,
    required TextTheme textTheme,
    required OrderController orderNotifier,
    required String deletingId,
    required int index,
  }) {
    return IgnorePointer(
      ignoring: deletingId == transaction.id,
      child: HoverableContainer(
        index: index,
        builder: (isHovered) => InkWell(
          onTap: () => _handleTransactionTap(transaction, orderNotifier),
          child: _buildTableRow(
            transaction,
            isHovered,
            textTheme,
            deletingId == transaction.id,
          ),
        ),
      ),
    );
  }

  Future<void> _handleTransactionTap(
    Transaction transaction,
    OrderController orderNotifier,
  ) async {
    await orderNotifier.setTransactionInView(transaction);
    if (mounted) {
      context.goNamed(
        kOrderSummaryRoute,
        pathParameters: {
          'id': transaction.reference ?? transaction.orderNumber ?? ''
        },
      );
    }
  }

  Widget _buildTableRow(
    Transaction transaction,
    bool isHovered,
    TextTheme textTheme,
    bool deleting,
  ) {
    final branch = _getTransactionBranch(transaction);
    final outletName = _getOutletName(transaction, branch);

    return Table(
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: _getColumnWidths(),
      children: [
        TableRow(
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Palette.stroke)),
          ),
          children: [
            Container(),
            buildContentCell(transaction.orderNumber, textTheme),
            _buildAmountCell(transaction, textTheme),
            buildContentCell(transaction.orderSummary, textTheme),
            buildContentCell(outletName, textTheme),
            _buildDateAndStatusCell(transaction, textTheme),
            _buildActionButtons(transaction, isHovered),
            _buildMoreOptionsButton(transaction),
          ],
        ),
      ],
    );
  }

  Map<int, TableColumnWidth> _getColumnWidths() {
    return const {
      0: FlexColumnWidth(0.35),
      1: FlexColumnWidth(1.3),
      2: FlexColumnWidth(1.6),
      3: FlexColumnWidth(2.5),
      4: FlexColumnWidth(1.2),
      5: FlexColumnWidth(1.6),
      6: FlexColumnWidth(2.5),
      7: FlexColumnWidth(0.5),
    };
  }

  RetailBranch? _getTransactionBranch(Transaction transaction) {
    return ref
        .read(branchesProvider)
        .firstWhereOrNull((x) => x.id == transaction.retailOutletId);
  }

  String _getOutletName(Transaction transaction, RetailBranch? branch) {
    return transaction.outletBusinessName ??
        branch?.outletBusinessName ??
        branch?.streetName ??
        '';
  }

  Widget _buildAmountCell(Transaction transaction, TextTheme textTheme) {
    return buildContentCell(
      CurrencyWidget.tableWidget(
        context,
        transaction.currency,
        transaction.orderTotal ?? transaction.amount ?? 0,
        mainAxisAlignment: MainAxisAlignment.start,
        extra: const Gap(4),
      ),
      textTheme,
    );
  }

  Widget _buildDateAndStatusCell(Transaction transaction, TextTheme textTheme) {
    return buildContentCell(
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Text(
                transaction.createdAt?.toDayMonth() ?? '',
                style: textTheme.bodyMedium?.copyWith(
                  color: Palette.blackSecondary,
                ),
              ),
              const Gap(5),
              OrderStatusBadge(transaction.status),
            ],
          ),
        ],
      ),
      textTheme,
    );
  }

  Widget _buildActionButtons(Transaction transaction, bool isHovered) {
    if (!isHovered) return Container();

    return Align(
      alignment: Alignment.centerRight,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            _buildPrintButton(transaction),
            if (transaction.isDraft) ...[
              const Gap(20),
              _buildDeleteButton(transaction),
            ],
            const Gap(20),
          ],
        ),
      ),
    );
  }

  Widget _buildMoreOptionsButton(Transaction transaction) {
    return Align(
      alignment: Alignment.centerRight,
      child: PopupMenuButton<String>(
        useRootNavigator: true,
        icon: const Icon(Icons.more_vert, color: Colors.black),
        onSelected: (value) => _handleMenuSelection(value, transaction),
        itemBuilder: (context) => _buildMenuItems(transaction),
        color: Colors.white,
        tooltip: '',
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: EdgeInsets.zero,
        menuPadding: EdgeInsets.zero,
        enableFeedback: false,
      ),
    );
  }

  void _handleMenuSelection(String value, Transaction transaction) {
    if (value == 'print') {
      _handlePrintOrder(transaction);
    } else if (value == 'delete') {
      _showDeleteConfirmation(transaction);
    } else if (value == 'copy') {
      Clipboard.setData(
          ClipboardData(text: transaction.orderNumber.toString()));
      Toast.show("Order number copied to clipboard", context,
          duration: 2, title: '');
    }
  }

  List<PopupMenuItem<String>> _buildMenuItems(Transaction transaction) {
    final items = <PopupMenuItem<String>>[
      const PopupMenuItem(
        value: 'copy',
        child: ListTile(
          leading: Icon(Icons.copy_outlined),
          title: Text(
            'Copy Order Number',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
      const PopupMenuItem(
        value: 'print',
        child: ListTile(
          leading: Icon(Icons.print_outlined),
          title: Text(
            'Print Order',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
    ];

    if (transaction.isDraft) {
      items.add(
        const PopupMenuItem(
          value: 'delete',
          child: ListTile(
            leading: Icon(Icons.delete_outline),
            title: Text(
              'Delete Order',
              style: TextStyle(color: Colors.black),
            ),
          ),
        ),
      );
    }

    return items;
  }

  Widget _buildPrintButton(Transaction transaction) {
    return SizedBox(
      width: 100,
      height: 35,
      child: CustomFilledButton(
        text: 'Print Order',
        loaderNotifier: loading,
        onPressed: () => _handlePrintOrder(transaction),
      ),
    );
  }

  Widget _buildDeleteButton(Transaction transaction) {
    return TextButton(
      onPressed: () => _showDeleteConfirmation(transaction),
      child: Text(
        'Delete',
        style: Theme.of(context).textTheme.bodyLarge,
      ),
    );
  }

  Future<void> _handlePrintOrder(Transaction transaction) async {
    loading.value = true;

    final printOrder = await processPrintOrder(transaction, ref);
    if (printOrder == null && mounted) {
      loading.value = false;
      return Toast.error('Error printing order', context, title: 'Print Error');
    }

    final branches = ref.read(branchesProvider);
    final branch = branches.firstWhere(
      (x) => x.id == transaction.retailOutletId,
      orElse: () => branches.first,
    );

    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final printer =
        OrderPrinter(printOrder!, branch, isExportCountry, 'Purchase Order');

    final pdfData = await printer.generatePdf();
    loading.value = false;
    await printer.printPdf(pdfData);
  }

  void _showDeleteConfirmation(Transaction transaction) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return DeleteConfirmationDialog(
          onDelete: () => _handleDelete(transaction),
          onClose: clearDeletingId,
        );
      },
    );
  }

  Future<void> _handleDelete(Transaction transaction) async {
    ref.read(deletingIdProvider.notifier).state = transaction.id;

    final outletId =
        ref.read(userControllerProvider)?.currentRetailOutlet?.id ?? '';
    final res = await ref
        .read(deleteOrderUseCaseProvider)
        .call(transaction.id, outletId);

    res.when(
      success: (_) {
        Toast.success(
          'Your draft order has been deleted successfully',
          context,
          title: 'Action successful!',
        );
        clearDeletingId();
        ref.read(orderControllerProvider.notifier).fetchTransactions(
              ref.read(orderControllerProvider).fetchTransactionsParam,
              forced: true,
            );
      },
      failure: (error, _) {
        clearDeletingId();
        Toast.apiError(
          error,
          context,
          title: 'Error deleting draft order',
        );
      },
    );
  }
}

Widget buildHeaderCell(String text, TextTheme textTheme) {
  return Text(
    text,
    style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
  );
}

Widget buildContentCell(dynamic content, TextTheme textTheme) {
  return SizedBox(
    height: 50,
    child: Row(
      children: [
        Flexible(
          child: content is String
              ? Text(
                  content,
                  style: textTheme.bodyMedium?.copyWith(
                    color: Palette.blackSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )
              : content,
        ),
        const Gap(12),
      ],
    ),
  );
}

Widget buildTableHeader({required Table tableHeader}) {
  return Container(
    color: Palette.kF7F7F7,
    height: 40,
    alignment: Alignment.center,
    child: tableHeader,
  );
}
