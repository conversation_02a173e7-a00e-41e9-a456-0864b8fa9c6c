import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/extensions/strings.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/formatters.dart';

class CartQuantityInputField extends StatefulWidget {
  const CartQuantityInputField({
    super.key,
    required this.initialCount,
    required this.maxValue,
    this.onQuantityChanged,
    this.focusBorder = true,
    this.cursorHeight,
    this.readOnly = false,
    this.autoFocus = false,
    this.allowDecimal = true,
    this.restrictToAllowedDecimals = true,
    this.focusNode,
  });

  final num initialCount;
  final num maxValue;
  final ValueChanged<dynamic>? onQuantityChanged;
  final bool focusBorder;
  final double? cursorHeight;
  final bool readOnly;
  final bool autoFocus;
  final bool allowDecimal;
  final bool restrictToAllowedDecimals;
  final FocusNode? focusNode;

  @override
  CartQuantityInputFieldState createState() => CartQuantityInputFieldState();
}

class CartQuantityInputFieldState extends State<CartQuantityInputField> {
  late final TextEditingController _controller;
  double _fontSize = 16;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.initialCount.toStringAsFixed(2).removeTrailingZerosAndDot(),
    );
  }

  @override
  void didUpdateWidget(covariant CartQuantityInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    final newText =
        widget.initialCount.toStringAsFixed(2).removeTrailingZerosAndDot();

    // Update the controller's text only if:
    // 1. The new text is different.
    // 2. The field is not focused (so we don't disrupt the user's editing).
    if (oldWidget.initialCount != widget.initialCount &&
        _controller.text != newText &&
        !(widget.focusNode?.hasFocus ?? false)) {
      _controller.value = _controller.value.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
        composing: TextRange.empty,
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _updateFontSize(String text) {
    var textLength = text.length;
    if (textLength > 6) {
      _fontSize = 12;
    } else if (textLength > 4) {
      _fontSize = 14;
    } else {
      _fontSize = 16;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      ignoring: widget.readOnly,
      child: TextField(
        focusNode: widget.focusNode,
        readOnly: widget.readOnly,
        controller: _controller,
        autofocus: widget.autoFocus,
        keyboardType: TextInputType.number,
        inputFormatters: _getInputFormatters(),
        onChanged: (value) {
          final count = _parseValue(value);
          widget.onQuantityChanged?.call(count);
          _updateFontSize(value);
        },
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: _fontSize,
              overflow: TextOverflow.ellipsis,
            ),
        decoration: InputDecoration(
          border: const OutlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent),
            borderRadius: BorderRadius.zero,
          ),
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent),
            borderRadius: BorderRadius.zero,
          ),
          filled: true,
          hoverColor: Colors.transparent,
          fillColor: Colors.white,
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: widget.focusBorder ? Palette.primary : Colors.transparent,
            ),
            borderRadius: BorderRadius.zero,
          ),
          contentPadding:
              const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
        ),
        cursorHeight: widget.cursorHeight,
      ),
    );
  }

  List<TextInputFormatter> _getInputFormatters() {
    if (widget.allowDecimal) {
      return [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        SingleLeadingZeroFormatter(),
        DecimalTextInputFormatter(
          decimalRange: 2,
          allowedDecimals:
              widget.restrictToAllowedDecimals ? allowedDecimals : [],
        ),
        MaxNumValueTextInputFormatter(
          maxValue: widget.maxValue,
          onMaxValueExceeded: () {
            Toast.error(
              'Only ${widget.maxValue} available in stock',
              context,
              title: 'Available quantity exceeded',
              duration: 3,
            );
          },
        ),
      ];
    } else {
      return [
        FilteringTextInputFormatter.digitsOnly,
        NoLeadingZeroFormatter(),
        MaxIntValueTextInputFormatter(
          maxValue: widget.maxValue.toInt(),
          onMaxValueExceeded: () {
            Toast.error(
              'Only ${widget.maxValue} available in stock',
              context,
              title: 'Available quantity exceeded',
              duration: 3,
            );
          },
        ),
      ];
    }
  }

  dynamic _parseValue(String value) {
    if (widget.allowDecimal) {
      return (value.endsWith('.') ||
              (value.contains('.') && value.split('.')[1].isEmpty))
          ? value
          : num.tryParse(value);
    } else {
      return num.tryParse(value) ?? 0;
    }
  }
}

// class CartQuantityInputField extends StatefulWidget {
//   const CartQuantityInputField({
//     super.key,
//     required this.initialCount,
//     required this.maxValue,
//     this.onQuantityChanged,
//     this.focusBorder = true,
//     this.cursorHeight,
//     this.readOnly = false,
//     this.autoFocus = false,
//     this.allowDecimal = true,
//     this.restrictToAllowedDecimals = true,
//     this.focusNode,
//   });

//   final num initialCount;
//   final num maxValue;
//   final ValueChanged<dynamic>? onQuantityChanged;
//   final bool focusBorder;
//   final double? cursorHeight;
//   final bool readOnly;
//   final bool autoFocus;
//   final bool allowDecimal;
//   final bool restrictToAllowedDecimals;
//   final FocusNode? focusNode;

//   @override
//   CartQuantityInputFieldState createState() => CartQuantityInputFieldState();
// }

// class CartQuantityInputFieldState extends State<CartQuantityInputField> {
//   late final TextEditingController _controller;
//   double _fontSize = 16;

//   @override
//   void initState() {
//     super.initState();
//     _controller = TextEditingController(
//         text:
//             widget.initialCount.toStringAsFixed(2).removeTrailingZerosAndDot());
//   }

//   @override
//   void didUpdateWidget(covariant CartQuantityInputField oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     final newText =
//         widget.initialCount.toStringAsFixed(2).removeTrailingZerosAndDot();

//     // Update the controller's text only if:
//     // 1. The new text is different.
//     // 2. The field is not focused (so we don't disrupt the user's editing).
//     if (oldWidget.initialCount != widget.initialCount &&
//         _controller.text != newText &&
//         !(widget.focusNode?.hasFocus ?? false)) {
//       _controller.value = _controller.value.copyWith(
//         text: newText,
//         selection: TextSelection.collapsed(offset: newText.length),
//         composing: TextRange.empty,
//       );
//     }
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   void _updateFontSize(String text) {
//     var textLength = text.length;
//     if (textLength > 4) {
//       _fontSize = 14;
//     } else if (textLength > 6) {
//       _fontSize = 12;
//     } else {
//       _fontSize = 16;
//     }
//     setState(() {});
//   }

//   @override
//   Widget build(BuildContext context) {
//     return IgnorePointer(
//       ignoring: widget.readOnly,
//       child: TextField(
//         focusNode: widget.focusNode,
//         readOnly: widget.readOnly,
//         controller: _controller,
//         // controller: TextEditingController.fromValue(
//         //   TextEditingValue(
//         //     text: widget.initialCount.toString(),
//         //     selection: TextSelection.collapsed(
//         //       offset: widget.initialCount.toString().length,
//         //     ),
//         //   ),
//         // ),
//         autofocus: widget.autoFocus,
//         keyboardType: TextInputType.number,
//         inputFormatters: [
//           // RemoveDecimalTrailingZeroFormatter(),
//           if (widget.allowDecimal) ...[
//             FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
//             SingleLeadingZeroFormatter(),
//             DecimalTextInputFormatter(
//                 decimalRange: 2,
//                 allowedDecimals:
//                     widget.restrictToAllowedDecimals ? allowedDecimals : []),
//             MaxNumValueTextInputFormatter(
//               maxValue: widget.maxValue,
//               onMaxValueExceeded: () {
//                 Toast.error(
//                   'Only ${widget.maxValue} available in stock',
//                   context,
//                   title: 'Available quantity exceeded',
//                   duration: 3,
//                 );
//               },
//             )
//           ] else ...[
//             FilteringTextInputFormatter.digitsOnly,
//             NoLeadingZeroFormatter(),
//             MaxIntValueTextInputFormatter(
//               maxValue: widget.maxValue.toInt(),
//               onMaxValueExceeded: () {
//                 Toast.error(
//                   'Only ${widget.maxValue} available in stock',
//                   context,
//                   title: 'Available quantity exceeded',
//                   duration: 3,
//                 );
//               },
//             ),
//           ],
//         ],
//         onChanged: (value) {
//           if (widget.allowDecimal) {
//             dynamic valueCount = (value.endsWith('.') ||
//                     (value.contains('.') && value.split('.')[1].isEmpty))
//                 ? value
//                 : num.tryParse(value);
//             final count = valueCount ?? 0;
//             widget.onQuantityChanged?.call(count);
//           } else {
//             final valueCount = num.tryParse(value);
//             final count = valueCount ?? 0;
//             widget.onQuantityChanged?.call(count);
//           }

//           _updateFontSize(value);
//         },
//         textAlign: TextAlign.center,
//         textDirection: TextDirection.ltr,
//         style: Theme.of(context).textTheme.bodyLarge?.copyWith(
//               fontWeight: FontWeight.w600,
//               fontSize: _fontSize,
//               overflow: TextOverflow.ellipsis,
//             ),
//         decoration: InputDecoration(
//           // enabledBorder: InputBorder.none,
//           border: const OutlineInputBorder(
//             borderSide: BorderSide(color: Colors.transparent),
//             borderRadius: BorderRadius.zero,
//           ),
//           enabledBorder: const OutlineInputBorder(
//             borderSide: BorderSide(color: Colors.transparent),
//             borderRadius: BorderRadius.zero,
//           ),
//           filled: true,
//           hoverColor: Colors.transparent,
//           fillColor: Colors.white,
//           focusedBorder: OutlineInputBorder(
//             borderSide: BorderSide(
//               color: widget.focusBorder ? Palette.primary : Colors.transparent,
//             ),
//             borderRadius: BorderRadius.zero,
//           ),
//           contentPadding:
//               const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
//         ),
//         cursorHeight: widget.cursorHeight,
//       ),
//     );
//   }
// }

/// ============================================================================
/// ============================================================================
///                                    END
/// ============================================================================
/// ============================================================================

// class CartQuantityInputField extends StatefulWidget {
//   const CartQuantityInputField({
//     super.key,
//     required this.initialCount,
//     required this.maxValue,
//     this.onQuantityChanged,
//     this.focusBorder = true,
//     this.contentPadding,
//     this.cursorHeight,
//     this.readOnly = false,
//     this.autoFocus = false,
//   });

//   final num initialCount;
//   final num maxValue;
//   final Function(num)? onQuantityChanged;
//   final bool focusBorder;
//   final EdgeInsetsGeometry? contentPadding;
//   final double? cursorHeight;
//   final bool readOnly;
//   final bool autoFocus;

//   @override
//   CartQuantityInputFieldState createState() => CartQuantityInputFieldState();
// }

// class CartQuantityInputFieldState extends State<CartQuantityInputField> {
//   // late TextEditingController _controller;
//   double _fontSize = 16; // Initial font size
//   final double _minFontSize = 12; // Minimum font size
//   final double _maxFontSize = 16; // Maximum font size

//   void _updateFontSize(String text) {
//     var textWidth = _getTextWidth(text, _fontSize);

//     // Set the width of the TextField (you can adjust this value)
//     const maxWidth = 150; // Available width for the TextField

//     if (textWidth > maxWidth) {
//       // Reduce font size until it fits within the available width
//       while (textWidth > maxWidth && _fontSize > _minFontSize) {
//         _fontSize -= 1;
//         textWidth = _getTextWidth(text, _fontSize);
//       }
//     } else {
//       // Reset to maximum font size if it fits
//       _fontSize = _maxFontSize;
//     }

//     setState(() {});
//   }

//   double _getTextWidth(String text, double fontSize) {
//     final textPainter = TextPainter(
//       text: TextSpan(
//         text: text,
//         style: TextStyle(fontSize: fontSize),
//       ),
//       maxLines: 1,
//       textDirection: TextDirection.ltr,
//     );

//     textPainter.layout();
//     return textPainter.width;
//   }

//   @override
//   Widget build(BuildContext context) {
//     // _controller.addListener(_updateFontSize);

//     return IgnorePointer(
//       ignoring: widget.readOnly,
//       child: TextField(
//         readOnly: widget.readOnly,
//         controller: TextEditingController.fromValue(
//           TextEditingValue(
//             text: widget.initialCount.toString(),
//             selection: TextSelection.collapsed(
//               offset: widget.initialCount.toString().length,
//             ),
//           ),
//         ),
//         autofocus: widget.autoFocus,
//         keyboardType: TextInputType.number,
//         inputFormatters: [
//           FilteringTextInputFormatter.digitsOnly,
//           NoLeadingZeroFormatter(),
//           MaxValueTextInputFormatter(
//             maxValue: widget.maxValue.toInt(),
//             onMaxValueExceeded: () {
//               ToastBox.showError(
//                   context, 'Only ${widget.maxValue} available in stock',
//                   title: 'Available quantity exceeded', duration: 3);
//             },
//           ),
//         ],
//         onChanged: (value) {
//           final valueCount = num.tryParse(value);
//           final count = valueCount ?? 0;

//           if (count > 0) {
//             widget.onQuantityChanged!(count);

//             _updateFontSize(value);
//           }
//         },
//         textAlign: TextAlign.center,
//         textDirection: TextDirection.ltr,
//         style: Theme.of(context)
//             .textTheme
//             .bodyLarge
//             ?.copyWith(fontWeight: FontWeight.w600, fontSize: _fontSize),
//         decoration: InputDecoration(
//           enabledBorder: InputBorder.none,
//           border: OutlineInputBorder(
//             borderSide: BorderSide(color: Palette.stroke),
//             borderRadius: BorderRadius.circular(6),
//           ),
//           focusedBorder: widget.focusBorder
//               ? OutlineInputBorder(
//                   borderSide: BorderSide(color: Palette.primary),
//                   borderRadius: BorderRadius.circular(6),
//                 )
//               : InputBorder.none,
//           contentPadding: widget.contentPadding,
//         ),
//         cursorHeight: widget.cursorHeight,
//       ),
//     );
//   }
// }
