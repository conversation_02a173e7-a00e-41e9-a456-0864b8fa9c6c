import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/widgets/catalog_table.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class CatalogViewSwitcher extends ConsumerWidget {
  // final ValueChanged<CatalogViewType> onViewChanged;
  final CatalogViewType initialCatalogView;
  final ValueNotifier<CatalogViewType> selectedViewNotifier;

  CatalogViewSwitcher({
    super.key,
    // required this.onViewChanged,
    this.initialCatalogView = CatalogViewType.grid,
  }) : selectedViewNotifier =
            ValueNotifier<CatalogViewType>(initialCatalogView);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: CatalogViewType.values.map((type) {
        return _buildShuffleButton(type, () {
          selectedViewNotifier.value = type;
          ref.read(catalogViewProvider.notifier).state = type;
        });
      }).toList(),
    );
  }

  Widget _buildShuffleButton(CatalogViewType type, VoidCallback onTap) {
    final isGrid = type == CatalogViewType.grid;
    final borderRadius = BorderRadius.horizontal(
      left: isGrid ? const Radius.circular(8) : Radius.zero,
      right: isGrid ? Radius.zero : const Radius.circular(8),
    );

    return InkWell(
      borderRadius: borderRadius,
      onTap: onTap,
      child: ValueListenableBuilder<CatalogViewType>(
        valueListenable: selectedViewNotifier,
        builder: (context, selectedType, _) {
          final isSelected = selectedType == type;
          return Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected ? Palette.primaryBlack : Palette.stroke,
                width: 2,
              ),
              borderRadius: borderRadius,
            ),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(6.0),
                child: SvgPicture.asset(
                  '$kSvgDir/order/${type.name}.svg',
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
