import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_state.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/date_picker.dart';
import 'package:td_procurement/core/models/sized_item.dart';
import 'package:td_procurement/src/components/toast/custom_toast.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class DateAndBranchPickerWidget extends ConsumerStatefulWidget {
  const DateAndBranchPickerWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      DateAndBranchPickerWidgetState();
}

class DateAndBranchPickerWidgetState
    extends ConsumerState<DateAndBranchPickerWidget> {
  List<RetailBranch> _branches = [];
  List<RetailBranch> _selectedBranches = [];

  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    _branches = ref.read(branchesProvider);
    _selectedBranches = ref.read(orderControllerProvider).selectedBranches;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;
    const double width = 155;

    // reset the local _selectedBranches when the global value changes
    ref.listen<OrderState>(orderControllerProvider, (previous, next) {
      if (previous?.selectedBranches != next.selectedBranches) {
        setState(() {
          _selectedBranches = next.selectedBranches;
        });
      }
    });

    final orderState = ref.watch(orderControllerProvider);

    return Padding(
      padding: const EdgeInsets.only(right: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // InkWell(
          //   onTap: () => _showDatePickerOverlay(context),
          //   child: Container(
          //     width: width,
          //     height: kOrderOptionsHeight,
          //     padding: const EdgeInsets.symmetric(horizontal: 12),
          //     decoration: BoxDecoration(
          //       border: Border.all(color: Palette.stroke),
          //       borderRadius: BorderRadius.circular(8),
          //     ),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //       children: [
          //         Expanded(
          //           child: Align(
          //             alignment: Alignment.centerLeft,
          //             child: FittedBox(
          //               fit: BoxFit.scaleDown,
          //               child: Text(
          //                 getFormattedDateRange(orderState.selectedStartDate,
          //                     orderState.selectedEndDate),
          //                 style: textTheme.bodyMedium,
          //               ),
          //             ),
          //           ),
          //         ),
          //         SvgPicture.asset('$kSvgDir/order/calender.svg'),
          //       ],
          //     ),
          //   ),
          // ),
          DatePickerWidget(
            initialDate: orderState.selectedStartDate,
            selectedStartDate: orderState.selectedStartDate,
            selectedEndDate: orderState.selectedEndDate,
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(color: Palette.stroke),
                top: BorderSide(color: Palette.stroke),
                bottom: BorderSide(color: Palette.stroke),
                right: BorderSide(color: Palette.stroke, width: 0.5),
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                bottomLeft: Radius.circular(8),
              ),
            ),
            getValueLabel: () {
              return getFormattedDateRange(
                  orderState.selectedStartDate, orderState.selectedEndDate);
            },
            onDatesSelected: ({startDate, endDate}) async {
              final params =
                  ref.read(orderControllerProvider).fetchTransactionsParam;

              final orderNotifier = ref.read(orderControllerProvider.notifier);

              orderNotifier.setDateFilter(
                  startDate ??= endDate, endDate ??= startDate);

              await Future.delayed(Duration.zero);

              orderNotifier.fetchTransactions(
                  params.copyWith(
                      selectedDates: [startDate, endDate], currentPage: 1),
                  forced: true);
            },
            onCancel: () async {
              final stateParams =
                  ref.read(orderControllerProvider).fetchTransactionsParam;

              if (stateParams.selectedDates.any((date) => date != null)) {
                final params = FetchTransactionsParam.defaultValue().copyWith(
                    orderStatus: stateParams.orderStatus,
                    selectedOutlets: stateParams.selectedOutlets,
                    searchText: stateParams.searchText,
                    activeIndex: stateParams.activeIndex);

                final orderNotifier =
                    ref.read(orderControllerProvider.notifier);
                orderNotifier.setDateFilter(null, null);

                await Future.delayed(Duration.zero);

                orderNotifier.fetchTransactions(params, forced: true);
              }
            },
          ),
          InkWell(
            onTap: () => _branches.isNotEmpty
                ? _showBranchPickerOverlay(context)
                : CustomToast.show(context, message: 'No branches to filter'),
            child: CompositedTransformTarget(
              link: orderState.layerBranchLink,
              child: Container(
                width: width,
                height: kOrderOptionsHeight,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  border: Border(
                    left: BorderSide(color: Palette.stroke, width: 0.5),
                    top: BorderSide(color: Palette.stroke),
                    bottom: BorderSide(color: Palette.stroke),
                    right: BorderSide(color: Palette.stroke),
                  ),
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            orderState.selectedBranches.isEmpty
                                ? 'All branches'
                                : '${orderState.selectedBranches.length} selected',
                          ),
                        ),
                      ),
                    ),
                    SvgPicture.asset(
                      '$kSvgDir/order/chevron_down.svg',
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Gap(10.w),
        ],
      ),
    );
  }

  String getFormattedDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate == null && endDate == null) {
      return 'Filter By Date';
    }

    startDate ??= endDate;
    endDate ??= startDate;

    final startFormat =
        (startDate!.year == endDate!.year) ? 'MMM d' : 'MMM d, yyyy';
    const endFormat = 'MMM d, yyyy';

    final formattedStart = DateFormat(startFormat).format(startDate);
    final formattedEnd = DateFormat(endFormat).format(endDate);

    return '$formattedStart - $formattedEnd';
  }

  Widget _buildBranchPicker() {
    final textTheme = Theme.of(context).textTheme;

    return Consumer(
      builder: (context, ref, _) {
        final orderState = ref.watch(orderControllerProvider);
        // final snapShot = [...orderState.selectedBranches];

        return Column(
          children: [
            Expanded(
              child: StatefulBuilder(builder: (context, setState) {
                return ListView.builder(
                  itemCount: _branches.length,
                  itemBuilder: (context, index) {
                    final branch = _branches[index];
                    final selected = _selectedBranches.contains(branch);
                    return HoverableContainer(
                      index: index,
                      child: Container(
                        padding: const EdgeInsets.only(left: 10),
                        decoration: BoxDecoration(
                            border: Border(
                                bottom: BorderSide(color: Palette.stroke))),
                        child: ListTile(
                          leading: SvgPicture.asset(
                            '$kSvgDir/order/check_circle.svg',
                            colorFilter: selected
                                ? ColorFilter.mode(
                                    Palette.primary,
                                    BlendMode.srcIn,
                                  )
                                : null,
                          ),
                          title: Text(
                            branch.outletBusinessName,
                            style: textTheme.bodyLarge,
                          ),
                          subtitle: Text(
                            branch.streetName ?? '',
                            style: textTheme.bodyMedium?.copyWith(
                              color: Palette.blackSecondary,
                            ),
                          ),
                          onTap: () {
                            final updatedBranches = selected
                                ? _selectedBranches
                                    .where((x) => x != branch)
                                    .toList()
                                : [..._selectedBranches, branch];

                            setState(() {
                              _selectedBranches = updatedBranches;
                            });
                          },
                        ),
                      ),
                    );
                  },
                );
              }),
            ),
            const Gap(14),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: () {
                    ref
                        .read(orderControllerProvider.notifier)
                        .setSelectedBranches([]);
                    if (orderState.selectedBranches.isNotEmpty) {
                      _processBranchAction();
                    }
                    _closeOverlay();
                  },
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    foregroundColor: Palette.primary,
                    backgroundColor: Palette.primary.withOpacity(0.2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Cancel'),
                ),
                const Gap(20),
                ElevatedButton(
                  onPressed: () {
                    // if (_selectedBranches.isNotEmpty) {
                    ref
                        .read(orderControllerProvider.notifier)
                        .setSelectedBranches(_selectedBranches);
                    _processBranchAction();
                    // }
                    _closeOverlay();
                  },
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    foregroundColor: Colors.white,
                    backgroundColor: Palette.primaryBlack,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Save'),
                ),
                const Gap(20),
              ],
            ),
            const Gap(14),
          ],
        );
      },
    );
  }

  // Widget _buildDatePicker() {
  //   return Padding(
  //     padding: const EdgeInsets.all(16.0),
  //     child: MultiDatePicker(
  //       onSelectRange: (startDate, endDate) async {
  //         final params =
  //             ref.read(orderControllerProvider).fetchTransactionsParam;

  //         final orderNotifier = ref.read(orderControllerProvider.notifier);

  //         orderNotifier.setDateFilter(
  //             startDate ??= endDate, endDate ??= startDate);

  //         await Future.delayed(Duration.zero);

  //         orderNotifier.fetchTransactions(
  //             params.copyWith(selectedDates: [startDate, endDate]),
  //             forced: true);

  //         _closeOverlay();
  //       },
  //       onCancel: () async {
  //         _closeOverlay();

  //         final stateParams =
  //             ref.read(orderControllerProvider).fetchTransactionsParam;

  //         if (stateParams.selectedDates.any((date) => date != null)) {
  //           final params = FetchTransactionsParam.defaultValue().copyWith(
  //               orderStatus: stateParams.orderStatus,
  //               selectedOutlets: stateParams.selectedOutlets,
  //               searchText: stateParams.searchText,
  //               activeIndex: stateParams.activeIndex);

  //           final orderNotifier = ref.read(orderControllerProvider.notifier);
  //           orderNotifier.setDateFilter(null, null);

  //           await Future.delayed(Duration.zero);

  //           orderNotifier.fetchTransactions(params, forced: true);
  //         }
  //       },
  //     ),
  //   );
  // }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  OverlayEntry _createOverlayEntry(
    BuildContext context,
    SizedItem size,
    Widget child,
  ) {
    return OverlayEntry(
      builder: (context) {
        final orderState = ref.watch(orderControllerProvider);

        return Stack(
          children: [
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  _closeOverlay();
                },
              ),
            ),
            Positioned(
              right: MediaQuery.of(context).size.width - (size.width + 35),
              top:
                  MediaQuery.of(context).size.height * (size.top + size.height),
              width: size.width,
              height: size.height + 25,
              child: CompositedTransformFollower(
                link: orderState.layerBranchLink,
                offset: Offset(-size.width + 155, 40),
                showWhenUnlinked: false,
                child: Material(
                  elevation: 8.0,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Palette.stroke),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: child,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _processBranchAction() {
    final params = ref.read(orderControllerProvider).fetchTransactionsParam;
    ref.read(orderControllerProvider.notifier).fetchTransactions(
        params.copyWith(
            selectedOutlets: _selectedBranches.map((el) => el.id).toList()),
        forced: true);
  }

  // Show overlay for branches
  void _showBranchPickerOverlay(BuildContext context) {
    _overlayEntry = _createOverlayEntry(
      context,
      SizedItem(width: 580, height: 269, top: 0.15, right: 0.012),
      _buildBranchPicker(),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  // Show overlay for date picker
  // void _showDatePickerOverlay(BuildContext context) {
  //   _overlayEntry = _createOverlayEntry(
  //     context,
  //     SizedItem(width: 620, height: 400, top: 0.15, right: 0.012), // w580, h383
  //     _buildDatePicker(),
  //   );
  //   Overlay.of(context).insert(_overlayEntry!);
  // }
}



// class _MultiDatePickerOverlay extends ConsumerStatefulWidget {
//   final void Function(DateTime?, DateTime?) onSelectRange;
//   final VoidCallback onCancel;

//   const _MultiDatePickerOverlay({
//     required this.onSelectRange,
//     required this.onCancel,
//   });

//   @override
//   ConsumerState<ConsumerStatefulWidget> createState() =>
//       _MultiDatePickerOverlayState();
// }

// class _MultiDatePickerOverlayState
//     extends ConsumerState<_MultiDatePickerOverlay> {
//   DateTime _focusedLeftMonth = DateTime.now();
//   DateTime _focusedRightMonth = DateTime.now().add(const Duration(days: 30));
//   DateTime? _selectedStartDate;
//   DateTime? _selectedEndDate;

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Flexible(
//               child: _buildCalendar(_focusedLeftMonth, isLeftCalendar: true),
//             ),
//             const Gap(16),
//             Flexible(
//               child: _buildCalendar(_focusedRightMonth, isLeftCalendar: false),
//             ),
//           ],
//         ),
//         const Spacer(),
//         Container(
//           padding: const EdgeInsets.only(top: 15),
//           decoration: BoxDecoration(
//               border: Border(top: BorderSide(color: Palette.stroke))),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             children: [
//               ElevatedButton(
//                 onPressed: widget.onCancel,
//                 style: ElevatedButton.styleFrom(
//                   elevation: 0,
//                   foregroundColor: Palette.primary,
//                   backgroundColor: Palette.primary.withOpacity(0.2),
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                 ),
//                 child: const Text('Cancel'),
//               ),
//               const Gap(20),
//               ElevatedButton(
//                 onPressed: () {
//                   widget.onSelectRange(_selectedStartDate, _selectedEndDate);
//                 },
//                 style: ElevatedButton.styleFrom(
//                   elevation: 0,
//                   foregroundColor: Colors.white,
//                   backgroundColor: Palette.primaryBlack,
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                 ),
//                 child: const Text('Save'),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   void _moveCalendars(bool moveBackwards) {
//     setState(() {
//       if (moveBackwards) {
//         _focusedLeftMonth =
//             DateTime(_focusedLeftMonth.year, _focusedLeftMonth.month - 1, 1);
//         _focusedRightMonth =
//             DateTime(_focusedRightMonth.year, _focusedRightMonth.month - 1, 1);
//       } else {
//         _focusedLeftMonth =
//             DateTime(_focusedLeftMonth.year, _focusedLeftMonth.month + 1, 1);
//         _focusedRightMonth =
//             DateTime(_focusedRightMonth.year, _focusedRightMonth.month + 1, 1);
//       }
//     });
//   }

//   Widget _buildCalendar(DateTime focusedMonth, {required bool isLeftCalendar}) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         // Header Row with Navigation Icons and Month/Year Display
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             if (isLeftCalendar)
//               IconButton(
//                 icon: const Icon(Icons.arrow_back),
//                 onPressed: () => _moveCalendars(true),
//               )
//             else
//               const Gap(48),
//             Text(
//               '${focusedMonth.toMonthName()} ${focusedMonth.year}',
//               style: const TextStyle(fontWeight: FontWeight.bold),
//             ),
//             if (!isLeftCalendar)
//               IconButton(
//                 icon: const Icon(Icons.arrow_forward),
//                 onPressed: () => _moveCalendars(false),
//               )
//             else
//               const Gap(48),
//           ],
//         ),
//         const Gap(10),
//         TableCalendar(
//           firstDay: DateTime.now().subtract(const Duration(days: 365 * 5)),
//           lastDay: DateTime.now().add(const Duration(days: 365)),
//           focusedDay: focusedMonth,
//           rowHeight: 45.0,
//           daysOfWeekHeight: 16.0,
//           selectedDayPredicate: (day) {
//             if (_selectedStartDate != null && _selectedEndDate != null) {
//               return day.isAfter(_selectedStartDate!) &&
//                   day.isBefore(_selectedEndDate!);
//             }
//             return false;
//           },
//           rangeStartDay: _selectedStartDate,
//           rangeEndDay: _selectedEndDate,
//           onDaySelected: (selectedDay, _) {
//             setState(() {
//               if (_selectedStartDate == null || _selectedEndDate != null) {
//                 _selectedStartDate = selectedDay;
//                 _selectedEndDate = null;
//               } else {
//                 _selectedEndDate = selectedDay.isAfter(_selectedStartDate!)
//                     ? selectedDay
//                     : _selectedStartDate;
//                 _selectedStartDate = selectedDay.isAfter(_selectedStartDate!)
//                     ? _selectedStartDate
//                     : selectedDay;
//               }
//             });
//           },
//           calendarFormat: CalendarFormat.month,
//           startingDayOfWeek: StartingDayOfWeek.monday,
//           headerVisible: false,
//           calendarStyle: CalendarStyle(
//             cellMargin: const EdgeInsets.all(1), // Compact dates
//             rangeHighlightColor: Colors.blue.withOpacity(0.5),
//             rangeStartDecoration: const BoxDecoration(
//               color: Colors.blue,
//               shape: BoxShape.circle,
//             ),
//             rangeEndDecoration: const BoxDecoration(
//               color: Colors.blue,
//               shape: BoxShape.circle,
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }