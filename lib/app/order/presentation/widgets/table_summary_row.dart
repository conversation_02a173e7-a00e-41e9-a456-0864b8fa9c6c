import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class TableSummaryRow extends StatelessWidget {
  final String title;
  final String content;
  final bool? showBorder;
  final bool? useBoldFontWeight;
  final HexColor? color;
  final int? flex;
  const TableSummaryRow(
    this.title,
    this.content, {
    super.key,
    this.showBorder = true,
    this.useBoldFontWeight = false,
    this.color,
    this.flex,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final style = textTheme.bodySmall!.copyWith(
      color: useBoldFontWeight!
          ? Palette.primaryBlack
          : color ?? Palette.blackSecondary,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: flex ?? 2,
            child: Container(),
          ),
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                    border: Border(
                        bottom: showBorder!
                            ? BorderSide(color: Palette.kE7E7E7)
                            : BorderSide.none),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          title,
                          style: useBoldFontWeight!
                              ? textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w700)
                              : style,
                        ),
                      ),
                      Flexible(
                        child: Text(
                          content,
                          style: useBoldFontWeight!
                              ? textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w700)
                              : style,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
