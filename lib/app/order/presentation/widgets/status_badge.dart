import 'package:flutter/material.dart';
import 'package:td_commons_flutter/utils/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class OrderStatusBadge extends StatelessWidget {
  const OrderStatusBadge(this.orderStatus, {super.key, this.swap = true});

  final String? orderStatus;
  final bool swap;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    String status;
    Color color;

    final orderStatusValue = orderStatus ?? '';

    switch (orderStatusValue.toLowerCase()) {
      case 'pending':
        status = orderStatusValue;
        color = HexColor('#FF8C06');
        if (swap) {
          status = 'Draft';
          color = HexColor('#6B797C');
        }
        break;
      case 'open':
        status = orderStatusValue;
        color = HexColor('#6B797C');
        if (swap) {
          status = 'Pending';
        }
        color = HexColor('#FF8C06');
        break;
      case 'cancelled':
      case 'canceled':
        status = 'Cancelled';
        color = HexColor('#FF4206');
        break;
      case 'processing':
        status = 'Processing';
        color = HexColor('#FF8C06');
        break;
      case 'dispatched':
        status = 'Dispatched';
        color = HexColor('#0610FF');
        break;
      case 'delivered':
        status = 'Delivered';
        color = HexColor('#08AA49');
        break;
      case 'paid':
        status = 'Paid';
        color = HexColor('#08AA49');
        break;
      case 'scheduled':
        status = 'Scheduled';
        color = HexColor('#315A99');
        break;
      default:
        status = 'Draft';
        color = HexColor('#6B797C');
        break;
    }

    return Row(
      children: [
        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: color.withOpacity(0.12),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            capitalize(status),
            style: textTheme.bodySmall
                ?.copyWith(color: color, fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }
}

class SalesOrderStatusBadge extends StatelessWidget {
  const SalesOrderStatusBadge(this.orderStatus, {super.key});

  final String? orderStatus;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    String status;
    Color color;

    switch ((orderStatus ?? '').toLowerCase()) {
      case 'pending':
      case 'open':
        status = 'Pending';
        color = HexColor('#FF8C06');
        break;
      case 'scheduled':
        status = 'Scheduled';
        color = HexColor('#315A99');
        break;
      case 'processing':
        status = 'Processing';
        color = HexColor('#117BFF');
        break;
      case 'dispatched':
        status = 'Dispatched';
        color = HexColor('#0610FF');
        break;
      case 'skipped':
        status = 'Skipped';
        color = HexColor('#FF0607');
        break;
      case 'delivered':
        status = 'Delivered';
        color = HexColor('#08AA49');
        break;
      case 'accepted':
        status = 'Accepted';
        color = HexColor('#117BFF');
        break;
      case 'cancelled':
      case 'canceled':
        status = 'Cancelled';
        color = HexColor('#FF4206');
        break;
      case 'shipped':
        status = 'Shipped';
        color = HexColor('#08AA49');
        break;
      case 'paid':
        status = 'Paid';
        color = HexColor('#08AA49');
        break;
      default:
        status = '-';
        color = HexColor('#6B797C');
        break;
    }

    return Row(
      children: [
        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: color.withOpacity(0.12),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            capitalize(status),
            style: textTheme.bodySmall
                ?.copyWith(color: color, fontWeight: FontWeight.w500),
          ),
        ),
      ],
    );
  }
}
