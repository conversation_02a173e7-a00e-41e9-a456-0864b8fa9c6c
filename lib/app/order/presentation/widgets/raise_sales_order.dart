import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';

import 'index.dart';

class RaiseSalesOrderWidget extends ConsumerWidget {
  const RaiseSalesOrderWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 120),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Gap(80),
          OutletSearchAutoCompleteWidget(),
          Gap(72),
          ProductSearchAutoCompleteWidget(OrderType.sales),
          Gap(40),
        ],
      ),
    );
  }
}
