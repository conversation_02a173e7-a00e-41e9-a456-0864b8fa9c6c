import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/src/components/widgets/status_filtering.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class CategorySelectionBar extends StatelessWidget {
  final List<String> categoryOptions;
  final int activeIndex;
  final Function(int) onIndexChanged;
  final Function(String) onCategoryChanged;
  final bool loading;

  const CategorySelectionBar({
    required this.categoryOptions,
    required this.activeIndex,
    required this.onIndexChanged,
    required this.onCategoryChanged,
    this.loading = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Palette.stroke),
      ),
      child: Skeletonizer(
        enabled: loading,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          scrollDirection: Axis.horizontal,
          child: Row(
            children: categoryOptions.map((status) {
              int index = categoryOptions.indexOf(status);
              bool isSelected = activeIndex == index;

              final child = StatusFilteringWidget(
                status,
                isSelected,
                inactiveItemsColor: Palette.primary,
                onPressed: () {
                  onIndexChanged(index);
                  onCategoryChanged(status);
                },
              );

              return index == 0 ? Row(children: [const Gap(40), child]) : child;
            }).toList(),
          ),
        ),
      ),
    );
  }
}
