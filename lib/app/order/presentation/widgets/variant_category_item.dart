import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/presentation/widgets/products_widget.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_card_item.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_grid_item.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class VariantCategoryItem extends ConsumerStatefulWidget {
  final String category;
  final List<Variant> variants;
  final bool loading;
  final bool isLastIndex;

  const VariantCategoryItem(
    this.category,
    this.variants,
    this.loading, {
    super.key,
    this.isLastIndex = false,
  });

  @override
  ConsumerState<VariantCategoryItem> createState() =>
      _VariantCategoryItemState();
}

class _VariantCategoryItemState extends ConsumerState<VariantCategoryItem> {
  final _scrollController = ScrollController();
  bool _showLeftChevron = false;
  bool _showRightChevron = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) => _onScroll());
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Check if the scroll position is at the start or end of the list.
    final position = _scrollController.position;
    final atStart = _scrollController.offset <= position.minScrollExtent;
    final atEnd = _scrollController.offset >= position.maxScrollExtent;

    setState(() {
      _showLeftChevron = !atStart;
      _showRightChevron = !atEnd;
    });
  }

  void _scrollForward() {
    final targetOffset = _scrollController.offset + 500;
    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.ease,
    );
  }

  void _scrollBackward() {
    final targetOffset = _scrollController.offset - 500;
    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.ease,
    );
  }

  @override
  Widget build(BuildContext context) {
    final countryType = ref.watch(countryTypeProvider);
    final listViewHeight =
        countryType.when(export: () => 342.0, nonExport: () => 130.0);

    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(20),
            _buildHeader(context),
            const Gap(10),
            SizedBox(
              height: listViewHeight,
              child: _buildListView(countryType),
            ),
            if (widget.isLastIndex) const Gap(40),
          ],
        ),
        if (_showLeftChevron) _buildChevronButton(isLeft: true),
        if (_showRightChevron) _buildChevronButton(isLeft: false),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.category,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          TextButton(
            onPressed: () => showCustomGeneralDialog(
              context,
              child: ProductsWidget(widget.variants, widget.category),
              percentage: 0.5,
              minRightSectionWidth: 520,
            ),
            child: const Row(
              children: [
                Text(
                  'View all',
                  style: TextStyle(color: Colors.orange),
                ),
                Icon(
                  Icons.arrow_forward,
                  color: Colors.orange,
                  size: 16,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView(CountryType countryType) {
    return ListView.builder(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      itemCount: widget.variants.length,
      itemBuilder: (context, index) {
        final variant = widget.variants[index];
        return countryType.when(
          export: () => VariantCardItem(
            // key: UniqueKey(),
            variant,
            widget.loading,
          ),
          nonExport: () => SizedBox(
            width: 250,
            child: VariantGridItem(
              variant,
              useEditingCart: false,
              quantityPickerWidth: 130,
            ),
          ),
        );
      },
    );
  }

  Widget _buildChevronButton({required bool isLeft}) {
    return Positioned(
      left: isLeft ? 0 : null,
      right: isLeft ? null : 0,
      top: 80,
      child: Skeleton.replace(
        child: GestureDetector(
          onTap: isLeft ? _scrollBackward : _scrollForward,
          child: SvgPicture.asset(
            isLeft
                ? '$kSvgDir/order/scroll_left.svg'
                : '$kSvgDir/order/scroll_right.svg',
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}

// class VariantCategoryItem extends ConsumerStatefulWidget {
//   final String category;
//   final List<Variant> variants;
//   final bool isLastIndex;
//   final bool loading;
//   const VariantCategoryItem(
//     this.category,
//     this.variants,
//     this.loading, {
//     super.key,
//     this.isLastIndex = false,
//   });

//   @override
//   ConsumerState<VariantCategoryItem> createState() =>
//       _VariantCategoryItemState();
// }

// class _VariantCategoryItemState extends ConsumerState<VariantCategoryItem> {
//   final _scrollController = ScrollController();
//   bool _showLeftChevron = false;
//   bool _showRightChevron = false;

//   @override
//   void initState() {
//     super.initState();
//     _scrollController.addListener(_onScroll);

//     WidgetsBinding.instance.addPostFrameCallback((_) => _onScroll());
//   }

//   @override
//   void dispose() {
//     _scrollController.removeListener(_onScroll);
//     _scrollController.dispose();
//     super.dispose();
//   }

//   void _onScroll() {
//     // Check if the scroll position is at the start or end of the list
//     final atStart = _scrollController.position.pixels <=
//         _scrollController.position.minScrollExtent;
//     final atEnd = _scrollController.position.pixels >=
//         _scrollController.position.maxScrollExtent;

//     setState(() {
//       _showLeftChevron = !atStart;
//       _showRightChevron = !atEnd;
//     });
//   }

//   void _scrollForward() {
//     // Scroll forward by a certain offset
//     final double offset = _scrollController.offset + 500;
//     _scrollController.animateTo(
//       offset,
//       duration: const Duration(milliseconds: 300),
//       curve: Curves.ease,
//     );
//   }

//   void _scrollBackward() {
//     // Scroll backward by a certain offset
//     final double offset = _scrollController.offset - 500;
//     _scrollController.animateTo(
//       offset,
//       duration: const Duration(milliseconds: 300),
//       curve: Curves.ease,
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const Gap(20),
//             Padding(
//               padding:
//                   const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     widget.category,
//                     style: const TextStyle(
//                         fontSize: 20, fontWeight: FontWeight.bold),
//                   ),
//                   TextButton(
//                     onPressed: () => showCustomGeneralDialog(
//                       context,
//                       child: ProductsWidget(widget.variants, widget.category),
//                       percentage: 0.5,
//                       minRightSectionWidth: 520,
//                     ),
//                     child: const Row(
//                       children: [
//                         Text('View all',
//                             style: TextStyle(color: Colors.orange)),
//                         Icon(Icons.arrow_forward,
//                             color: Colors.orange, size: 16),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             const Gap(10),
//             SizedBox(
//               height: ref
//                   .read(countryTypeProvider)
//                   .when(export: () => 342, nonExport: () => 130),
//               child: ListView.builder(
//                 controller: _scrollController,
//                 scrollDirection: Axis.horizontal,
//                 itemCount: widget.variants.length,
//                 itemBuilder: (context, index) {
//                   return ref.read(countryTypeProvider).when(
//                     export: () {
//                       return VariantCardItem(
//                         key: UniqueKey(),
//                         widget.variants[index],
//                         widget.loading,
//                       );
//                     },
//                     nonExport: () {
//                       return SizedBox(
//                         width: 250,
//                         // height: 154,
//                         child: VariantGridItem(
//                             // key: UniqueKey(),
//                             widget.variants[index],
//                             useEditingCart: false,
//                             quantityPickerWidth: 130),
//                       );
//                     },
//                   );
//                 },
//               ),
//             ),
//             if (widget.isLastIndex) const Gap(40),
//           ],
//         ),
//         if (_showLeftChevron)
//           Positioned(
//             left: 0,
//             top: 80,
//             child: Skeleton.replace(
//               child: GestureDetector(
//                 onTap: _scrollBackward,
//                 child: SvgPicture.asset(
//                   '$kSvgDir/order/scroll_left.svg',
//                   fit: BoxFit.contain,
//                 ),
//               ),
//             ),
//           ),
//         if (_showRightChevron)
//           Positioned(
//             right: 0,
//             top: 80,
//             child: Skeleton.replace(
//               child: GestureDetector(
//                 onTap: _scrollForward,
//                 child: SvgPicture.asset(
//                   '$kSvgDir/order/scroll_right.svg',
//                   fit: BoxFit.contain,
//                 ),
//               ),
//             ),
//           ),
//       ],
//     );
//   }
// }
