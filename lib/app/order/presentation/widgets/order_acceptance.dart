import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/order/domain/use_cases/order_use_cases.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class OrderAcceptanceWidget extends ConsumerWidget {
  const OrderAcceptanceWidget(
    this.orderReference, {
    super.key,
    this.onSuccess,
  });

  final String orderReference;
  final VoidCallback? onSuccess;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    Future<void> showLoadingDialog(BuildContext context) async {
      return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Row(
              children: [
                const CircularProgressIndicator(),
                const Gap(20),
                Text(
                  'Processing...',
                  style: textTheme.bodyMedium,
                ),
              ],
            ),
          );
        },
      );
    }

    Future<void> showResultDialog(
        BuildContext context, String message, bool isSuccess) async {
      return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(isSuccess ? 'Success' : 'Error'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  if (isSuccess) {
                    // reloadCurrentUrl(context);
                    onSuccess?.call();
                  }
                },
                child: const Text('Close'),
              ),
            ],
          );
        },
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 50),
      child: Container(
        height: 48,
        width: double.maxFinite,
        decoration: BoxDecoration(
          color: HexColor('#C0E5E6'),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'This order needs approval. Click an action to proceed',
                style: textTheme.bodyMedium,
              ),
              Row(
                children: [
                  TextButton(
                    onPressed: () async {
                      // Show loading dialog
                      showLoadingDialog(context);

                      final res = await ref
                          .read(acceptOrderUseCaseProvider(orderReference));

                      // Close the loading dialog
                      // ignore: use_build_context_synchronously
                      Navigator.of(context).pop();

                      res.when(
                        success: (_) async {
                          await showResultDialog(
                            context,
                            'Order accepted successfully!',
                            true,
                          );
                        },
                        failure: (error, _) async {
                          await showResultDialog(
                            context,
                            'Failed to accept the order: ${error.error.toString()}',
                            false,
                          );
                        },
                      );
                    },
                    child: Text(
                      'Accept',
                      style: textTheme.bodyMedium?.copyWith(
                        color: HexColor('#21AC53'),
                        decoration: TextDecoration.underline,
                        decorationColor: HexColor('#21AC53'),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
