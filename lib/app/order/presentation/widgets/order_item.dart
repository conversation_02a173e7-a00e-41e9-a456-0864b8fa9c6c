import 'package:flutter/material.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class OrderItemWidget extends StatelessWidget {
  const OrderItemWidget(
    this.item,
    this.currencyCode, {
    super.key,
  });

  final OrderItem item;
  final String currencyCode;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.only(bottom: 10),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Palette.stroke))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Flexible(
                  child: Text(
                    item.name ?? '',
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  ' x${item.quantity}',
                  style: textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Palette.blackSecondary,
                  ),
                )
              ],
            ),
          ),
          const Spacer(flex: 1),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerRight,
              child: CurrencyWidget(
                  ((item.price ?? 0) * (item.quantity ?? 0)), currencyCode),
            ),
          ),
        ],
      ),
    );
  }
}
