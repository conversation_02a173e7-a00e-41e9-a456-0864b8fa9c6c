import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class DeleteConfirmationDialog extends StatefulWidget {
  final VoidCallback onDelete;
  final VoidCallback onClose;

  const DeleteConfirmationDialog({
    super.key,
    required this.onDelete,
    required this.onClose,
  });

  @override
  DeleteConfirmationDialogState createState() =>
      DeleteConfirmationDialogState();
}

class DeleteConfirmationDialogState extends State<DeleteConfirmationDialog>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _closeDialog() {
    _animationController.reverse().then((_) => Navigator.of(context).pop());
  }

  void _handleDelete() {
    widget.onDelete();
    _closeDialog();
  }

  void _handleCancel() {
    widget.onClose();
    _closeDialog();
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Row(
      children: [
        IconButton(
          onPressed: _closeDialog,
          icon: const Icon(Icons.close),
        ),
        const Gap(10),
        Text(
          'Delete order',
          style: textTheme.bodyLarge?.copyWith(
            color: Palette.blackSecondary,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 11),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundColor: Colors.orange,
            child: SvgPicture.asset('$kSvgDir/order/delete.svg'),
          ),
          const SizedBox(height: 16),
          Text('Are you sure?', style: textTheme.titleMedium),
          const SizedBox(height: 8),
          Text(
            'You will be unable to undo this action once confirmed.',
            style: textTheme.bodyLarge?.copyWith(
              color: Palette.blackSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Gap(24),
          CustomFilledButton(
            onPressed: _handleDelete,
            text: 'Confirm Deletion',
          ),
          const Gap(10),
          Align(
            alignment: Alignment.center,
            child: TextButton(
              onPressed: _handleCancel,
              child: Text(
                'Cancel',
                style: textTheme.bodyLarge?.copyWith(
                  color: Palette.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const Gap(5),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: Colors.black54.withValues(alpha: 0.2),
      body: Stack(
        children: [
          GestureDetector(onTap: _closeDialog),
          SlideTransition(
            position: _slideAnimation,
            child: Align(
              alignment: Alignment.topCenter,
              child: Container(
                width: 400,
                margin: const EdgeInsets.symmetric(horizontal: 24)
                    .copyWith(top: 40),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(textTheme),
                    const Gap(10),
                    _buildContent(textTheme),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
