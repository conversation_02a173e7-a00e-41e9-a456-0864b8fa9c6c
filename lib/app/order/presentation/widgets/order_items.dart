import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/widgets/cart_quantity_field.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class OrderItemsWidget extends ConsumerWidget {
  final String title;
  final String message;
  final List<OrderItem> items;
  final String currencyCode;
  final String? errorText;
  final bool fixable;
  final ValueChanged<bool>? onOrderQuantityChanged;
  final bool isSalesOrderOrigin;

  const OrderItemsWidget({
    super.key,
    required this.title,
    required this.message,
    required this.items,
    required this.currencyCode,
    this.errorText,
    this.fixable = false,
    this.onOrderQuantityChanged,
    this.isSalesOrderOrigin = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: textTheme.headlineMedium),
        const Gap(6),
        Text(
          message,
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.blackSecondary,
          ),
        ),
        const Gap(12),
        _buildItemList(
            context, ref, textTheme, errorText, onOrderQuantityChanged),
        const Gap(20),
      ],
    );
  }

  Widget _buildItemList(
    BuildContext context,
    WidgetRef ref,
    TextTheme textTheme,
    String? errorText,
    ValueChanged<bool>? onOrderQuantityChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 14),
      decoration: BoxDecoration(
        color: Palette.kFCFCFC,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...mapIndexed(
              items,
              (index, item) => OrderItemRow(
                    item,
                    isLastIndex: index == items.length - 1,
                    fixable: fixable,
                    onOrderQuantityChanged: onOrderQuantityChanged,
                    isSalesOrderOrigin: isSalesOrderOrigin,
                  )),
          if (errorText != null) ...[
            const Gap(2),
            Text(
              errorText,
              style: textTheme.bodySmall?.copyWith(color: Palette.kFF4206),
            ),
          ]
        ],
      ),
    );
  }
}

class OrderItemRow extends ConsumerStatefulWidget {
  const OrderItemRow(this.item,
      {super.key,
      this.isLastIndex = false,
      this.fixable = false,
      this.onOrderQuantityChanged,
      this.isSalesOrderOrigin = false});

  final OrderItem item;
  final bool isLastIndex;
  final bool fixable;
  final ValueChanged<bool>? onOrderQuantityChanged;
  final bool isSalesOrderOrigin;

  @override
  ConsumerState<OrderItemRow> createState() => OrderItemRowState();
}

class OrderItemRowState extends ConsumerState<OrderItemRow> {
  bool get isExportCountry =>
      ref.read(countryTypeProvider) == CountryType.export;

  late num quantity = widget.item.quantity ?? 0;

  late final initialQuantity = widget.item.quantity ?? 0;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final cartNotifier = ref.read(cartProvider.notifier);
    final cartItem = cartNotifier.item(widget.item.variantId!);
    final maxValue = (isExportCountry || widget.isSalesOrderOrigin)
        ? defaultItemQuantity
        : cartItem?.variant.extVariant?.available ?? 0;
    return Container(
      margin: EdgeInsets.only(bottom: widget.isLastIndex ? 0 : 8),
      padding: EdgeInsets.only(bottom: widget.isLastIndex ? 0 : 8),
      decoration: BoxDecoration(
          border: Border(
        bottom: BorderSide(
            color: widget.isLastIndex ? Colors.transparent : Palette.kE7E7E7),
      )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 8,
            child: Container(
              height: 34,
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.only(left: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Palette.stroke),
              ),
              child: Text(
                widget.item.name ?? '',
                style: textTheme.bodyMedium,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          const Gap(4),
          Expanded(
            flex: 3,
            child: Container(
              height: 34,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                color: widget.fixable ? Colors.white : Palette.kECEDED,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Palette.stroke),
              ),
              child: CartQuantityInputField(
                initialCount: quantity,
                maxValue: maxValue,
                readOnly: !widget.fixable,
                cursorHeight: 14,
                focusBorder: false,
                onQuantityChanged: (value) {
                  setState(() {
                    quantity = value ?? 0;

                    final newValue = quantity > 0 ? quantity : initialQuantity;
                    cartNotifier.addVariant(cartItem!.variant, newValue);

                    if (widget.onOrderQuantityChanged != null) {
                      widget.onOrderQuantityChanged!(
                          true); // value != widget.item.quantity
                    }
                  });
                },
              ),
            ),
          ),
          const Gap(4),
          Expanded(
            flex: 3,
            child: Container(
              height: 34,
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.only(left: 8),
              decoration: BoxDecoration(
                color: Palette.kECEDED,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Palette.stroke),
              ),
              child: Text(
                CurrencyWidget.formattedAmount(
                    context, ((widget.item.price ?? 0) * quantity)),
                style: textTheme.bodyMedium,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
