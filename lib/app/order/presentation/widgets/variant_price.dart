import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

class VariantPriceWidget extends ConsumerWidget {
  const VariantPriceWidget(this.variant,
      {super.key, this.quantity = 1, this.textStyle});

  final Variant variant;
  final num? quantity;
  final TextStyle? textStyle;

  static String value(
    BuildContext context,
    WidgetRef ref,
    Variant variant, [
    num? quantity = 1,
    int? decimalDigits,
  ]) {
    final currencyCode =
        variant.currency?.iso ?? ref.read(currencyCodeProvider) ?? '';
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final variantPrice = (isExportCountry
            ? supplierPrice(ref, variant)
            : variant.vatPrice ?? variant.price ?? 0) *
        quantity!;
    return CurrencyWidget.value(context, currencyCode, variantPrice,
        decimalDigits: decimalDigits);
  }

  static String formattedPrice(
    BuildContext context,
    WidgetRef ref,
    Variant variant, [
    num? quantity = 1,
    int decimalDigits = 0,
  ]) {
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final variantPrice = (isExportCountry
            ? supplierPrice(ref, variant)
            : variant.vatPrice ?? variant.price ?? 0) *
        quantity!;
    return CurrencyWidget.formattedAmount(context, variantPrice, decimalDigits);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currencyCode = ref.read(currencyCodeProvider);

    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    final variantPrice = (isExportCountry
            ? supplierPrice(ref, variant)
            : variant.vatPrice ?? variant.price ?? 0) *
        quantity!;

    return Text(
      CurrencyWidget.value(
          context, variant.currency?.iso ?? currencyCode, variantPrice),
      style: textStyle,
    );
  }
}
