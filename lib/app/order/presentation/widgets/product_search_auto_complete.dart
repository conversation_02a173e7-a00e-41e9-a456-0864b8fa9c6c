import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/screens/products_screen.dart';
import 'package:td_procurement/app/order/presentation/widgets/cart_item.dart';
import 'package:td_procurement/app/order/presentation/widgets/outlet_search_auto_complete.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_price.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

/// Tracks focused field on export cart items
final focusedFieldProvider = StateProvider<FocusedField?>((_) => null);

class ProductSearchAutoCompleteWidget extends ConsumerStatefulWidget {
  const ProductSearchAutoCompleteWidget(this.orderType, {super.key});

  final OrderType orderType;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ProductSearchAutoCompleteWidgetState();
}

class _ProductSearchAutoCompleteWidgetState
    extends ConsumerState<ProductSearchAutoCompleteWidget>
    with SingleTickerProviderStateMixin {
  final _productController = TextEditingController();
  final _searchNotifier = ValueNotifier<bool>(false);

  late final isSalesOrderOrigin = widget.orderType == OrderType.sales;

  bool _showProductAutocomplete = false;
  bool _addingItem = true;
  // FocusedField? focusedField;
  int editingIndex = -1;

  late AnimationController _controller;
  late Animation<double> _opacity;
  final _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  // Timer? _debounce;

  bool get isExportCountry =>
      ref.read(countryTypeProvider) == CountryType.export;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _opacity = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _productController.dispose();
    _overlayEntry?.remove();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final cartItems = ref.watch(cartProvider).uniqueCartItemsWithNonZeroCount;
    final cartNotifier = ref.read(cartProvider.notifier);

    final focusedField = ref.watch(focusedFieldProvider);

    // disable the search in sales-order flow, until a customer is selected.
    final selectedCustomer = ref.watch(selectedCustomerProvider);
    final preventProductsSearch =
        isSalesOrderOrigin && selectedCustomer == null;

    ref.listen(
      supplierInventoryProvider,
      (_, inventory) {
        if (inventory is AsyncLoading) {
          _searchNotifier.value = true;
        } else {
          _searchNotifier.value = false;
        }
      },
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                isExportCountry ? 'Cart' : 'Add Products',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            Flexible(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => showCustomGeneralDialog(
                      context,
                      child: ProductsScreen(widget.orderType),
                      percentage: 0.5,
                      minRightSectionWidth: 520,
                    ),
                    child: Text(
                      'View Catalogue',
                      style: textTheme.bodySmall?.copyWith(
                        color: isExportCountry
                            ? Palette.blackSecondary
                            : Palette.primary,
                      ),
                    ),
                  ),
                  if (isExportCountry) ...[
                    const Gap(5),
                    SizedBox(
                      height: 35,
                      width: 100,
                      child: CustomFilledButton(
                        style: TextButton.styleFrom(
                          elevation: 0,
                          foregroundColor: Palette.primary,
                          backgroundColor:
                              Palette.primary.withValues(alpha: 0.2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () =>
                            resetCart(ref, clearSelectedBranch: false),
                        text: 'Clear cart',
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
        const Gap(8),
        ...mapIndexed(cartItems, (index, item) {
          return CartItemWidget(
            item,
            index: index,
            isEditing: index == editingIndex,
            focusedField: focusedField,
            ignoreQuantityLimits: isSalesOrderOrigin,
            orderType: widget.orderType,
            onFocusChange: (field) {
              setState(() {
                editingIndex = index;
                ref.read(focusedFieldProvider.notifier).state = field;
              });
            },
            onIndexChanged: (value) {
              setState(() {
                editingIndex = value;
                // focusedField = null;
              });
            },
            onSave: (count) {
              setState(() {
                editingIndex = -1;
              });
              cartNotifier.addVariant(item.variant, count);
            },
          );
        }),
        _addingItem
            ? Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: CompositedTransformTarget(
                  link: _layerLink,
                  child: SizedBox(
                    height: 36,
                    child: TextField(
                      // autofocus: _addingItem,
                      enabled: !preventProductsSearch,
                      controller: _productController,
                      decoration: InputDecoration(
                        // isDense: true,
                        filled: preventProductsSearch,
                        fillColor:
                            preventProductsSearch ? Colors.grey[200] : null,
                        border: OutlineInputBorder(
                          borderSide: BorderSide(color: Palette.stroke),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Palette.primary),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        hintText: 'Find or add item',
                        hintStyle: textTheme.bodySmall
                            ?.copyWith(color: Palette.placeholder),
                        contentPadding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8)
                            .copyWith(bottom: 40),
                        suffixIcon: ValueListenableBuilder<bool>(
                            valueListenable: _searchNotifier,
                            builder: (context, isLoading, _) {
                              return isLoading
                                  ? Row(
                                      children: [
                                        SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            valueColor: AlwaysStoppedAnimation(
                                                Palette.primary),
                                          ),
                                        ),
                                        const Gap(8),
                                      ],
                                    )
                                  : const SizedBox.shrink();
                            }),
                        suffixIconConstraints:
                            BoxConstraints.tight(const Size(30, 20)),
                      ),
                      textAlign: TextAlign.start,
                      textAlignVertical: TextAlignVertical.center,
                      // cursorColor: Palette.strokePressed,
                      cursorHeight: 14,
                      onTap: () {
                        if (_productController.text.isNotEmpty &&
                            _showProductAutocomplete) {
                          _updateOverlay();
                        }
                      },
                      onChanged: (text) {
                        // if (_debounce?.isActive ?? false) _debounce?.cancel();
                        // _debounce =
                        //     Timer(const Duration(milliseconds: 500), () {
                        handleSearch(text);
                        // });
                      },
                    ),
                  ),
                ),
              )
            : InkWell(
                onTap: () {
                  setState(() {
                    _addingItem = true;
                  });
                },
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Icon(Icons.add, color: Palette.primary),
                    Text(
                      'Add another item',
                      style: textTheme.bodyMedium
                          ?.copyWith(color: Palette.primary),
                    ),
                  ],
                ),
              ),
        const Gap(10),
      ],
    );
  }

  Future<void> handleSearch(String searchTerm) async {
    // final hexCode = ref.read(hexCodeProvider);

    // if (searchTerm.isEmpty || hexCode == null) return;

    _searchNotifier.value = true;

    final found = await _searchItems(searchTerm);

    _searchNotifier.value = false;

    // if (found.isEmpty) return;

    setState(() {
      _showProductAutocomplete = searchTerm.isNotEmpty && found.isNotEmpty;
      if (_showProductAutocomplete) {
        _updateOverlay();
      } else {
        _removeOverlay();
      }
    });
  }

  Future<List<Variant>> _searchItems(String searchTerm) async {
    List<Variant> results = [];

    if (isSalesOrderOrigin) {
      final supplierVariants = ref.read(supplierInventoryProvider);
      if (supplierVariants is AsyncData) {
        results = supplierVariants.value!;
      }
    } else {
      ref.read(countryTypeProvider).when(
        export: () {
          final exportVariants =
              ref.read(orderControllerProvider).exportVariants;
          if (exportVariants is AsyncData) {
            results = exportVariants.value!;
          }
        },
        nonExport: () {
          final nonExportVariants =
              ref.read(orderControllerProvider.notifier).nonExportVariants;
          if (nonExportVariants is AsyncData) {
            results = nonExportVariants.value!;
          }
        },
      );
    }

    final filteredResults = results
        .where((variant) => (variant.name ?? '')
            .toLowerCase()
            .contains(searchTerm.toLowerCase()))
        .toList();

    await Future.delayed(const Duration(milliseconds: 300));

    ref
        .read(orderControllerProvider.notifier)
        .updateSearchedVariants(filteredResults);

    return filteredResults;
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final orderState = ref.watch(orderControllerProvider);
    final searchedVariants = orderState.searchedVariants;
    final cartNotifier = ref.read(cartProvider.notifier);
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Only add GestureDetector when the overlay is visible
          if (_showProductAutocomplete &&
              (_controller.status == AnimationStatus.forward ||
                  _controller.status == AnimationStatus.completed))
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  _removeOverlay();
                },
              ),
            ),
          Positioned(
            width: size.width,
            left: offset.dx,
            top: offset.dy + size.height,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: const Offset(0, 38),
              child: FadeTransition(
                opacity: _opacity,
                child: Visibility(
                  visible: _controller.status == AnimationStatus.completed ||
                      _controller.status == AnimationStatus.forward,
                  child: Material(
                    elevation: 0,
                    color: Colors.white,
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxHeight: 200,
                        minHeight: 50,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Palette.kE7E7E7),
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.white,
                            boxShadow: const [
                              BoxShadow(
                                color: Colors.black12,
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: searchedVariants.hasValue
                              ? ListView.builder(
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  physics: const BouncingScrollPhysics(),
                                  itemCount: searchedVariants.value!.length,
                                  itemBuilder: (context, index) {
                                    final searchedVariant =
                                        searchedVariants.value![index];
                                    final variant = getVariantFromList(
                                            ref,
                                            searchedVariant.variantSupplierId,
                                            orderState,
                                            isExportCountry,
                                            isSalesOrderOrigin) ??
                                        searchedVariant;

                                    return StatefulBuilder(
                                      builder: (context, updateState) {
                                        return HoverableContainer(
                                          index: index,
                                          child: ListTile(
                                            title: Text(
                                              variant.name ?? '',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w400),
                                            ),
                                            subtitle: (isSalesOrderOrigin ||
                                                    isExportCountry)
                                                ? const SizedBox.shrink()
                                                : Text(
                                                    '${variant.extVariant?.available ?? 0} items available',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                            color: Palette
                                                                .blackSecondary),
                                                  ),
                                            trailing:
                                                VariantPriceWidget(variant),
                                            onTap: () {
                                              updateState(() {
                                                _removeOverlay();
                                                _addingItem = false;
                                              });

                                              setState(() {
                                                _productController.text = '';
                                                cartNotifier.addVariant(
                                                    variant, 1);
                                                // rebuild
                                              });
                                            },
                                          ),
                                        );
                                      },
                                    );
                                  },
                                )
                              : const SizedBox.shrink(),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _controller.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
        // Reset the animation to the initial state
        _controller.value = 0.0;
      });
    }
  }

  void _updateOverlay() {
    // Always remove the old overlay if it exists
    _removeOverlay();

    // Create a new overlay entry
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);

    // Start the fade-in animation
    _controller.forward();
  }
}
