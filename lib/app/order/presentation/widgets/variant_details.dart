import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/index.dart' hide Container;
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/widgets/top_bar_header.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_card_item.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_price.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_quantity_picker.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class VariantDetailsWidget extends ConsumerWidget {
  const VariantDetailsWidget(this.variant, {super.key});

  final Variant variant;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    final incoterms = ref
            .read(userControllerProvider)
            ?.currentRetailOutlet
            ?.incoterms
            ?.toUpperCase() ??
        '-';

    ref.watch(cartProvider);

    return Scaffold(
      body: Column(
        children: [
          TopBarHeader(title: variant.name ?? '-', saveBtn: false),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Gap(20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Image
                      Expanded(
                        child: AspectRatio(
                          aspectRatio: 5 / 2,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 20),
                            child:
                                CachedImage(variant.variantId, ImageSize.large),
                          ),
                        ),
                      ),
                      // const Gap(16),
                      // Product Details
                      Expanded(
                        child: Container(
                          // margin: const EdgeInsets.symmetric(horizontal: 8.0),
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.18),
                                spreadRadius: 1,
                                blurRadius: 3,
                                offset: const Offset(0, 0),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LocationChip(incoterms, variant.shipsFrom),
                              const Gap(8),
                              Text(
                                variant.description ?? variant.name ?? '-',
                                style: textTheme.headlineSmall,
                              ),
                              const Gap(8),
                              Row(
                                children: [
                                  VariantPriceWidget(variant,
                                      textStyle: textTheme.headlineSmall),
                                  const Gap(4),
                                  Text('per CS',
                                      style: textTheme.bodyMedium?.copyWith(
                                          color: Palette.blackSecondary)),
                                ],
                              ),
                              const Gap(4),
                              buildDetailRow(
                                  'EAN', variant.subUnit?.upc ?? '-', context),
                              buildDetailRow(
                                'Production Country',
                                variant.productCountry ?? '-',
                                context,
                              ),
                              const Gap(4),
                              Divider(color: Palette.stroke),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text('Quantity'),
                                  const Gap(8),
                                  SizedBox(
                                    width: 134,
                                    height: 28,
                                    child: VariantQuantityPicker(
                                      key: UniqueKey(),
                                      variant,
                                      useEditingCart: false,
                                      showInitialAddBtn: false,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 15),
                                    ),
                                  ),
                                ],
                              ),
                              Divider(color: Palette.stroke),
                              const Gap(12),
                              SizedBox(
                                width: double.maxFinite,
                                height: 38,
                                child: ElevatedButton(
                                  onPressed: () => addToCart(ref),
                                  child: const Text('Add to cart'),
                                ),
                              ),
                              const Gap(5),
                              Center(
                                child: TextButton(
                                  onPressed: () =>
                                      context.pushNamed(kCreateOrderRoute),
                                  child: const Text('Review cart',
                                      style: TextStyle(color: Colors.orange)),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Gap(24),
                  if (variant.otherSuppliers.isNotEmpty) ...[
                    Text(
                      'Ships from other locations',
                      style: textTheme.headlineSmall,
                    ),
                    const Gap(18),
                    ShippingLocations(variant, incoterms),
                    const Gap(24),
                  ],
                  // Frequently bought together
                  // const Text(
                  //   'Frequently bought together',
                  //   style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  // ),
                  // const Gap(8),
                  // Divider(color: Palette.stroke),
                  // const Gap(10),
                  // FrequentlyBoughtTogether(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void addToCart(WidgetRef ref) {
    final cartItem =
        ref.read(cartProvider.notifier).item(variant.variantSupplierId);
    num count = cartItem?.count ?? 0;
    final updatedItem = CartItem(count: count + 1, variant: variant);
    ref.read(cartProvider.notifier).addItem(updatedItem);
  }
}

class ShippingLocations extends StatelessWidget {
  const ShippingLocations(this.variant, this.incoterms, {super.key});

  final Variant variant;
  final String incoterms;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      children: variant.otherSuppliers.map((supplier) {
        final price = supplier.toMap()[incoterms.toLowerCase()] ?? 0;
        final supplierVariant = variant.copyWith(
            supplierId: supplier.companyId,
            price: price,
            currency: Currency(
                iso: supplier.currency?.iso,
                symbol: supplier.currency?.symbol));

        return Container(
          decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Palette.stroke))),
          child: ListTile(
            leading: SizedBox(
              width: 50,
              child: CachedImage(supplierVariant.variantId, ImageSize.small),
            ), //width: 50
            title: VariantPriceWidget(
              supplierVariant,
              textStyle:
                  textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  supplierVariant.description ?? supplierVariant.name ?? '-',
                  style: textTheme.bodyMedium
                      ?.copyWith(color: Palette.blackSecondary),
                ),
                const Gap(5),
                LocationChip(incoterms, supplier.shipsFrom),
              ],
            ),
            trailing: VariantQuantityPicker(
              key: UniqueKey(),
              supplierVariant,
              width: 140,
              useEditingCart: false,
              title: 'Add to cart',
            ),
          ),
        );
      }).toList(),
    );
  }
}

class FrequentlyBoughtTogether extends StatelessWidget {
  final List<String> products = [
    'Supreme Tasty Chicken Noodles 70g x 24',
    'Supreme Tasty Chicken Noodles 70g x 24',
    'Supreme Tasty Chicken Noodles 70g x 24',
    'Supreme Tasty Chicken Noodles 70g x 24',
  ];

  FrequentlyBoughtTogether({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: products.map((product) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset('assets/noodles.png',
                width: 60), // Placeholder for product image
            const Gap(4),
            Text(product, style: const TextStyle(fontSize: 12)),
            const Gap(4),
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(60, 30),
                padding: EdgeInsets.zero,
              ),
              child: const Text('Add'),
            ),
          ],
        );
      }).toList(),
    );
  }
}

class LocationChip extends StatelessWidget {
  const LocationChip(this.incoterm, this.location, {super.key});

  final String incoterm, location;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: Palette.k0679FF,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            incoterm,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
        const Gap(4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Palette.k6B797C,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              SvgPicture.asset('$kSvgDir/order/map.svg'),
              Text(
                location,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white, fontWeight: FontWeight.w400),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
