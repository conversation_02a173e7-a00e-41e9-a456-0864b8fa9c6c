import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/driver.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/domain/use_cases/order_use_cases.dart';
import 'package:td_procurement/app/order/presentation/controllers/options_provider.dart';
import 'package:td_procurement/app/order/presentation/controllers/driver_notifier.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/widgets/status_badge.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

final foundSalesLocationDriversProvider =
    StateProvider<List<Driver>>((_) => []);

class OrderAssignDriverWidget extends ConsumerStatefulWidget {
  const OrderAssignDriverWidget(this.selectedOrders, this.drivers, {super.key});

  final List<Order> selectedOrders;
  final List<Driver>? drivers;

  @override
  ConsumerState<OrderAssignDriverWidget> createState() => _OrderAssignDriver();
}

class _OrderAssignDriver extends ConsumerState<OrderAssignDriverWidget>
    with SingleTickerProviderStateMixin {
  final searchController = TextEditingController();
  final _searchNotifier = ValueNotifier<bool>(false);
  final _confirmButtonNotifier = ValueNotifier<bool>(false);
  late AnimationController _controller;
  late Animation<double> _opacity;
  bool _showOutletAutocomplete = false;
  Timer? _debounce;
  final _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
        duration: const Duration(milliseconds: 300), vsync: this);
    _opacity = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    pickAssignedDriver();
  }

  @override
  void didUpdateWidget(covariant OrderAssignDriverWidget oldWidget) {
    pickAssignedDriver();
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    searchController.dispose();
    _overlayEntry?.remove();
    _controller.dispose();
    _confirmButtonNotifier.dispose();
    super.dispose();
  }

  void pickAssignedDriver() {
    if (widget.selectedOrders.isNotEmpty &&
        widget.selectedOrders.length == 1 &&
        widget.selectedOrders.first.dispatchUser != null) {
      final dispatchRiderPhone =
          widget.selectedOrders.first.dispatchUser?.phoneNumber;
      final driver = widget.drivers?.firstWhere(
          (driver) => driver.profile?.phoneNumber == dispatchRiderPhone);
      if (driver != null) {
        _removeOverlay();
        Future.microtask(() {
          ref.read(driverProvider.notifier).setDriver(driver);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final driver = ref.watch(driverProvider);

    return Scaffold(
      body: Skeletonizer(
          enabled: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 70,
                padding: const EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                    border: Border.all(color: Palette.stroke),
                    color: Palette.kF7F7F7),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                        child: Row(
                      children: [
                        Flexible(
                            child: IconButton(
                                onPressed: () {
                                  context.pop();
                                  ref
                                      .read(driverProvider.notifier)
                                      .removeDriver();
                                },
                                icon: Skeleton.shade(
                                    child: SvgPicture.asset(
                                        '$kSvgDir/order/close.svg')))),
                        const Gap(10),
                        Flexible(
                            child: InkWell(
                          focusColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onTap: () => context.pop(),
                          child: Text(
                            'Orders',
                            style: textTheme.bodyLarge
                                ?.copyWith(color: Palette.k6B797C),
                          ),
                        )),
                        const Gap(10),
                        Skeleton.shade(
                            child: SvgPicture.asset(
                                '$kSvgDir/packs/chevron_right.svg',
                                width: 14,
                                height: 12)),
                        const Gap(10),
                        Flexible(
                            child: Text(
                          'Assign to driver',
                          style: textTheme.bodyLarge
                              ?.copyWith(color: Palette.k6B797C),
                        ))
                      ],
                    )),
                    if (driver.id.isNotEmpty)
                      Flexible(
                          child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          ConstrainedBox(
                            constraints: const BoxConstraints(
                                minWidth: 116, minHeight: 40, maxHeight: 40),
                            child: TextButton.icon(
                                style: TextButton.styleFrom(
                                    elevation: 0,
                                    foregroundColor: Palette.primary,
                                    backgroundColor:
                                        Palette.primary.withOpacity(0.2),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    disabledBackgroundColor: Palette.kECEDED),
                                onPressed: () {
                                  ref
                                      .read(driverProvider.notifier)
                                      .removeDriver();
                                },
                                label: Text('Change',
                                    style: textTheme.bodyMedium
                                        ?.copyWith(color: Palette.primary))),
                          ),
                          const Gap(20),
                          ConstrainedBox(
                            constraints: const BoxConstraints(
                                minWidth: 116, minHeight: 40, maxHeight: 40),
                            child: ValueListenableBuilder(
                                valueListenable: _confirmButtonNotifier,
                                builder: (context, loadingButton, child) {
                                  return TextButton(
                                      style: TextButton.styleFrom(
                                          fixedSize:
                                              const Size.fromWidth(116.0),
                                          backgroundColor: loadingButton
                                              ? Palette.kECEDED
                                              : Palette.primaryBlack,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          elevation: 0,
                                          disabledBackgroundColor: Palette
                                              .kECEDED),
                                      onPressed: () {
                                        if (loadingButton) {
                                          return;
                                        }
                                        handleAssignOrdersToDriver();
                                      },
                                      child: loadingButton
                                          ? const SizedBox(
                                              width: 20,
                                              height: 20,
                                              child:
                                                  CircularProgressIndicator(),
                                            )
                                          : Text('Confirm',
                                              style: textTheme.bodyMedium
                                                  ?.copyWith(
                                                      color: Palette.kFFFFFF,
                                                      fontWeight:
                                                          FontWeight.w500)));
                                }),
                          )
                        ],
                      ))
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Gap(20),
                    Text(
                      'Assign to Driver',
                      style: textTheme.headlineSmall,
                    ),
                    const Gap(15),
                    if (driver.id.isNotEmpty)
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ASSIGNED TO',
                            style: textTheme.labelMedium?.copyWith(
                              fontSize: 12,
                              color: Palette.k6B797C,
                            ),
                          ),
                          const Gap(7),
                          Text(driver.profile?.fullName ?? '',
                              style: textTheme.bodyMedium),
                        ],
                      )
                    else
                      CompositedTransformTarget(
                          link: _layerLink,
                          child: TextField(
                            controller: searchController,
                            decoration: InputDecoration(
                              hintText: 'Search by name or select',
                              suffixIcon: ValueListenableBuilder(
                                  valueListenable: _searchNotifier,
                                  builder: (context, isLoading, _) {
                                    return isLoading
                                        ? SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                              valueColor:
                                                  AlwaysStoppedAnimation(
                                                      Palette.primary),
                                            ),
                                          )
                                        : Padding(
                                            padding:
                                                const EdgeInsets.only(top: 2.0),
                                            child: SizedBox(
                                              width: 16,
                                              height: 16,
                                              child: SvgPicture.asset(
                                                kChevronDownSvg,
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          );
                                  }),
                              hintStyle: textTheme.bodyMedium
                                  ?.copyWith(color: Palette.placeholder),
                            ),
                            onTap: () {
                              if (searchController.text.isNotEmpty &&
                                  _showOutletAutocomplete) {
                                _updateOverlay();
                              }
                            },
                            onChanged: (text) {
                              if (text.isNotEmpty) {
                                if (_debounce?.isActive ?? false) {
                                  _debounce?.cancel();
                                }
                                _debounce = Timer(
                                    const Duration(milliseconds: 500), () {
                                  handleSearch(text, widget.drivers!);
                                });
                              }
                            },
                          )),
                    const Gap(10)
                  ],
                ),
              ),
              const Gap(10),
              Divider(color: Palette.stroke, height: 0),
              const Gap(31),
              Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30),
                  child: Container(
                    width: double.maxFinite,
                    decoration: BoxDecoration(
                        border: Border.all(color: Palette.stroke),
                        borderRadius: BorderRadius.circular(10)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 16, top: 14),
                          child: Text(
                            'Orders to be assigned',
                            style: textTheme.headlineSmall,
                          ),
                        ),
                        const Gap(15),
                        _buildTableHeader(context),
                        _buildTableRow(widget.selectedOrders, false, textTheme,
                            '', context),
                      ],
                    ),
                  )),
              const Gap(16)
            ],
          )),
    );
  }

  Future<void> handleAssignOrdersToDriver() async {
    final driverState = ref.read(driverProvider);
    final userState = ref.read(userControllerProvider);
    if (driverState.id.isEmpty) {
      return Toast.error(
          'You need to pick a driver to assign these orders to', context,
          title: 'Cannot assign orders.');
    }
    _confirmButtonNotifier.value = true;
    final res = await ref.read(assignDriverToOrdersUseCaseProvider(
        AssignOrdersParams(
            locationId:
                userState?.retailOutlets?.firstOrNull?.salesLocationId ?? '',
            driverId: driverState.id,
            orderIds: widget.selectedOrders.map((e) => e.id!).toList())));

    return res.when(
      success: (data) {
        ref.read(orderControllerProvider.notifier).fetchSalesOrders(
            ref.read(orderControllerProvider).fetchSalesOrdersParams,
            forced: true);
        _confirmButtonNotifier.value = false;
        context.pop();
        ref.read(optionsProvider.notifier).clearList();
        ref.read(driverProvider.notifier).removeDriver();
        Toast.success('Orders assigned to driver', context);
      },
      failure: (error, code) {
        Toast.apiError(error, context, title: 'Error assigning orders');
        ref.read(driverProvider.notifier).removeDriver();
        _confirmButtonNotifier.value = false;
      },
    );
  }

  Future<void> handleSearch(String searchTerm, List<Driver> drivers) async {
    _removeOverlay();
    _searchNotifier.value = true;
    await _searchDrivers(searchTerm, drivers);
    _searchNotifier.value = false;

    setState(() {
      _showOutletAutocomplete = searchTerm.isNotEmpty;
    });

    _updateOverlay();
  }

  Future<List<Driver>> _searchDrivers(
      String searchTerm, List<Driver> drivers) async {
    List<Driver> results = [];

    results = drivers
        .where((driver) => (driver.profile?.fullName ?? '')
            .toLowerCase()
            .contains(searchTerm.toLowerCase()))
        .toList();

    ref.read(foundSalesLocationDriversProvider.notifier).state = results;
    return results;
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final searchedDrivers = ref.watch(foundSalesLocationDriversProvider);

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          if (_showOutletAutocomplete &&
              (_controller.status == AnimationStatus.forward ||
                  _controller.status == AnimationStatus.completed))
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeOverlay,
              ),
            ),
          Positioned(
            width: size.width - 50,
            left: offset.dx,
            top: offset.dy + size.height,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: const Offset(0, 52),
              child: FadeTransition(
                opacity: _opacity,
                child: Visibility(
                  visible: _controller.status == AnimationStatus.completed ||
                      _controller.status == AnimationStatus.forward,
                  child: Material(
                    elevation: 0,
                    color: Colors.white,
                    child: Builder(builder: (context) {
                      final textTheme = Theme.of(context).textTheme;
                      return ConstrainedBox(
                        constraints:
                            const BoxConstraints(maxHeight: 200, minHeight: 50),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Palette.kE7E7E7),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.white,
                              boxShadow: const [
                                BoxShadow(
                                    color: Colors.black12,
                                    blurRadius: 4,
                                    offset: Offset(0, 2))
                              ],
                            ),
                            child: searchedDrivers.isNotEmpty
                                ? ListView.builder(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount: searchedDrivers.length,
                                    itemBuilder: (context, index) {
                                      final searchedDriver =
                                          searchedDrivers[index];
                                      return HoverableContainer(
                                        index: index,
                                        child: ListTile(
                                          title: Text(
                                              searchedDriver
                                                      .profile?.fullName ??
                                                  '-',
                                              style: textTheme.bodyLarge
                                                  ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w600)),
                                          subtitle: Text(
                                            searchedDriver.profile?.fullName ??
                                                '-',
                                            style: textTheme.bodyMedium
                                                ?.copyWith(
                                                    fontWeight: FontWeight.w600,
                                                    color:
                                                        Palette.blackSecondary),
                                          ),
                                          onTap: () {
                                            _removeOverlay();
                                            ref
                                                .read(driverProvider.notifier)
                                                .setDriver(searchedDriver);
                                          },
                                        ),
                                      );
                                    },
                                  )
                                : const SizedBox.shrink(),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _controller.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
        // Reset the animation to the initial state
        _controller.value = 0.0;
      });
    }
  }

  void _updateOverlay() {
    // Always remove the old overlay if it exists
    _removeOverlay();
    if (ref.read(foundSalesLocationDriversProvider).isEmpty) {
      // // Show add customer overlay if no search results
      // _addCustomerOverlayEntry = _createAddCustomerOverlayEntry();
      // Overlay.of(context).insert(_addCustomerOverlayEntry!);
      // _controller.forward();
    } else {
      // Create a new overlay entry
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
      // Start the fade-in animation
      _controller.forward();
    }
  }
}

Widget _buildTableHeader(context) {
  final textTheme = Theme.of(context).textTheme;
  return Container(
    color: Palette.kF7F7F7,
    // height: 36,
    padding: const EdgeInsets.symmetric(vertical: 10),
    alignment: Alignment.center,
    child: Table(
      columnWidths: const {
        0: FlexColumnWidth(2.3),
        1: FlexColumnWidth(3),
        2: FlexColumnWidth(2.9),
        3: FlexColumnWidth(1.5),
        4: FlexColumnWidth(1),
      },
      children: [
        TableRow(
          children: [
            _buildHeaderCell('Amount', textTheme),
            _buildHeaderCell('Reference', textTheme),
            _buildHeaderCell('Delivering to', textTheme),
            _buildHeaderCell('Created', textTheme),
            Container(),
          ],
        ),
      ],
    ),
  );
}

Widget _buildHeaderCell(String text, TextTheme textTheme) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8),
    child: Text(
      text,
      style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
    ),
  );
}

Widget _buildTableRow(
  List<Order> orders,
  bool isHovered,
  TextTheme textTheme,
  String consoleUrl,
  BuildContext context,
) {
  return Table(
    columnWidths: const {
      0: FlexColumnWidth(1.5),
      1: FlexColumnWidth(2),
      2: FlexColumnWidth(1.8),
      3: FlexColumnWidth(1.5),
      4: FlexColumnWidth(.5),
    },
    children: [
      ...orders.map((el) {
        return TableRow(
          decoration: BoxDecoration(
              border: Border(
            bottom: BorderSide(color: Palette.stroke),
          )),
          children: [
            _buildContentAmount(el.total ?? 0, textTheme, el.currency!.iso!,
                el.currency!.symbol!),
            _buildContentCell(el.globalOrderNumber ?? '-', textTheme),
            _buildContentCell(el.stockLocationName ?? '-', textTheme),
            _buildContentBadge(el.createdAt!.toDate(), el.status!, textTheme),
            Container(),
          ],
        );
      })
    ],
  );
}

Widget _buildContentCell(
  dynamic content,
  TextTheme textTheme,
) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
    child: content is String
        ? Text(
            content,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          )
        : content,
  );
}

Widget _buildContentAmount(
    num amount, TextTheme textTheme, String iso, String symbol) {
  return Padding(
    padding: const EdgeInsets.all(10),
    child: Row(
      children: [
        CurrencyWidget(
          amount,
          symbol,
          amountStyle: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const Gap(5),
        Text(
          iso,
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.blackSecondary,
          ),
        ),
      ],
    ),
  );
}

Widget _buildContentBadge(String date, String status, TextTheme textTheme) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
    child: Row(
      children: [
        Text(
          date,
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.blackSecondary,
          ),
        ),
        const Gap(3),
        SalesOrderStatusBadge(status)
      ],
    ),
  );
}
