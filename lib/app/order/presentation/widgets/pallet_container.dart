import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/domain/use_cases/order_use_cases.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

import 'index.dart';

class PalletContainerWidget extends ConsumerStatefulWidget {
  const PalletContainerWidget({super.key});

  @override
  ConsumerState<PalletContainerWidget> createState() =>
      _PalletContainerWidgetState();
}

class _PalletContainerWidgetState extends ConsumerState<PalletContainerWidget>
    with SingleTickerProviderStateMixin {
  final _loadingCheckout = ValueNotifier<bool>(false);
  final _disabledCheckout = ValueNotifier<bool>(true);
  final _loadingPrepareOrder = ValueNotifier<bool>(false);
  final _disabledRecalculate = ValueNotifier<bool>(false);

  bool _calculated = false;
  bool _disabledRecalcFlag = false;
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Initialize based on current order preview state.
    final previewDetail = ref.read(orderPreviewDetailProvider);
    _calculated = previewDetail != null;
    _disabledCheckout.value = previewDetail != null ? false : true;
    _disabledRecalculate.value = _disabledRecalcFlag;

    // Generate draft order reference.
    ref.read(cartProvider.notifier).populateReference();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  TextTheme get _textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    // Listen for cart updates.
    ref.listen<CartState>(cartProvider, (previous, next) {
      if (previous?.cartItems != next.cartItems) {
        _disabledRecalculate.value = false;
        _disabledRecalcFlag = false;
      }
      if (next.uniqueCartItems.isEmpty && _calculated) {
        setState(() {
          _calculated = false;
        });
      }
    });

    // Listen for order preview detail updates.
    ref.listen<OrderPreviewDetail?>(orderPreviewDetailProvider, (prev, next) {
      if (next != null) {
        _disabledCheckout.value = false;
        if (!_calculated) {
          setState(() {
            _calculated = true;
          });
        }
      }
    });

    final previewDetail = ref.watch(orderPreviewDetailProvider);
    final cartItems = ref.watch(cartProvider).uniqueCartItems;

    // Show when there are items and we have calculated container info.
    if (cartItems.isNotEmpty && _calculated) {
      return SingleChildScrollView(
        child: Column(
          children: [
            _buildOrderTotalSection(previewDetail!),
            const Gap(20),
            _buildContainerInfoSection(previewDetail),
          ],
        ),
      );
    }

    // Otherwise, show the empty state prompting for calculation.
    return _buildEmptyStateContent();
  }

  Widget _buildOrderTotalSection(OrderPreviewDetail previewDetail) {
    final previewOrders = previewDetail.orders ?? [];
    final currencyCode = previewOrders.isNotEmpty
        ? (previewOrders.first.currency?.iso ?? ref.read(currencyCodeProvider))
        : ref.read(currencyCodeProvider);
    final total = previewOrders.fold<double>(
      0.0,
      (sum, order) => sum + (order.total ?? 0.0),
    );

    return Container(
      padding: const EdgeInsets.all(20.0),
      margin: const EdgeInsets.symmetric(horizontal: 60).copyWith(top: 40),
      decoration: BoxDecoration(
        color: HexColor('#FCF3E8'),
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Order Total',
                  style: _textTheme.headlineSmall?.copyWith(fontSize: 20)),
              Text(
                CurrencyWidget.valueWithIso(context, currencyCode ?? '', total),
                style: _textTheme.headlineSmall?.copyWith(fontSize: 20),
              ),
            ],
          ),
          const Gap(16),
          SizedBox(
            width: double.infinity,
            child: CustomFilledButton(
              text: 'Checkout',
              disabledNotifier: _disabledCheckout,
              loaderNotifier: _loadingCheckout,
              onPressed: createExportOrder,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContainerInfoSection(OrderPreviewDetail previewDetail) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 60),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Palette.stroke),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text('Container information',
                  style: _textTheme.headlineSmall?.copyWith(fontSize: 20)),
            ),
            const Gap(10),
            _buildTabSection(previewDetail),
            Padding(
              padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 20.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(child: _buildViewDetailsButton(previewDetail)),
                  const Gap(2),
                  Flexible(child: _buildRecalculateButton()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabSection(OrderPreviewDetail previewDetail) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border(top: BorderSide(color: Palette.stroke)),
          ),
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            labelColor: Palette.primary,
            unselectedLabelColor: Palette.primaryBlack,
            indicatorColor: Palette.primary,
            indicatorWeight: 1.0,
            tabAlignment: TabAlignment.start,
            dividerColor: Palette.stroke,
            labelStyle:
                _textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            onTap: (index) {
              setState(() {
                _tabController.index = index;
              });
            },
            tabs: const [
              Tab(text: "Direct Sourcing"),
              Tab(text: "Truck Load"),
            ],
          ),
        ),
        IndexedStack(
          index: _tabController.index,
          children: [
            SingleChildScrollView(
              child: Column(
                children: _buildContainerSections(previewDetail),
              ),
            ),
            SingleChildScrollView(
              child: Column(
                children: _buildTruckSections(previewDetail),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildViewDetailsButton(OrderPreviewDetail previewDetail) {
    return ValueListenableBuilder<bool>(
      valueListenable: _disabledRecalculate,
      builder: (context, isDisabled, _) {
        return SizedBox(
          height: 40,
          child: OutlinedButton(
            onPressed: isDisabled
                ? () =>
                    context.pushNamed(kContainerInfoRoute, extra: previewDetail)
                : null,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                side: BorderSide(color: Palette.primaryBlack),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'View detailed information',
              style: _textTheme.bodyMedium?.copyWith(
                color: isDisabled ? Palette.primaryBlack : Palette.placeholder,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecalculateButton() {
    return SizedBox(
      height: 40,
      child: CustomFilledButton(
        style: FilledButton.styleFrom(
          elevation: 0,
          foregroundColor: Palette.primary,
          backgroundColor: Palette.primary.withValues(alpha: 0.2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: _textTheme.bodyMedium,
        ),
        text: 'Recalculate',
        disabledNotifier: _disabledRecalculate,
        loaderNotifier: _loadingPrepareOrder,
        onPressed: handleExportPrepareOrder,
      ),
    );
  }

  Widget _buildEmptyStateContent() {
    return Container(
      color: Palette.kFCFCFC,
      height: MediaQuery.sizeOf(context).height,
      child: Column(
        children: [
          Gap(160.h),
          Container(
            width: 93,
            height: 48,
            decoration: BoxDecoration(
              border: Border.all(color: Palette.stroke),
              borderRadius: BorderRadius.circular(40),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset('$kSvgDir/order/shipment.svg'),
                SvgPicture.asset('$kSvgDir/order/power_plant.svg'),
              ],
            ),
          ),
          const Gap(20),
          Text(
            'Sourcing and Container\ninformation',
            style: _textTheme.headlineSmall?.copyWith(fontSize: 20),
            textAlign: TextAlign.center,
          ),
          const Gap(10),
          Text(
            'Click the “Calculate” button to calculate\nsourcing and container information',
            textAlign: TextAlign.center,
            style: _textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
          const Gap(20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 100),
            child: SizedBox(
              width: 200,
              child: CustomFilledButton(
                text: 'Calculate',
                loaderNotifier: _loadingPrepareOrder,
                onPressed: handleExportPrepareOrder,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildContainerSections(OrderPreviewDetail previewDetail) {
    return mapIndexed(
      previewDetail.containerUtilization,
      (index, utilization) => PalletWidget(
        name: utilization.name ?? '',
        cTwentyQty: utilization.summary?.mid?.containersRequired ?? 0,
        cFortyQty: utilization.summary?.large?.containersRequired ?? 0,
        uTwentyQty: utilization.summary?.mid?.lastContainerUtilization ?? 0,
        uFortyQty: utilization.summary?.large?.lastContainerUtilization ?? 0,
        isLastIndex: index == previewDetail.containerUtilization.length - 1,
      ),
    ).toList();
  }

  List<Widget> _buildTruckSections(OrderPreviewDetail previewDetail) {
    return mapIndexed(
      previewDetail.containerUtilization,
      (index, utilization) => TruckWidget(
        name: utilization.name?.toUpperCase() ?? '',
        trucksRequired: utilization.summary?.truck?.trucksRequired ?? 0,
        uLastTruck: utilization.summary?.truck?.lastTruckUtilization ?? 0,
        isLastIndex: index == previewDetail.containerUtilization.length - 1,
      ),
    ).toList();
  }

  Future<void> handleExportPrepareOrder() async {
    final cartState = ref.read(cartProvider);

    if (cartState.branch == null) {
      Toast.error(
        'You need to select a location to proceed',
        context,
        title: 'Select location',
      );
      return;
    }
    if (cartState.uniqueCartItemsWithNonZeroCount.isEmpty) {
      Toast.error(
        'You must select an item to proceed',
        context,
        title: 'Add an item',
      );
      return;
    }

    _loadingPrepareOrder.value = true;

    final params = PrepareOrderParams(
      outletId: cartState.branch!.id,
      cartItems: cartState.uniqueCartItemsWithNonZeroCount,
      contactPhone: cartState.branch!.contactPhone,
      isExportCountry: ref.read(countryTypeProvider) == CountryType.export,
      extChannel: 'SHOP.B2B',
    );

    final res = await ref.read(prepareExportOrderUseCaseProvider(params));
    res.when(
      success: (data) {
        _loadingPrepareOrder.value = false;
        _disabledRecalculate.value = true;
        _disabledRecalcFlag = true;
        ref.read(orderPreviewDetailProvider.notifier).state = data;
      },
      failure: (error, _) {
        _loadingPrepareOrder.value = false;
        Toast.apiError(
          error,
          context,
          title: 'Failed to calculate sourcing and container information',
        );
      },
    );
  }

  Future<void> createExportOrder() async {
    final previewDetail = ref.read(orderPreviewDetailProvider);
    if (previewDetail == null) return;

    _loadingCheckout.value = true;
    final params = CreateOrderParams(orders: previewDetail.rawData!);
    final res = await ref.read(createExportOrderUseCaseProvider(params));

    res.when(
      success: (_) {
        _loadingCheckout.value = false;
        context.goNamed(kOrdersRoute, extra: {'isRefreshing': true});
        Toast.success('Created your order successfully', context);
        Future.microtask(() => resetCart(ref));
      },
      failure: (error, _) {
        _loadingCheckout.value = false;
        Toast.apiError(error, context, title: 'Error Creating Orders');
      },
    );
  }
}

// class PalletContainerWidget extends ConsumerStatefulWidget {
//   const PalletContainerWidget({super.key});

//   @override
//   ConsumerState<PalletContainerWidget> createState() =>
//       _PalletContainerWidgetState();
// }

// class _PalletContainerWidgetState extends ConsumerState<PalletContainerWidget>
//     with SingleTickerProviderStateMixin {
//   // late TabController _tabController;

//   final _loadingCheckout = ValueNotifier<bool>(false);
//   final _disabledCheckout = ValueNotifier<bool>(true);
//   final _loadingPrepareOrder = ValueNotifier<bool>(false);
//   final _disabledRecalculate = ValueNotifier<bool>(false);

//   bool calculated = false;
//   bool disabledRecalculate = false;

//   late TabController _tabController;

//   @override
//   void initState() {
//     super.initState();

//     _tabController = TabController(length: 2, vsync: this);

//     // update checkout button disabled state
//     final previewDetail = ref.read(orderPreviewDetailProvider);
//     if (previewDetail != null) {
//       _disabledCheckout.value = false;
//     }

//     calculated = previewDetail != null;

//     _disabledRecalculate.value = disabledRecalculate;

//     // generate draft order reference
//     ref.read(cartProvider.notifier).populateReference();
//   }

//   @override
//   void dispose() {
//     _tabController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final headlineSmall = textTheme.headlineSmall?.copyWith(fontSize: 20);

//     // enable button to recalculate container information as needed
//     ref.listen(cartProvider, (oldState, newState) {
//       if (oldState?.cartItems != newState.cartItems) {
//         _disabledRecalculate.value = false;
//         disabledRecalculate = false;
//       }

//       if (newState.uniqueCartItems.isEmpty) {
//         setState(() {
//           calculated = false;
//         });
//       }
//     });

//     // enable checkout button
//     ref.listen(orderPreviewDetailProvider, (oldState, newState) {
//       if (newState != null) {
//         _disabledCheckout.value = false;
//         setState(() {
//           calculated = true;
//         });
//       }
//     });

//     Widget child = const SizedBox.shrink();

//     final previewDetail = ref.watch(orderPreviewDetailProvider);
//     final cartItems = ref.watch(cartProvider).uniqueCartItems;

//     (cartItems.isNotEmpty && calculated)
//         ? child = Builder(builder: (context) {
//             final previewOrders = previewDetail?.orders ?? [];

//             final currencyCode = (previewOrders.isNotEmpty
//                 ? previewOrders.first.currency?.iso ??
//                     ref.read(currencyCodeProvider)
//                 : ref.read(currencyCodeProvider));

//             final total = previewOrders.fold(0.0,
//                 (num sum, OrderPreview current) => sum + (current.total ?? 0));

//             return Column(
//               children: [
//                 Container(
//                   padding: const EdgeInsets.all(20.0),
//                   margin: const EdgeInsets.symmetric(horizontal: 60)
//                       .copyWith(top: 40),
//                   decoration: BoxDecoration(
//                     color: HexColor('#FCF3E8'),
//                     border: Border.all(color: Palette.stroke),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Text(
//                             'Order Total',
//                             style: headlineSmall,
//                           ),
//                           Text(
//                             CurrencyWidget.valueWithIso(
//                                 context, currencyCode ?? '', total),
//                             style: headlineSmall,
//                           ),
//                         ],
//                       ),
//                       const Gap(16),
//                       SizedBox(
//                         width: double.maxFinite,
//                         child: CustomFilledButton(
//                           text: 'Checkout',
//                           disabledNotifier: _disabledCheckout,
//                           loaderNotifier: _loadingCheckout,
//                           onPressed: createExportOrder,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 const Gap(20),
//                 Container(
//                   margin: const EdgeInsets.symmetric(horizontal: 60),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.stretch,
//                     children: [
//                       Container(
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           border: Border.all(color: Palette.stroke),
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Padding(
//                               padding: const EdgeInsets.all(16.0),
//                               child: Text(
//                                 'Container information',
//                                 style: headlineSmall,
//                               ),
//                             ),
//                             const Gap(10),
//                             Column(
//                               crossAxisAlignment: CrossAxisAlignment.stretch,
//                               children: [
//                                 // TabBar
//                                 Container(
//                                   // height: 40,
//                                   decoration: BoxDecoration(
//                                     border: Border(
//                                       top: BorderSide(color: Palette.stroke),
//                                     ),
//                                   ),
//                                   child: TabBar(
//                                     controller: _tabController,
//                                     isScrollable: true,
//                                     labelColor: Palette.primary,
//                                     unselectedLabelColor: Palette.primaryBlack,
//                                     indicatorColor: Palette.primary,
//                                     indicatorWeight: 1.0,
//                                     tabAlignment: TabAlignment.start,
//                                     dividerColor: Palette.stroke,
//                                     labelStyle: textTheme.bodyMedium
//                                         ?.copyWith(fontWeight: FontWeight.w500),
//                                     onTap: (index) {
//                                       _tabController.index = index;
//                                       setState(() {
//                                         // rebuild
//                                       });
//                                     },
//                                     tabs: const [
//                                       Tab(text: "Direct Sourcing"),
//                                       Tab(text: "Truck Load"),
//                                     ],
//                                   ),
//                                 ),
//                                 // Tab Content
//                                 IndexedStack(
//                                   index: _tabController.index,
//                                   children: [
//                                     SingleChildScrollView(
//                                       child: Column(
//                                         children: _buildContainerSections(
//                                             previewDetail!),
//                                       ),
//                                     ),
//                                     SingleChildScrollView(
//                                       child: Column(
//                                         children:
//                                             _buildTruckSections(previewDetail),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ],
//                             ),
//                             Padding(
//                               padding: const EdgeInsets.all(12.0)
//                                   .copyWith(bottom: 20, top: 40),
//                               child: Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Expanded(
//                                     child: ValueListenableBuilder<bool>(
//                                         valueListenable: _disabledRecalculate,
//                                         builder: (context, isDisabled, _) {
//                                           return SizedBox(
//                                             height: 40,
//                                             child: OutlinedButton(
//                                               onPressed: isDisabled
//                                                   ? () {
//                                                       context.pushNamed(
//                                                           kContainerInfoRoute,
//                                                           extra: previewDetail);
//                                                     }
//                                                   : null,
//                                               style: OutlinedButton.styleFrom(
//                                                 padding:
//                                                     const EdgeInsets.symmetric(
//                                                         horizontal: 20,
//                                                         vertical: 12),
//                                                 shape: RoundedRectangleBorder(
//                                                   side: BorderSide(
//                                                       color:
//                                                           Palette.primaryBlack),
//                                                   borderRadius:
//                                                       BorderRadius.circular(8),
//                                                 ),
//                                               ),
//                                               child: Text(
//                                                 'View detailed information',
//                                                 style: textTheme.bodyMedium
//                                                     ?.copyWith(
//                                                         color: !isDisabled
//                                                             ? Palette
//                                                                 .placeholder
//                                                             : Palette
//                                                                 .primaryBlack),
//                                               ),
//                                             ),
//                                           );
//                                         }),
//                                   ),
//                                   const Gap(2),
//                                   Flexible(
//                                     child: SizedBox(
//                                       height: 40,
//                                       child: CustomFilledButton(
//                                         style: FilledButton.styleFrom(
//                                             elevation: 0,
//                                             foregroundColor: Palette.primary,
//                                             backgroundColor: Palette.primary
//                                                 .withValues(alpha: 0.2),
//                                             shape: RoundedRectangleBorder(
//                                               borderRadius:
//                                                   BorderRadius.circular(8),
//                                             ),
//                                             textStyle: textTheme.bodyMedium),
//                                         text: 'Recalculate',
//                                         disabledNotifier: _disabledRecalculate,
//                                         loaderNotifier: _loadingPrepareOrder,
//                                         onPressed: handleExportPrepareOrder,
//                                       ),
//                                     ),
//                                   )
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 )
//               ],
//             );
//           })
//         : child = Container(
//             color: Palette.kFCFCFC,
//             height: MediaQuery.sizeOf(context).height,
//             child: Column(
//               children: [
//                 Gap(160.h),
//                 Container(
//                   width: 93,
//                   height: 48,
//                   decoration: BoxDecoration(
//                     border: Border.all(color: Palette.stroke),
//                     borderRadius: BorderRadius.circular(40),
//                   ),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       SvgPicture.asset('$kSvgDir/order/shipment.svg'),
//                       SvgPicture.asset('$kSvgDir/order/power_plant.svg'),
//                     ],
//                   ),
//                 ),
//                 const Gap(20),
//                 Text(
//                   'Sourcing and Container\ninformation',
//                   style: headlineSmall,
//                   textAlign: TextAlign.center,
//                 ),
//                 const Gap(10),
//                 Text(
//                   'Click the “Calculate” button to calculate\nsourcing and container information',
//                   textAlign: TextAlign.center,
//                   style: textTheme.bodyMedium?.copyWith(
//                     color: Palette.blackSecondary,
//                   ),
//                 ),
//                 const Gap(20),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 100),
//                   child: SizedBox(
//                     width: 200,
//                     child: CustomFilledButton(
//                       text: 'Calculate',
//                       loaderNotifier: _loadingPrepareOrder,
//                       onPressed: handleExportPrepareOrder,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           );

//     return child;
//   }

//   TextTheme get textTheme => Theme.of(context).textTheme;

//   List<Widget> _buildContainerSections(OrderPreviewDetail previewDetail) {
//     final cUtilizations = previewDetail.containerUtilization;

//     return mapIndexed(
//       cUtilizations,
//       (index, x) => PalletWidget(
//         name: x.name ?? '',
//         cTwentyQty: x.summary?.mid?.containersRequired ?? 0,
//         cFortyQty: x.summary?.large?.containersRequired ?? 0,
//         uTwentyQty: x.summary?.mid?.lastContainerUtilization ?? 0,
//         uFortyQty: x.summary?.large?.lastContainerUtilization ?? 0,
//         isLastIndex: index == cUtilizations.length - 1,
//       ),
//     ).toList();
//   }

//   List<Widget> _buildTruckSections(OrderPreviewDetail previewDetail) {
//     final cUtilizations = previewDetail.containerUtilization;

//     return mapIndexed(
//       cUtilizations,
//       (index, x) => TruckWidget(
//         name: x.name.toString().toUpperCase(),
//         trucksRequired: x.summary?.truck?.trucksRequired ?? 0,
//         uLastTruck: x.summary?.truck?.lastTruckUtilization ?? 0,
//         isLastIndex: index == cUtilizations.length - 1,
//       ),
//     ).toList();
//   }

//   Future<void> handleExportPrepareOrder() async {
//     final cartState = ref.read(cartProvider);

//     if (cartState.branch == null) {
//       return Toast.error('You need to select a location to proceed', context,
//           title: 'Select location');
//     }

//     if (cartState.uniqueCartItemsWithNonZeroCount.isEmpty) {
//       return Toast.error('You must select an item to proceed', context,
//           title: 'Add an item');
//     }

//     _loadingPrepareOrder.value = true;

//     final params = PrepareOrderParams(
//       outletId: cartState.branch?.id ?? '',
//       cartItems: cartState.uniqueCartItemsWithNonZeroCount,
//       contactPhone: cartState.branch?.contactPhone ?? '',
//       isExportCountry: ref.read(countryTypeProvider) == CountryType.export,
//       extChannel: 'SHOP.B2B',
//     );

//     final res = await ref.read(prepareExportOrderUseCaseProvider(params));

//     res.when(
//       success: (data) {
//         _loadingPrepareOrder.value = false;
//         _disabledRecalculate.value = true;
//         disabledRecalculate = true;
//         ref.read(orderPreviewDetailProvider.notifier).state = data;
//       },
//       failure: (error, _) {
//         _loadingPrepareOrder.value = false;
//         Toast.apiError(error, context,
//             title: 'Failed to calculate sourcing and container information');
//       },
//     );
//   }

//   Future<void> createExportOrder() async {
//     final previewDetail = ref.read(orderPreviewDetailProvider);

//     if (previewDetail == null) return;

//     _loadingCheckout.value = true;

//     final params = CreateOrderParams(orders: previewDetail.rawData!);
//     final res = await ref.read(createExportOrderUseCaseProvider(params));

//     res.when(
//       success: (_) {
//         _loadingCheckout.value = false;
//         context.goNamed(kOrdersRoute, extra: {'isRefreshing': true});
//         Toast.success('Created your order successfully', context);
//         Future.microtask(() {
//           resetCart(ref);
//         });
//       },
//       failure: (error, _) {
//         _loadingCheckout.value = false;
//         Toast.apiError(error, context, title: 'Error Creating Orders');
//       },
//     );
//   }
// }
