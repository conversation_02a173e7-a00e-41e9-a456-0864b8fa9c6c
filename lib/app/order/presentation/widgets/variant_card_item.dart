import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_details.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_price.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_quantity_picker.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class VariantCardItem extends ConsumerWidget {
  final Variant variant;
  final bool ignoreQuantityLimits;
  final bool loading;

  const VariantCardItem(
    this.variant,
    this.loading, {
    super.key,
    this.ignoreQuantityLimits = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final incoterm = ref
        .read(userControllerProvider)
        ?.currentRetailOutlet
        ?.incoterms
        ?.toUpperCase();

    return InkWell(
      onTap: () => showCustomGeneralDialog(
        context,
        child: VariantDetailsWidget(variant),
        percentage: 0.44,
        minRightSectionWidth: 630,
      ),
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      focusColor: Colors.transparent,
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      child: Container(
        width: 260,
        margin: const EdgeInsets.symmetric(horizontal: 8.0),
        padding: const EdgeInsets.all(12.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.18),
              spreadRadius: 1,
              blurRadius: 1,
              offset: const Offset(0, 0),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImage(),
            VariantPriceWidget(variant, textStyle: textTheme.headlineSmall),
            const Gap(4),
            _buildDescription(textTheme),
            const Gap(8),
            buildDetailRow('Ships from', variant.shipsFrom, context),
            buildDetailRow('EAN #', variant.subUnit?.upc ?? '-', context),
            buildDetailRow(
                'Production Country', variant.productCountry ?? '-', context),
            _buildIncotermRow(textTheme, incoterm),
            const Gap(14),
            _buildQuantityPicker(),
          ],
        ),
      ),
    );
  }

  Widget _buildImage() {
    return Align(
      alignment: Alignment.topCenter,
      child: SizedBox(
        width: 76.15,
        height: 110,
        child: CachedImage(variant.variantId, ImageSize.large),
      ),
    );
  }

  Widget _buildDescription(TextTheme textTheme) {
    return Flexible(
      child: Text(
        variant.description ?? variant.name ?? '-',
        style: textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          color: Palette.blackSecondary,
        ),
      ),
    );
  }

  Widget _buildIncotermRow(TextTheme textTheme, String? incoterm) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Incoterms',
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w400,
            color: Palette.blackSecondary,
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: loading ? null : Palette.k0679FF,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            incoterm ?? '-',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildQuantityPicker() {
    return ConstrainedBox(
      constraints: BoxConstraints.loose(const Size(120, 120)),
      child: VariantQuantityPicker(
        key: ValueKey(variant.variantSupplierId),
        variant,
        useEditingCart: false,
        ignoreQuantityLimits: ignoreQuantityLimits,
      ),
    );
  }
}

Widget buildDetailRow(String label, String value, BuildContext context) {
  final textTheme = Theme.of(context).textTheme;
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w400,
            color: Palette.blackSecondary,
          ),
        ),
        Text(
          value,
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    ),
  );
}


// class VariantCardItem extends ConsumerWidget {
//   final Variant variant;
//   final bool ignoreQuantityLimits;
//   final bool loading;

//   const VariantCardItem(
//     this.variant,
//     this.loading, {
//     super.key,
//     this.ignoreQuantityLimits = false,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final textTheme = Theme.of(context).textTheme;

//     final incoterm = ref
//         .read(userControllerProvider)
//         ?.currentRetailOutlet
//         ?.incoterms
//         ?.toUpperCase();

//     ref.watch(cartProvider);

//     return InkWell(
//       onTap: () => showCustomGeneralDialog(
//         context,
//         child: VariantDetailsWidget(variant),
//         percentage: 0.44,
//         minRightSectionWidth: 630,
//       ),
//       splashColor: Colors.transparent,
//       highlightColor: Colors.transparent,
//       hoverColor: Colors.transparent,
//       focusColor: Colors.transparent,
//       overlayColor: WidgetStateProperty.all(Colors.transparent),
//       child: Container(
//         width: 260,
//         margin: const EdgeInsets.symmetric(horizontal: 8.0),
//         padding: const EdgeInsets.all(12.0),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(8),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.grey.withValues(alpha: 0.18),
//               spreadRadius: 1,
//               blurRadius: 1,
//               offset: const Offset(0, 0),
//             ),
//           ],
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Align(
//               alignment: Alignment.topCenter,
//               child: SizedBox(
//                 width: 76.15,
//                 height: 110,
//                 child: CachedImage(variant.variantId, ImageSize.large),
//               ),
//             ),
//             // const Gap(8),
//             VariantPriceWidget(variant, textStyle: textTheme.headlineSmall),
//             const Gap(4),
//             Flexible(
//               child: Text(
//                 variant.description ?? variant.name ?? '-',
//                 style: textTheme.bodyMedium?.copyWith(
//                   fontWeight: FontWeight.w500,
//                   color: Palette.blackSecondary,
//                 ),
//               ),
//             ),
//             const Gap(8),
//             buildDetailRow(
//               'Ships from',
//               variant.shipsFrom,
//               context,
//             ),
//             buildDetailRow('EAN #', variant.subUnit?.upc ?? '-', context),
//             buildDetailRow(
//               'Production Country',
//               variant.productCountry ?? '-',
//               context,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   'Incoterms',
//                   style: textTheme.bodyMedium?.copyWith(
//                     fontWeight: FontWeight.w400,
//                     color: Palette.blackSecondary,
//                   ),
//                 ),
//                 Container(
//                   padding:
//                       const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
//                   decoration: BoxDecoration(
//                     color: loading ? null : Palette.k0679FF,
//                     borderRadius: BorderRadius.circular(4),
//                   ),
//                   child: Text(
//                     incoterm ?? '-',
//                     style: const TextStyle(color: Colors.white, fontSize: 12),
//                   ),
//                 ),
//               ],
//             ),
//             const Gap(14),
//             ConstrainedBox(
//               constraints: BoxConstraints.loose(const Size(120, 120)),
//               child: VariantQuantityPicker(
//                   key: ValueKey(variant.variantSupplierId),
//                   variant,
//                   useEditingCart: false,
//                   ignoreQuantityLimits: ignoreQuantityLimits),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// Widget buildDetailRow(String label, String value, BuildContext context) {
//   final textTheme = Theme.of(context).textTheme;
//   return Padding(
//     padding: const EdgeInsets.symmetric(vertical: 4.0),
//     child: Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Text(
//           label,
//           style: textTheme.bodyMedium?.copyWith(
//             fontWeight: FontWeight.w400,
//             color: Palette.blackSecondary,
//           ),
//         ),
//         Text(
//           value,
//           style: textTheme.bodyMedium?.copyWith(
//             fontWeight: FontWeight.w500,
//           ),
//           overflow: TextOverflow.ellipsis,
//         ),
//       ],
//     ),
//   );
// }