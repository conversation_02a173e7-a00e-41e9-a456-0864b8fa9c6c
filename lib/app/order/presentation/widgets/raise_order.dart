import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';

import 'index.dart';

class RaiseOrderWidget extends ConsumerStatefulWidget {
  const RaiseOrderWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _RaiseOrderWidgetState();
}

class _RaiseOrderWidgetState extends ConsumerState<RaiseOrderWidget> {
  // final _noteController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;

    // final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;

    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 120),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Gap(80),
          // if (outlet?.isParentOutlet ?? false) ...[
          LocationPickerWidget(),
          Gap(72),
          // ],
          ProductSearchAutoCompleteWidget(OrderType.purchase),
          // const Gap(72),
          // Text(
          //   'Additional details',
          //   style: textTheme.titleMedium?.copyWith(
          //     fontWeight: FontWeight.w700,
          //   ),
          // ),
          // const Gap(8),
          // Text(
          //   'Enter an additional information you’d like the supplier to be aware of',
          //   style: textTheme.bodyMedium?.copyWith(
          //       fontWeight: FontWeight.w400, color: Palette.blackSecondary),
          // ),
          // const Gap(10),
          // Flexible(
          //   child: SizedBox(
          //     height: 96,
          //     child: TextField(
          //       controller: _noteController,
          //       minLines: null,
          //       maxLines: null,
          //       expands: true,
          //       onChanged: (value) {
          //         ref.read(cartProvider.notifier).setNote(value);
          //       },
          //       decoration: InputDecoration(
          //         border: OutlineInputBorder(
          //           borderSide: BorderSide(color: Palette.stroke),
          //           borderRadius: BorderRadius.circular(6),
          //         ),
          //         focusedBorder: OutlineInputBorder(
          //           borderSide: BorderSide(color: Palette.primary),
          //           borderRadius: BorderRadius.circular(6),
          //         ),
          //         hintText: 'Note to supplier (optional)',
          //         hintStyle: textTheme.bodySmall
          //             ?.copyWith(color: Palette.placeholder),
          //         contentPadding: const EdgeInsets.symmetric(horizontal: 8)
          //             .copyWith(top: 8),
          //       ),
          //       textAlign: TextAlign.start,
          //       textAlignVertical: TextAlignVertical.top,
          //       // cursorColor: Palette.strokePressed,
          //       cursorHeight: 16,
          //     ),
          //   ),
          // ),
          Gap(40),
        ],
      ),
    );
  }
}
