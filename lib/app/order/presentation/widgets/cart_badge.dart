import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class CartBadgeWidget extends ConsumerWidget {
  const CartBadgeWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    final cartState = ref.watch(cartProvider);

    return InkWell(
      onTap: () {
        // final sp = await SharedPreferences.getInstance();
        // final isSalesOrderPath =
        //     sp.getBool(StorageKeys.isSalesOrderPath) ?? false;

        // if (!context.mounted) return;

        // if (isSalesOrderPath) {
        //   context.pushNamed(kCreateSalesOrderRoute);
        // } else {
        context.pushNamed(kCreateOrderRoute);
        // }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          SvgPicture.asset(
            '$kSvgDir/order/cart_btn.svg',
          ),
          Positioned(
            top: 2,
            right: 0,
            child: Visibility(
              visible: cartState.uniqueCartItemsWithNonZeroCount.isNotEmpty,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 5),
                decoration: BoxDecoration(
                    color: HexColor('#081F24'),
                    // shape: BoxShape.circle,
                    borderRadius: BorderRadius.circular(8)),
                child: Text(
                  '${cartState.uniqueCartItemsWithNonZeroCount.length}',
                  style: textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
