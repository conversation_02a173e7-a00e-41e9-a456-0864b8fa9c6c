import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class OrderActionBarWidget extends StatelessWidget {
  final String? leftText;
  final VoidCallback? leftIconAction;
  final String? rightButton1Text;
  final VoidCallback? rightButton1Action;
  final bool? rightButton1Loading;
  final bool? rightButton1Disabled;
  final String? rightButton2Text;
  final VoidCallback? rightButton2Action;
  final bool? rightButton2Loading;
  final bool? rightButton2Disabled;
  final bool? isCloseIcon;

  const OrderActionBarWidget({
    super.key,
    this.leftText,
    this.leftIconAction,
    this.rightButton1Text,
    this.rightButton1Action,
    this.rightButton1Loading = false,
    this.rightButton1Disabled = false,
    this.rightButton2Text,
    this.rightButton2Action,
    this.rightButton2Loading = false,
    this.rightButton2Disabled = false,
    this.isCloseIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final svgPath = isCloseIcon! ? kCloseSvg : kSvgArrowBackIcon;
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(
        top: Radius.circular(16),
      ),
      child: Container(
        height: 70,
        padding:
            const EdgeInsets.only(left: 31, right: 36, top: 13, bottom: 13),
        decoration: BoxDecoration(
          color: Palette.kFCFCFC,
          // borderRadius: const BorderRadius.vertical(
          //   top: Radius.circular(16),
          // ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Row(
                children: [
                  Flexible(
                    child: Skeleton.keep(
                      keep: false,
                      child: IconButton(
                        icon: SvgPicture.asset(svgPath),
                        onPressed: leftIconAction,
                      ),
                    ),
                  ),
                  const Gap(10),
                  leftText != null
                      ? Text(
                          leftText!,
                          style: textTheme.bodyLarge
                              ?.copyWith(color: Palette.k6B797C),
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: rightButton1Text != null
                        ? ConstrainedBox(
                            constraints: const BoxConstraints(
                              minWidth: 116,
                              minHeight: 40,
                              maxHeight: 40,
                            ),
                            child: TextButton(
                              onPressed: (rightButton1Loading! ||
                                      rightButton1Disabled!)
                                  ? null
                                  : rightButton1Action,
                              style: TextButton.styleFrom(
                                elevation: 0,
                                foregroundColor: rightButton1Disabled!
                                    ? Palette.k6B797C
                                    : Palette.primary,
                                backgroundColor: rightButton1Disabled!
                                    ? Palette.k6B797C.withValues(alpha: 0.2)
                                    : Palette.primary.withValues(alpha: 0.2),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  Opacity(
                                    opacity: rightButton1Loading! ? 0 : 1,
                                    child: Text(rightButton1Text!),
                                  ),
                                  if (rightButton1Loading!)
                                    const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(),
                                    ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                  const Gap(10),
                  Flexible(
                    child: rightButton2Text != null
                        ? ConstrainedBox(
                            constraints: const BoxConstraints(
                              minWidth: 116,
                              minHeight: 40,
                              maxHeight: 40,
                            ),
                            child: ElevatedButton(
                              onPressed: (rightButton2Loading! ||
                                      rightButton2Disabled!)
                                  ? null
                                  : rightButton2Action,
                              style: ElevatedButton.styleFrom(
                                elevation: 0,
                                foregroundColor: Colors.white,
                                backgroundColor: rightButton2Disabled!
                                    ? Palette.k6B797C
                                    : Palette.primaryBlack,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  Opacity(
                                    opacity: rightButton2Loading! ? 0 : 1,
                                    child: Text(rightButton2Text!),
                                  ),
                                  if (rightButton2Loading!)
                                    const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
