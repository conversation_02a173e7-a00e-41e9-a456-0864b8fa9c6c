import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class LocationPickerWidget extends ConsumerStatefulWidget {
  const LocationPickerWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _LocationPickerWidgetState();
}

class _LocationPickerWidgetState extends ConsumerState<LocationPickerWidget>
    with SingleTickerProviderStateMixin {
  OverlayEntry? _overlayEntry;
  bool _showLocationDropdown = false;
  final _layerLink = LayerLink();

  late AnimationController _controller;
  late Animation<double> _opacity;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final selectedLocation = ref.watch(cartProvider).branch;
    final cartNotifier = ref.read(cartProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Delivery location',
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const Gap(8),
        if (selectedLocation != null) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Flexible(
                flex: 3,
                child: ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(
                    selectedLocation.outletBusinessName,
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text(
                    selectedLocation.streetName ?? '',
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
              Flexible(
                flex: 1,
                child: IconButton(
                  onPressed: () => cartNotifier.setBranch(null),
                  icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                ),
              )
            ],
          ),
        ] else
          GestureDetector(
            onTap: _toggleDropdown,
            child: CompositedTransformTarget(
              link: _layerLink,
              child: Container(
                height: 36,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  border: Border.all(color: Palette.stroke),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Select location',
                      style: textTheme.bodySmall
                          ?.copyWith(color: Palette.placeholder),
                    ),
                    SvgPicture.asset(
                      '$kSvgDir/order/chevron_down.svg',
                      color: Palette.primaryBlack,
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _removeDropdown();
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _opacity = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);

    Future.microtask(() {
      final branches = ref.read(branchesProvider);

      if (branches.length == 1) {
        ref.read(cartProvider.notifier).setBranch(branches.first);
      }
    });
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    final branches = ref.watch(branchesProvider);

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                _toggleDropdown();
              },
            ),
          ),
          Positioned(
            width: size.width,
            left: offset.dx,
            top: offset.dy + size.height + 5,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: const Offset(0, 38),
              showWhenUnlinked: false,
              child: FadeTransition(
                opacity: _opacity,
                child: Material(
                  elevation: 0,
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxHeight: 200,
                      minHeight: 50,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Palette.kE7E7E7),
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                          boxShadow: const [
                            BoxShadow(
                              color: Colors.black12,
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ListView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          itemCount: branches.length,
                          itemBuilder: (context, index) {
                            final location = branches[index];
                            return StatefulBuilder(
                              builder: (context, setState) {
                                return HoverableContainer(
                                  index: index,
                                  child: ListTile(
                                    title: Text(
                                      location.outletBusinessName,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                              fontWeight: FontWeight.w400),
                                    ),
                                    subtitle: Text(
                                      location.streetName ?? '',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                              color: Palette.blackSecondary),
                                    ),
                                    onTap: () {
                                      setState(() {
                                        ref
                                            .read(cartProvider.notifier)
                                            .setBranch(location);
                                        _toggleDropdown(false);
                                      });
                                    },
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeDropdown() {
    if (_overlayEntry != null) {
      _controller.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
        // Reset the animation to the initial state
        _controller.value = 0; // Reset animation
      });
    }
  }

  void _showDropdown() {
    final overlay = Overlay.of(context);
    if (_overlayEntry == null) {
      _overlayEntry = _createOverlayEntry();
      overlay.insert(_overlayEntry!);

      // Start the fade-in animation
      _controller.forward();
    }
  }

  void _toggleDropdown([bool? resetIndex = true]) {
    setState(() {
      _showLocationDropdown = !_showLocationDropdown;
    });

    if (_showLocationDropdown) {
      _showDropdown();
    } else {
      _removeDropdown();
    }
  }
}
