import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/data/models/payout.dart';
import 'package:td_procurement/app/invoices/data/repositories/invoices_repo.dart';
import 'package:td_procurement/app/invoices/domain/entities/invoices_params.dart';
import 'package:td_procurement/app/invoices/domain/entities/payout_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final invoicesUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<InvoiceStatement>>, InvoicesParams>(
  (ref, arg) => UseCase<InvoiceStatement>().call(
    () => ref.read(invoicesRepoProvider).fetchInvoicesStatement(arg),
  ),
);

final invoiceSummaryUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<InvoiceDetailResult>>, InvoiceDetailParams>(
  (ref, params) {
    return UseCase<InvoiceDetailResult>()
        .call(() => ref.read(invoicesRepoProvider).fetchInvoiceDetail(params));
  },
);

final payoutUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<PayoutResponse>>, PayoutParams>(
  (ref, arg) => UseCase<PayoutResponse>()
      .call(() => ref.read(invoicesRepoProvider).getPayoutData(arg)),
);

final payoutDetailsUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<Payout>>, String>(
  (ref, reference) => UseCase<Payout>()
      .call(() => ref.read(invoicesRepoProvider).getPayoutDetails(reference)),
);

final salesInvoicesUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<InvoiceStatement>>, InvoicesParams>(
  (ref, arg) => UseCase<InvoiceStatement>().call(
    () => ref.read(invoicesRepoProvider).getSalesInvoices(arg),
  ),
);

final downloadInvoiceUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<String>>, String>(
  (ref, arg) => UseCase<String>().call(
    () => ref.read(invoicesRepoProvider).downloadInvoice(arg),
  ),
);

final downloadPayoutInvoiceUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<String>>, PayoutInvoiceParams>(
  (ref, arg) => UseCase<String>().call(
    () => ref.read(invoicesRepoProvider).downloadPayoutInvoice(arg),
  ),
);

final downloadSalesInvoiceUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<String>>, String>(
  (ref, arg) => UseCase<String>().call(
    () => ref.read(invoicesRepoProvider).downloadSalesInvoice(arg),
  ),
);
