import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/data/models/payout.dart';
import 'package:td_procurement/app/invoices/data/sources/invoices_data_source.dart';
import 'package:td_procurement/app/invoices/domain/entities/invoices_params.dart';
import 'package:td_procurement/app/invoices/domain/entities/payout_params.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

class InvoicesRepoImplementation extends InvoicesRepo {
  final Ref _ref;

  InvoicesRepoImplementation(this._ref);

  late final InvoicesDataSource _dataSource = _ref.read(invoicesDataProvider);

  @override
  Future<ApiResponse<InvoiceStatement>> fetchInvoicesStatement(
      InvoicesParams params) {
    return dioInterceptor(
        () => _dataSource.fetchInvoiceStatement(params), _ref);
  }

  @override
  Future<ApiResponse<String>> downloadInvoice(String id) {
    return dioInterceptor(() => _dataSource.downloadInvoice(id), _ref);
  }

  @override
  Future<ApiResponse<String>> downloadPayoutInvoice(
      PayoutInvoiceParams params) {
    return dioInterceptor(
        () => _dataSource.downloadPayoutInvoice(params), _ref);
  }

  @override
  Future<ApiResponse<InvoiceStatement>> getSalesInvoices(
      InvoicesParams params) {
    return dioInterceptor(() => _dataSource.getSalesInvoices(params), _ref);
  }

  @override
  Future<ApiResponse<String>> downloadSalesInvoice(String orderId) {
    return dioInterceptor(
        () => _dataSource.downloadSalesInvoice(orderId), _ref);
  }

  @override
  Future<ApiResponse<PayoutResponse>> getPayoutData(PayoutParams params) {
    return dioInterceptor(() => _dataSource.getPayoutData(params), _ref);
  }

  @override
  Future<ApiResponse<InvoiceDetailResult>> fetchInvoiceDetail(InvoiceDetailParams params) {
    return dioInterceptor(() => _dataSource.fetchInvoiceDetail(params), _ref);
  }

  @override
  Future<ApiResponse<Payout>> getPayoutDetails(String reference) {
    return dioInterceptor(() => _dataSource.getPayoutDetails(reference), _ref);
  }
}

abstract class InvoicesRepo {
  Future<ApiResponse<InvoiceStatement>> fetchInvoicesStatement(
      InvoicesParams params);
  Future<ApiResponse<InvoiceDetailResult>> fetchInvoiceDetail(InvoiceDetailParams params);
  Future<ApiResponse<String>> downloadInvoice(String id);
  Future<ApiResponse<String>> downloadPayoutInvoice(PayoutInvoiceParams params);
  Future<ApiResponse<String>> downloadSalesInvoice(String orderId);
  Future<ApiResponse<InvoiceStatement>> getSalesInvoices(InvoicesParams params);
  Future<ApiResponse<PayoutResponse>> getPayoutData(PayoutParams params);
  Future<ApiResponse<Payout>> getPayoutDetails(String reference);
}

final invoicesRepoProvider = Provider<InvoicesRepo>((ref) {
  return InvoicesRepoImplementation(ref);
});
