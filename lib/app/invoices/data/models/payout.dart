// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/utils/index.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';

class PayoutResponse {
  final Data? data;

  PayoutResponse({this.data});

  factory PayoutResponse.fromJson(Map<String, dynamic> json) => PayoutResponse(
        data: json['data'] != null ? Data.fromJson(json['data']) : null,
      );
}

class Data {
  final Pagination? pagination;
  final List<Transaction>? transactions;
  final int? total;
  final int batch;
  final PayoutBankAccount? account;

  Data(
      {this.pagination,
      this.transactions,
      this.total,
      required this.batch,
      this.account});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
      pagination: json['pagination'] != null
          ? Pagination.fromJson(json['pagination'])
          : null,
      transactions: json['data'] != null
          ? (json['data'] as List)
              .map((item) => Transaction.fromJson(item))
              .toList()
          : null,
      total: json['total'],
      batch: json['page'],
      account: PayoutBankAccount.fromJson(json['bankAccount'] ?? {}));
}

class Pagination {
  final Next? next;

  Pagination({this.next});

  factory Pagination.fromJson(Map<String, dynamic> json) => Pagination(
        next: json['next'] != null ? Next.fromJson(json['next']) : null,
      );
}

class Next {
  final int? page;
  final int? perPage;

  Next({this.page, this.perPage});

  factory Next.fromJson(Map<String, dynamic> json) => Next(
        page: json['page'],
        perPage: json['per_page'],
      );
}

class Transaction {
  final String? type;
  final bool? settled;
  final List<dynamic>? settlementAccounts;
  final double? serviceCharge;
  final String? id;
  final String? agentId;
  final String? agentName;
  final String? accountId;
  final double? amount;
  final String? outletName;
  final String? outletId;
  final String? reference;
  final String? status;
  final String? createdAt;
  final String? paidAt;
  final String? paymentRef;
  final String? currencyIso;
  final List<Invoice>? invoices;

  Transaction({
    this.type,
    this.settled,
    this.settlementAccounts,
    this.serviceCharge,
    this.id,
    this.agentId,
    this.agentName,
    this.accountId,
    this.amount,
    this.outletName,
    this.outletId,
    this.reference,
    this.status,
    this.createdAt,
    this.paidAt,
    this.paymentRef,
    this.invoices,
    this.currencyIso,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) => Transaction(
      type: json['type'],
      settled: json['settled'],
      settlementAccounts: json['settlementAccounts'],
      serviceCharge: (json['serviceCharge'] as num?)?.toDouble(),
      id: json['_id'],
      agentId: json['agentId'],
      agentName: json['agentName'],
      accountId: json['accountId'],
      amount: (json['amount'] as num?)?.toDouble(),
      outletName: json['outletName'],
      outletId: json['outletId'],
      reference: json['reference'],
      status: json['status'],
      createdAt: json['createdAt'],
      paidAt: json['paidAt'],
      paymentRef: json['paymentRef'],
      invoices: json['invoices'] != null
          ? (json['invoices'] as List)
              .map((item) => Invoice.fromJson(item))
              .toList()
          : null,
      currencyIso: json['currency']?['iso']);
}

class PayoutBankAccount {
  final String accountNumber;
  final String accountName;
  final String bank;

  PayoutBankAccount({
    required this.accountNumber,
    required this.accountName,
    required this.bank,
  });

  // Factory constructor to create an instance from JSON
  factory PayoutBankAccount.fromJson(Map<String, dynamic> json) {
    return PayoutBankAccount(
      accountNumber: json['accountNumber'],
      accountName: json['accountName'],
      bank: json['bank'],
    );
  }

  // Method to convert an instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'accountNumber': accountNumber,
      'accountName': accountName,
      'bank': bank,
    };
  }
}

class Payout extends Equatable {
  final String id;
  final bool? settled;
  final num? serviceCharge;
  final bool? isLoan;
  final String? agentId;
  final String? agentName;
  final String? accountId;
  final num? amount;
  final String? outletName;
  final String? outletId;
  final String reference;
  final DateTime? createdAt;
  final DateTime? paidAt;
  final String? paymentRef;
  final BankAccount? bankAccount;
  final List<Invoice> invoices;
  final String? status;
  const Payout(
    this.id,
    this.settled,
    this.serviceCharge,
    this.isLoan,
    this.agentId,
    this.agentName,
    this.accountId,
    this.amount,
    this.outletName,
    this.outletId,
    this.reference,
    this.createdAt,
    this.paidAt,
    this.paymentRef,
    this.bankAccount,
    this.invoices,
    this.status,
  );

  factory Payout.defaultValue() => Payout(
        'id',
        false,
        0,
        false,
        'agentId',
        'agentName',
        'accountId',
        0,
        'outletName',
        'outletId',
        'reference',
        DateTime.now(),
        DateTime.now(),
        'paymentRef',
        null,
        List.filled(3, Invoice.loadValue()),
        'status',
      );

  Payout copyWith({
    String? id,
    bool? settled,
    num? serviceCharge,
    bool? isLoan,
    String? agentId,
    String? agentName,
    String? accountId,
    num? amount,
    String? outletName,
    String? outletId,
    String? reference,
    DateTime? createdAt,
    DateTime? paidAt,
    String? paymentRef,
    BankAccount? bankAccount,
    List<Invoice>? invoices,
    String? status,
  }) {
    return Payout(
      id ?? this.id,
      settled ?? this.settled,
      serviceCharge ?? this.serviceCharge,
      isLoan ?? this.isLoan,
      agentId ?? this.agentId,
      agentName ?? this.agentName,
      accountId ?? this.accountId,
      amount ?? this.amount,
      outletName ?? this.outletName,
      outletId ?? this.outletId,
      reference ?? this.reference,
      createdAt ?? this.createdAt,
      paidAt ?? this.paidAt,
      paymentRef ?? this.paymentRef,
      bankAccount ?? this.bankAccount,
      invoices ?? this.invoices,
      status ?? this.status,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'settled': settled,
      'serviceCharge': serviceCharge,
      'isLoan': isLoan,
      'agentId': agentId,
      'agentName': agentName,
      'accountId': accountId,
      'amount': amount,
      'outletName': outletName,
      'outletId': outletId,
      'reference': reference,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'paidAt': paidAt?.millisecondsSinceEpoch,
      'paymentRef': paymentRef,
      'bankAccount': bankAccount?.toJson(),
      'invoices': invoices.map((x) => x.toJson()).toList(),
      'status': status,
    };
  }

  factory Payout.fromMap(Map<String, dynamic> map) {
    return Payout(
      map['_id'] as String,
      map['settled'],
      map['serviceCharge'],
      map['isLoan'],
      map['agentId'],
      map['agentName'],
      map['accountId'],
      map['amount'],
      map['outletName'],
      map['outletId'],
      map['reference'] as String,
      parseDate(map['createdAt']),
      parseDate(map['paidAt']),
      map['paymentRef'],
      BankAccount.fromJson(map['bankAccount'] as Map<String, dynamic>),
      (map['invoices'] is! List || (map['invoices'] as List).isEmpty)
          ? []
          : List<Invoice>.from(
              (map['invoices'] as List).map<Invoice>(
                (x) => Invoice.fromJson(x as Map<String, dynamic>),
              ),
            ),
      map['status'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory Payout.fromJson(String source) =>
      Payout.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props => [
        id,
        settled,
        serviceCharge,
        isLoan,
        agentId,
        agentName,
        accountId,
        amount,
        outletName,
        outletId,
        reference,
        createdAt,
        paidAt,
        paymentRef,
        bankAccount,
        invoices,
        status,
      ];
}
