import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class PayoutStatusChip extends StatelessWidget {
  final String status;

  const PayoutStatusChip({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 22,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(34),
          color: getStatusColor(status).withOpacity(0.1),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
          child: Text(
            getStatusText(status),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                  color: getStatusColor(status),
                ),
          ),
        ),
      ),
    );
  }

  Color getStatusColor(String status) {
    switch (status) {
      case "success":
        return Palette.statusSuccess;
      case "completed":
        return Palette.statusSuccess;
      case "pending":
        return Palette.statusPending;
      case "failed":
        return Palette.statusPending;
      case "processing":
        return Palette.statusPending;
      case "in-progress":
        return Palette.statusPending;
      case "in progress":
        return Palette.statusPending;
      default:
        return Colors.transparent;
    }
  }

  String getStatusText(String status) {
    switch (status) {
      case "success":
        return "Sucess";
      case "completed":
        return "Success";
      case "pending":
        return "In Progress";
      case "failed":
        return "In Progress";
      case "in progress":
        return "In Progress";
      case "processing":
        return "In Progress";
      case "in-progress":
        return "In Progress";

      default:
        return "";
    }
  }
}
