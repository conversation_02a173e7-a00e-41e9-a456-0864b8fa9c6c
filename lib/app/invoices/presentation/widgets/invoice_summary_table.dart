import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoice_summary_row.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class InvoiceSummaryTable extends StatelessWidget {
  final Invoice invoice;

  const InvoiceSummaryTable({super.key, required this.invoice});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Table(
          columnWidths: const {
            0: FlexColumnWidth(4),
            1: FlexColumnWidth(1),
            2: FlexColumnWidth(1),
            3: FlexColumnWidth(1),
            4: FlexColumnWidth(1),
            5: FlexColumnWidth(1),
          },
          children: [
            TableRow(
              children: [
                buildTableContent('Description', context),
                buildTableContent('Qty', context),
                buildTableContent('Unit Price', context),
                buildTableContent('Discount', context),
                buildTableContent('Tax', context),
                buildTableContent('Amount', context),
              ],
              decoration: BoxDecoration(
                border: Border.symmetric(
                  horizontal: BorderSide(
                      color: Palette.kE7E7E7.withOpacity(0.5), width: 1),
                ),
              ),
            ),
            const TableRow(
              children: [
                SizedBox(height: 8),
                SizedBox(),
                SizedBox(),
                SizedBox(),
                SizedBox(),
                SizedBox(),
              ],
            ),
            // Table Items
            ...(invoice.items ?? []).map((item) {
              final price = (item.price ?? 0) + (item.promoDiscount ?? 0);
              final quantity = item.quantity ?? 0;
              final itemTotal = price * quantity - (item.discount ?? 0);

              return InvoiceSummaryItem(
                price: price,
                quantity: quantity,
                discount: item.discount ?? 0,
                total: itemTotal,
                name: item.name ?? '',
                tax: 0,
                currency: invoice.currency,
              );
            }).map(
              (item) => TableRow(
                children: [
                  buildTableContent(item.name, context, true),
                  buildTableContent(item.quantity.toString(), context),
                  buildTableContent(
                      CurrencyWidget.value(
                          context, item.currency.iso, item.price),
                      context),
                  buildTableContent(
                      CurrencyWidget.value(
                          context, item.currency.iso, item.discount),
                      context,
                      true),
                  buildTableContent(
                      CurrencyWidget.value(
                          context, item.currency.iso, item.tax),
                      context,
                      true),
                  buildTableContent(
                      CurrencyWidget.value(
                          context, item.currency.iso, item.total),
                      context,
                      true)
                ],
              ),
            ),
          ],
        ),
        const Gap(50),
        Padding(
          padding: const EdgeInsets.only(left: 200, right: 28),
          child: Column(
            children: [
              InvoiceSummaryRow(
                'Subtotal',
                CurrencyWidget.value(
                    context, invoice.currency.iso, invoice.subTotal),
                showBorder: false,
              ),
              const Gap(8),
              InvoiceSummaryRow(
                'Discount',
                CurrencyWidget.value(
                    context, invoice.currency.iso, invoice.shippingDiscount),
                showBorder: false,
              ),
              const Gap(8),
              InvoiceSummaryRow(
                'Taxes',
                CurrencyWidget.value(
                    context, invoice.currency.iso, invoice.taxes),
                showBorder: false,
              ),
              const Gap(8),
              InvoiceSummaryRow(
                'Shipping Costs',
                CurrencyWidget.value(
                    context, invoice.currency.iso, invoice.shippingCosts),
                showBorder: false,
              ),
              const Gap(8),
              InvoiceSummaryRow(
                'Paid Amount',
                CurrencyWidget.value(context, invoice.currency.iso,
                    invoice.total - invoice.amount),
                showBorder: false,
                useBoldFontWeight: true,
              ),
              if (invoice.status != InvoiceStatus.paid) ...[
                const Gap(8),
                InvoiceSummaryRow(
                    'Amount Due',
                    CurrencyWidget.value(
                        context, invoice.currency.iso, invoice.amount),
                    showBorder: false,
                    useBoldFontWeight: true),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget buildTableContent(String text, BuildContext context,
      [bool bold = false]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: bold ? null : Palette.blackSecondary,
              fontWeight: bold ? FontWeight.w500 : FontWeight.w400,
            ),
      ),
    );
  }
}

class InvoiceSummaryItem {
  final String name;
  final num price;
  final num discount;
  final num tax;
  final num total;
  final num quantity;
  final CurrencyData currency;

  InvoiceSummaryItem(
      {required this.name,
      required this.price,
      required this.discount,
      required this.tax,
      required this.total,
      required this.quantity,
      required this.currency});
}
