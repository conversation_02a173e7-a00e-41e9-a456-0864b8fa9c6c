import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/presentation/screens/invoices.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoice_status_chip.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/empty_widget.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class InvoicesTable extends ConsumerStatefulWidget {
  final InvoiceType type;
  const InvoicesTable(this.type, {super.key});

  @override
  ConsumerState<InvoicesTable> createState() => _InvoicesTableState();
}

class _InvoicesTableState extends ConsumerState<InvoicesTable> {
  @override
  Widget build(BuildContext context) {
    final invoicesState = ref.watch(
      widget.type.controller(
        ref.watch(widget.type.argument),
      ),
    );
    return invoicesState.when(
      skipLoadingOnRefresh: false,
      data: (data) {
        if (data.invoices.isEmpty) {
          return CustomScrollView(
            slivers: [
              SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyWidget(
                    icon: kReceiptSvg,
                    routeName: switch (widget.type) {
                      InvoiceType.purchase => kCreateOrderRoute,
                      InvoiceType.sales => kCreateSalesOrderRoute,
                    },
                    title: 'No ${widget.type.title}s',
                    routeInfo: 'Create a new one →',
                    subTitle:
                        'There are no invoices matching your filter criteria'),
              ),
            ],
          );
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: _TableContent(widget.type, invoices: data.invoices),
              ),
            ),
            const Gap(10),
            InvoicesPagination(
              total: data.totalCount,
              totalPages: data.pagination.totalPages,
              invoiceType: widget.type,
            ),
            const Gap(10),
          ],
        );
      },
      error: (error, __) => FailureWidget(
        e: error,
        retry: () => ref.invalidate(widget.type.controller),
      ),
      loading: () => TableLoadingView(type: widget.type),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }
}

class TableLoadingView extends StatelessWidget {
  final InvoiceType type;
  const TableLoadingView({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    final defaultLoadInvoices = Invoice.loadValue();
    return Skeletonizer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: _TableContent(
                type,
                invoices: List.filled(20, defaultLoadInvoices),
              ),
            ),
          ),
          const Gap(10),
          InvoicesPagination(invoiceType: type),
          const Gap(10),
        ],
      ),
    );
  }
}

/*class _TableHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return SizedBox(
      height: 36,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Palette.kF7F7F7,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Table(
            columnWidths: const {
              0: FlexColumnWidth(2),
              1: FlexColumnWidth(2),
              2: FlexColumnWidth(3),
              3: FlexColumnWidth(2),
              4: FlexColumnWidth(2),
              5: FlexColumnWidth(3),
            },
            children: [
              TableRow(
                children: [
                  _buildHeaderCell('Amount', textTheme),
                  _buildHeaderCell('Invoice Number', textTheme),
                  _buildHeaderCell('Bill To', textTheme),
                  _buildHeaderCell('Created on', textTheme),
                  _buildHeaderCell('Due by', textTheme),
                  const SizedBox.shrink(), // Empty space for floating icon
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }
}*/

class _TableContent extends ConsumerStatefulWidget {
  final List<Invoice> invoices;
  final InvoiceType type;

  const _TableContent(this.type, {required this.invoices});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _TableContentState();
  }
}

const kInvoiceTableMinSize = 1210;

class _TableContentState extends ConsumerState<_TableContent> {
  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return LayoutBuilder(
      builder: (_, constraints) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Palette.kE7E7E7,
            ),
          ),
          child: DataTable(
            showCheckboxColumn: false,
            headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
            columns: [
              DataColumn(
                label: _buildHeaderCell('Invoice number', textTheme),
              ),
              DataColumn(
                label: _buildHeaderCell('Amount due', textTheme),
              ),
              DataColumn(
                label: _buildHeaderCell('Bill to', textTheme),
              ),
              DataColumn(
                label: _buildHeaderCell('Created on', textTheme),
              ),
              DataColumn(
                label: _buildHeaderCell('Due by', textTheme),
              ),
              DataColumn(
                label: ConstrainedBox(
                  constraints: BoxConstraints(
                      maxWidth: constraints.maxWidth > kInvoiceTableMinSize
                          ? double.infinity
                          : 50),
                  child: const SizedBox.shrink(),
                ),
              )
            ],
            rows: widget.invoices
                .map(
                  (element) => DataRow(
                    onSelectChanged: (isSelected) {
                      if (isSelected ?? false) {
                        InvoiceOption.download.action(
                          context,
                          element,
                          widget.type,
                          element.orderIds.first,
                          false,
                        );
                      }
                    },
                    cells: [
                      DataCell(
                        _buildContentText(element.invoiceNumber, textTheme),
                      ),
                      DataCell(
                        _buildContentAmount(
                            element.total, textTheme, element.currency.iso),
                      ),
                      DataCell(
                        _buildContentText(
                            element.outletBusinessName, textTheme),
                      ),
                      DataCell(
                        _buildContentText(
                            element.createdAt.toDate(), textTheme),
                      ),
                      DataCell(
                        _buildContentBadge(
                            element.dueAt.toDate(), element.status, textTheme),
                      ),
                      /*   DataCell(
                      (hoverState == element.invoiceNumber
                          ? child!
                          : const SizedBox.shrink()),
                    )*/
                      DataCell(
                        // constraints.maxWidth > kInvoiceTableMinSize
                        //     ? Row(
                        //         mainAxisAlignment: MainAxisAlignment.end,
                        //         children: [
                        //           FilledButton.tonal(
                        //             onPressed: () =>
                        //                 InvoiceOption.download.action(
                        //               context,
                        //               element,
                        //               widget.type,
                        //               element.orderIds.first,
                        //               true,
                        //             ),
                        //             style: FilledButton.styleFrom(
                        //                 minimumSize: const Size(50, 40),
                        //                 fixedSize: const Size(140, 35),
                        //                 textStyle: textTheme.bodyMedium
                        //                     ?.copyWith(
                        //                         fontWeight: FontWeight.w500),
                        //                 backgroundColor:
                        //                     Palette.primary.withOpacity(0.1),
                        //                 foregroundColor: Palette.primaryBlack),
                        //             child: const Text('Download invoice'),
                        //           ),
                        //           const Gap(5),
                        //           OutlinedButton(
                        //             onPressed: () => InvoiceOption.view.action(
                        //               context,
                        //               element,
                        //               widget.type,
                        //               element.orderIds.first,
                        //             ),
                        //             style: OutlinedButton.styleFrom(
                        //               textStyle: textTheme.bodyMedium?.copyWith(
                        //                   fontWeight: FontWeight.w500),
                        //               backgroundColor: Colors.white,
                        //               side: BorderSide(color: Palette.stroke),
                        //               foregroundColor: Palette.primaryBlack,
                        //               minimumSize: const Size(50, 40),
                        //               fixedSize: const Size(130, 35),
                        //               shape: RoundedRectangleBorder(
                        //                 borderRadius: BorderRadius.circular(8),
                        //               ),
                        //             ),
                        //             child: const Text('View order'),
                        //           )
                        //         ],
                        //       )
                        //     :

                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                              padding: const EdgeInsets.only(right: 15),
                              child: PopupMenuButton(
                                useRootNavigator: true,
                                icon: const Icon(
                                  Icons.more_vert,
                                  color: Colors.black,
                                ),
                                onSelected: (value) => {
                                  if (value == 'download')
                                    {
                                      InvoiceOption.download.action(
                                          context,
                                          element,
                                          widget.type,
                                          element.orderIds.first,
                                          true),
                                    }
                                  else if (value == 'view')
                                    {
                                      InvoiceOption.view.action(
                                        context,
                                        element,
                                        widget.type,
                                        element.orderIds.first,
                                      ),
                                    }
                                  else if (value == 'copy')
                                    {
                                      Clipboard.setData(ClipboardData(
                                          text: element.invoiceNumber)),
                                      Toast.show(
                                          "Invoice number copied to clipboard",
                                          context,
                                          duration: 2,
                                          title: ''),
                                    }
                                },
                                itemBuilder: (context) => [
                                  const PopupMenuItem(
                                    value: 'copy',
                                    child: ListTile(
                                      leading: Icon(Icons.copy_outlined),
                                      title: Text(
                                        'Copy Invoice Number',
                                        style: TextStyle(color: Colors.black),
                                      ),
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'download',
                                    child: ListTile(
                                      leading: Icon(Icons.info_outline),
                                      title: Text(
                                        'Download invoice',
                                        style: TextStyle(color: Colors.black),
                                      ),
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'view',
                                    child: ListTile(
                                      leading: Icon(Icons.info_outline),
                                      title: Text(
                                        'View Order Details',
                                        style: TextStyle(color: Colors.black),
                                      ),
                                    ),
                                  ),
                                ],
                                color: Colors.white,
                                tooltip: '',
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: EdgeInsets.zero,
                                menuPadding: EdgeInsets.zero,
                                enableFeedback: false,
                              )),
                        ),
                      )
                    ],
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme, String currency) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          CurrencyWidget(
            amount,
            currency,
            amountStyle: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const Gap(5),
          Text(
            currency,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentText(String text, TextTheme textTheme) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
            child: Text(
              text,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentBadge(
      String date, InvoiceStatus status, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Row(
        children: [
          Text(
            date,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
          const Gap(3),
          InvoiceStatusChip(status: status)
        ],
      ),
    );
  }
}

class InvoicesPagination extends ConsumerWidget {
  final InvoiceType invoiceType;
  final int? total;
  final int? totalPages;
  const InvoicesPagination(
      {super.key, this.total, this.totalPages, required this.invoiceType});

  @override
  Widget build(BuildContext context, ref) {
    final params = ref.watch(invoiceType.argument);
    final int batches = totalPages ??
        ((ref.read(invoiceType.controller(params)).valueOrNull?.totalCount ??
                    total ??
                    0) /
                params.limit)
            .ceil();
    final start = ((params.batch - 1) * params.limit) + 1;
    final end = params.batch * params.limit;
    final controllerTotalCount =
        ref.read(invoiceType.controller(params)).valueOrNull?.totalCount;
    final totalCount =
        (controllerTotalCount == null || controllerTotalCount == 0)
            ? (batches * params.limit)
            : controllerTotalCount;

    return Padding(
      padding: const EdgeInsets.only(left: 36, right: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$start - ${end > totalCount ? totalCount : end} of $totalCount',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: Palette.blackSecondary),
          ),
          if (batches > 0)
            IntrinsicWidth(
              child: NumberPagination(
                onPageChanged: (int pageNumber) {
                  ref.read(invoiceType.argument.notifier).state =
                      params.paginate(pageNumber);
                },
                visiblePagesCount: batches > 5 || batches == 0 ? 5 : batches,
                buttonElevation: 0.3,
                totalPages: batches,
                currentPage: params.batch,
                buttonRadius: 8,
                selectedButtonColor: Palette.primaryBlack,
                selectedNumberColor: Colors.white,
                unSelectedButtonColor: Colors.white,
                unSelectedNumberColor: Palette.blackSecondary,
                fontSize: 14,
                firstPageIcon: SvgPicture.asset(
                  kDoubleChevronLeftSvg,
                  width: 30,
                  height: 30,
                ),
                previousPageIcon: SvgPicture.asset(
                  kChevronLeftSvg,
                  colorFilter:
                      ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
                  width: 12,
                  height: 12,
                ),
                lastPageIcon: SvgPicture.asset(
                  kDoubleChevronRightSvg,
                  width: 30,
                  height: 30,
                ),
                nextPageIcon: SvgPicture.asset(
                  kChevronRightSvg,
                  colorFilter:
                      ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
                  width: 12,
                  height: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

enum InvoiceOption {
  download('Download invoice'),
  view('View order');

  const InvoiceOption(this.label);
  final String label;

  action(BuildContext ctx, Invoice invoice, InvoiceType type, String orderId,
      [bool isDownload = false]) {
    switch (this) {
      case InvoiceOption.download:
        final route = type == InvoiceType.purchase
            ? kInvoiceDetailsRoute
            : kSalesInvoiceSummaryRoute;

        ctx.goNamed(
          route,
          pathParameters: {'id': orderId},
        );
      case InvoiceOption.view:
        switch (type) {
          case InvoiceType.purchase:
            ctx.goNamed(kOrderSummaryRoute,
                pathParameters: {'id': invoice.paymentReference ?? ''},
                extra: invoice.transaction);
          case InvoiceType.sales:
            ctx.goNamed(kSalesOrderSummaryRoute,
                pathParameters: {'id': invoice.orderIds.first});
        }
    }
  }
}
