import 'package:collection/collection.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/data/models/payout.dart';
import 'package:td_procurement/app/invoices/presentation/screens/invoices.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/payout_status_chip.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

const minWidth = 1500;

class PayoutTable extends ConsumerStatefulWidget {
  const PayoutTable(this.payoutList, this.details, this.showStatus,
      {super.key});

  final List<Transaction> payoutList;
  final PayoutBankAccount details;
  final bool showStatus;
  @override
  ConsumerState<PayoutTable> createState() => _PayoutTableState();
}

class _PayoutTableState extends ConsumerState<PayoutTable> {
  @override
  Widget build(BuildContext context) {
    return _TableContent(
      transactions: widget.payoutList,
      details: widget.details,
      showStatus: widget.showStatus,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }
}

class _TableContent extends ConsumerStatefulWidget {
  final List<Transaction> transactions;
  final PayoutBankAccount details;
  final bool showStatus;

  const _TableContent(
      {required this.transactions,
      required this.details,
      required this.showStatus});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _TableContentState();
  }
}

class _TableContentState extends ConsumerState<_TableContent> {
  final ScrollController _scrollController = ScrollController();

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  String getLastFiveDigits(String input) {
    if (input.length <= 5) {
      return input;
    }
    return input.substring(input.length - 5);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Palette.kE7E7E7,
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final tableWidth =
              constraints.maxWidth > minWidth ? constraints.maxWidth : minWidth;
          return SizedBox(
              width: tableWidth.toDouble(),
              height: MediaQuery.of(context).size.height,
              child: DataTable2(
                  columnSpacing: 0,
                  minWidth: tableWidth.toDouble(),
                  headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
                  showCheckboxColumn: false,
                  columns: [
                    DataColumn2(
                      label: _buildHeaderCell('Payout ID', textTheme),
                    ),
                    DataColumn2(
                      label: _buildHeaderCell('Amount sent', textTheme),
                    ),
                    DataColumn2(
                      label: _buildHeaderCell('Linked invoice(s)', textTheme),
                    ),
                    DataColumn2(
                      label: _buildHeaderCell('Customer Name', textTheme),
                    ),
                    DataColumn2(
                      label: _buildHeaderCell('Receiving bank', textTheme),
                    ),
                    DataColumn2(
                      label: _buildHeaderCell('Fees', textTheme),
                    ),
                    DataColumn2(
                      label: _buildHeaderCell('Arrive by', textTheme),
                    ),
                    const DataColumn2(
                      label: SizedBox.shrink(),
                    )
                  ],
                  rows: widget.transactions
                      .map((element) => DataRow(
                            onSelectChanged: (isSelected) {
                              if (isSelected ?? false) {
                                context.goNamed(kPayoutSummaryRoute,
                                    pathParameters: {
                                      'id': element.reference ?? ''
                                    });
                                // showCustomGeneralDialog(
                                //   context,
                                //   dismissible: true,
                                //   percentage: 0.45,
                                //   child: PayoutSummary(element, widget.details),
                                // );
                              }
                            },
                            cells: [
                              DataCell(
                                _buildReferenceText(
                                    element.reference ?? '', textTheme, false),
                              ),
                              DataCell(
                                _buildContentAmount(element.amount ?? 0,
                                    textTheme, element.currencyIso),
                              ),
                              DataCell(
                                _buildContentTextList(
                                    textTheme,
                                    element.invoices != null
                                        ? element.invoices!
                                            .map((e) => e.invoiceNumber)
                                            .toList()
                                        : []),
                              ),
                              DataCell(
                                _buildContentText(
                                  element.outletName ?? '',
                                  textTheme,
                                ),
                              ),
                              DataCell(
                                _buildContentBankCell(
                                    widget.details.bank,
                                    getLastFiveDigits(
                                        widget.details.accountNumber),
                                    textTheme),
                              ),
                              DataCell(
                                _buildContentAmount(element.serviceCharge ?? 0,
                                    textTheme, element.currencyIso),
                              ),
                              DataCell(
                                element.status != null &&
                                        element.createdAt != null
                                    ? _buildContentBadge(
                                        DateTime.parse(element.createdAt!),
                                        element.status == null
                                            ? ''
                                            : element.status!.toLowerCase(),
                                        textTheme)
                                    : const SizedBox.shrink(),
                              ),
                              DataCell(
                                _buildMoreOptionsButton(element),
                              )
                            ],
                          ))
                      .toList()));
        },
      ),
    );
  }

  Widget _buildMoreOptionsButton(Transaction transaction) {
    return Align(
      alignment: Alignment.centerRight,
      child: PopupMenuButton<String>(
        useRootNavigator: true,
        icon: const Icon(Icons.more_vert, color: Colors.black),
        onSelected: (value) => _handleMenuSelection(value, transaction),
        itemBuilder: (context) => _buildMenuItems(transaction),
        color: Colors.white,
        tooltip: '',
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: EdgeInsets.zero,
        menuPadding: EdgeInsets.zero,
        enableFeedback: false,
      ),
    );
  }

  void _handleMenuSelection(String value, Transaction transaction) {
    if (value == 'copy') {
      Clipboard.setData(ClipboardData(text: transaction.reference.toString()));
      Toast.show("Payout ID copied to clipboard", context,
          duration: 2, title: '');
    }
  }

  List<PopupMenuItem<String>> _buildMenuItems(Transaction transaction) {
    final items = <PopupMenuItem<String>>[
      const PopupMenuItem(
        value: 'copy',
        child: ListTile(
          leading: Icon(Icons.copy_outlined),
          title: Text(
            'Copy Payout ID',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
    ];

    return items;
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme, String? iso) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Wrap(
        spacing: 5,
        children: [
          CurrencyWidget(
            amount,
            iso ?? 'NGN',
            amountStyle: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            iso ?? 'NGN',
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentText(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(
          color: Palette.blackSecondary,
        ),
      ),
    );
  }

  Widget _buildReferenceText(
      String text, TextTheme textTheme, bool shouldExpandCell) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: SizedBox(
          width: shouldExpandCell ? null : 150,
          child: Text(
            text,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          )),
    );
  }

  Widget _buildContentBankCell(
      String bank, String bankNumber, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Text(
            bank,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: CircleAvatar(
              backgroundColor: Palette.orangePrimaryDark,
              radius: 2,
            ),
          ),
          Text(
            bankNumber,
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: Palette.blackSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildContentTextList(TextTheme textTheme, List invoiceList) {
    final length = invoiceList.length > 2 ? 2 : invoiceList.length;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Wrap(
        children: [
          ...invoiceList.mapIndexed(
            (i, e) => Text(
              '$e${++i < length ? ',' : ''} ',
              style: textTheme.bodyMedium?.copyWith(
                decoration: TextDecoration.underline,
                color: Palette.blackSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (length > 2) ...[
            const Gap(2),
            Text(
              '+${invoiceList.length - 2}',
              style: textTheme.bodyMedium?.copyWith(
                decoration: TextDecoration.underline,
                color: Palette.blackSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ]
        ],
      ),
    );
  }

  Widget _buildContentBadge(DateTime date, String status, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Wrap(
        spacing: 3,
        children: [
          Text(
            date.toDayMonth(),
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          const Gap(3),
          if (widget.showStatus) PayoutStatusChip(status: status)
        ],
      ),
    );
  }
}

class Pagination extends ConsumerWidget {
  final InvoiceType invoiceType;
  final int? total;
  final int? totalPages;
  const Pagination(
      {super.key, this.total, this.totalPages, required this.invoiceType});

  @override
  Widget build(BuildContext context, ref) {
    final params = ref.watch(invoiceType.argument);
    final int batches = totalPages ??
        ((ref.read(invoiceType.controller(params)).valueOrNull?.totalCount ??
                    total ??
                    0) /
                params.limit)
            .ceil();
    return SizedBox(
      width: 500,
      child: NumberPagination(
        onPageChanged: (int pageNumber) {
          ref.read(invoiceType.argument.notifier).state =
              params.paginate(pageNumber);
        },
        visiblePagesCount: batches > 5 || batches == 0 ? 5 : batches,
        buttonElevation: 0.3,
        totalPages: batches,
        currentPage: params.batch,
        buttonRadius: 8,
        selectedButtonColor: Palette.primaryBlack,
        selectedNumberColor: Colors.white,
        unSelectedButtonColor: Colors.white,
        unSelectedNumberColor: Palette.blackSecondary,
        fontSize: 14,
        firstPageIcon: SvgPicture.asset(
          kDoubleChevronLeftSvg,
          width: 30,
          height: 30,
        ),
        previousPageIcon: SvgPicture.asset(
          kChevronLeftSvg,
          colorFilter:
              ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
          width: 12,
          height: 12,
        ),
        lastPageIcon: SvgPicture.asset(
          kDoubleChevronRightSvg,
          width: 30,
          height: 30,
        ),
        nextPageIcon: SvgPicture.asset(
          kChevronRightSvg,
          colorFilter:
              ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
          width: 12,
          height: 12,
        ),
      ),
    );
  }
}
