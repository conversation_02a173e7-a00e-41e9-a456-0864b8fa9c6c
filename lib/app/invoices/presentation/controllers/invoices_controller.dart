import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/domain/entities/invoices_params.dart';
import 'package:td_procurement/app/invoices/domain/use_cases/invoices_use_cases.dart';

class InvoicesController
    extends AutoDisposeFamilyAsyncNotifier<InvoiceStatement, InvoicesParams> {
  @override
  FutureOr<InvoiceStatement> build(arg) async {
    return (await ref.read(
      invoicesUseCaseProvider(arg),
    ))
        .extract();
  }
}

final invoicesControllerProvider = AutoDisposeAsyncNotifierProviderFamily<
    InvoicesController, InvoiceStatement, InvoicesParams>(() {
  return InvoicesController();
});

final invoicesArgProvider = AutoDisposeStateProvider<InvoicesParams>(
  (_) => InvoicesParams(type: InvoiceType.purchase),
);

final homeInvoicesArgProvider = StateProvider<InvoicesParams>(
  (_) => InvoicesParams(status: InvoiceStatus.unpaid,type: InvoiceType.purchase),
);
