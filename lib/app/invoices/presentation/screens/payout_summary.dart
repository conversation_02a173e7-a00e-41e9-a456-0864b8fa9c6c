import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/data/models/payout.dart';
import 'package:td_procurement/app/invoices/domain/entities/payout_params.dart';
import 'package:td_procurement/app/invoices/domain/use_cases/invoices_use_cases.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoice_summary.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoices_table.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class PayoutSummaryScreen extends ConsumerStatefulWidget {
  const PayoutSummaryScreen(this.reference, {super.key});
  final String reference;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _PayoutSummaryScreen();
  }
}

class _PayoutSummaryScreen extends ConsumerState<PayoutSummaryScreen> {
  final isFetchingInvoicePdf = ValueNotifier<bool>(false);
  late final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  final ValueNotifier<PayoutInfoStatus> _selectedOption =
      ValueNotifier(PayoutInfoStatus.invoices);
  int? selectedItem;
  late AsyncValue<Payout> payoutResponse;

  @override
  void initState() {
    super.initState();
    payoutResponse = const AsyncValue.loading();
    Future.microtask(() => fetchPayoutDetails());
  }

  @override
  dispose() {
    super.dispose();
  }

  Future<void> fetchPayoutDetails() async {
    if (mounted) {
      setState(() {
        payoutResponse = const AsyncValue.loading();
      });
    } else {
      payoutResponse = const AsyncValue.loading();
    }

    try {
      final res =
          await ref.read(payoutDetailsUseCaseProvider(widget.reference));

      setState(() {
        res.when(
          success: (payout) {
            payoutResponse = AsyncValue.data(payout);
          },
          failure: (error, _) {
            payoutResponse = AsyncValue.error(error, StackTrace.current);
          },
        );
      });
    } catch (error, stackTrace) {
      setState(() {
        payoutResponse = AsyncValue.error(error, stackTrace);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(
        top: Radius.circular(16),
      ),
      child: payoutResponse.when(
        data: (data) => _dataWidget(data),
        error: (error, stackTrace) =>
            FailureWidget(e: error, retry: fetchPayoutDetails),
        loading: () => Skeletonizer(
          enabled: true,
          child: _dataWidget(Payout.defaultValue()),
        ),
      ),
    );
  }

  Widget _dataWidget(Payout payout) {
    final textTheme = Theme.of(context).textTheme;
    return SingleChildScrollView(
        child: ValueListenableBuilder<PayoutInfoStatus>(
      valueListenable: _selectedOption,
      builder: (context, state, _) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 70,
            width: double.infinity,
            child: DecoratedBox(
              decoration: BoxDecoration(color: Palette.kFCFCFC),
              child: Row(
                children: [
                  const Gap(20),
                  Skeleton.replace(
                    child: IconButton(
                      icon: SvgPicture.asset(kSvgArrowBackIcon),
                      onPressed: () => context.pop(),
                    ),
                  ),
                  const Gap(10),
                  Row(
                    children: [
                      Text(
                        'Payouts',
                        style: textTheme.bodyLarge
                            ?.copyWith(color: Palette.k6B797C),
                      ),
                      const Gap(10),
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 8,
                      ),
                      const Gap(10),
                      Text(
                        payout.reference,
                        style: textTheme.bodyLarge
                            ?.copyWith(color: Palette.k6B797C),
                      ),
                    ],
                  ),
                  const Spacer(),
                  const Gap(30)
                ],
              ),
            ),
          ),
          const Gap(20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Text(
                  payout.reference,
                  style: textTheme.headlineMedium,
                ),
              ),
              const Gap(30),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildContentText('AMOUNT SENT', textTheme),
                        _buildContentAmount(
                          payout.amount ?? 0,
                          textTheme,
                        )
                      ],
                    ),
                    const Gap(60),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildContentText('RECEIVING BANK', textTheme),
                        _buildContentBankCell(
                            payout.bankAccount?.bankName ?? '',
                            payout.bankAccount?.accountNumber ?? '',
                            textTheme)
                      ],
                    ),
                    const Gap(60),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildContentText('FEES', textTheme),
                        _buildContentAmount(
                          payout.serviceCharge ?? 0,
                          textTheme,
                        )
                      ],
                    ),
                    const Gap(60),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildContentText('STATUS', textTheme),
                        _buildContentText(
                            getStatusText(payout.status ?? ''), textTheme),
                      ],
                    ),
                  ],
                ),
              ),
              const Gap(20),
              Divider(color: Colors.grey.withValues(alpha: 0.3)),
              const Gap(20),
              Container(
                  margin: const EdgeInsets.symmetric(horizontal: 30),
                  decoration: BoxDecoration(
                      border: Border.all(color: Palette.kE7E7E7),
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: const [
                        BoxShadow(
                            offset: Offset(0, 4),
                            blurRadius: 4,
                            spreadRadius: 0,
                            color: Palette.k0000000A)
                      ]),
                  child: invoiceTab(payout.invoices, textTheme))
            ],
          ),
        ],
      ),
    ));
  }

  Widget customerTab(TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Customers',
            style: textTheme.bodyLarge?.copyWith(
                color: Palette.orangePrimaryDark, fontWeight: FontWeight.w600),
          ),
        ),
        const Gap(20),
        Row(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return DataTable(
                    showCheckboxColumn: false,
                    headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
                    columns: [
                      DataColumn(
                        label: _buildHeaderCell('Name', textTheme),
                      ),
                      DataColumn(
                        label: _buildHeaderCell('Invoices', textTheme),
                      ),
                    ],
                    rows: [kTestInvoiceData]
                        .map(
                          (element) => DataRow(
                            onSelectChanged: (isSelected) {},
                            cells: [
                              DataCell(
                                _buildContentText(
                                    element.invoiceNumber.toString(),
                                    textTheme,
                                    true),
                              ),
                              DataCell(_buildContentTextList(
                                  'text', textTheme, ['54554554', '656567'])),
                            ],
                          ),
                        )
                        .toList(),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget invoiceTab(List<Invoice> invoices, TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Invoices',
            style: textTheme.bodyLarge?.copyWith(
                color: Palette.orangePrimaryDark, fontWeight: FontWeight.w600),
          ),
        ),
        const Gap(20),
        Row(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return DataTable(
                      showCheckboxColumn: false,
                      headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
                      columns: [
                        DataColumn(
                          label: _buildHeaderCell('Invoice Number', textTheme),
                        ),
                        DataColumn(
                          label: _buildHeaderCell('Invoice Date', textTheme),
                        ),
                        DataColumn(
                          label: _buildHeaderCell('Amount paid out', textTheme),
                        ),
                        DataColumn(
                          label: ConstrainedBox(
                            constraints: BoxConstraints(
                                maxWidth:
                                    constraints.maxWidth > kInvoiceTableMinSize
                                        ? double.infinity
                                        : 50),
                            child: const SizedBox.shrink(),
                          ),
                        )
                      ],
                      rows: List.generate(invoices.length, (index) {
                        return DataRow(
                          onSelectChanged: (isSelected) {},
                          cells: [
                            DataCell(invoices.isNotEmpty
                                ? InkWell(
                                    onTap: () {
                                      final invoice = invoices[index];
                                      showCustomGeneralDialog(
                                        context,
                                        dismissible: true,
                                        percentage: 0.45,
                                        child: InvoiceSummary(
                                          invoice.invoiceNumber,
                                          isCloseIcon: true,
                                          args: InvoiceSummaryArgs(
                                            type: invoice.invoiceType,
                                            invoice: invoice,
                                            payout: true,
                                          ),
                                        ),
                                      );
                                      // context.goNamed(
                                      //   kInvoiceSummaryRoute,
                                      //   // pathParameters: {
                                      //   //   'id': widget
                                      //   //           .transaction
                                      //   //           .invoices?[index]
                                      //   //           .orders
                                      //   //           ?.first
                                      //   //           .id ??
                                      //   //       ''
                                      //   // },
                                      //   extra: InvoiceSummaryArgs(
                                      //     invoice: widget
                                      //         .transaction.invoices![index],
                                      //     payout: true,
                                      //   ),
                                      // );
                                    },
                                    child: Text(
                                      invoices[index].invoiceNumber,
                                      style: textTheme.bodyMedium?.copyWith(
                                          fontSize: 12,
                                          color: Palette.strokePressed,
                                          decorationColor:
                                              Palette.strokePressed,
                                          decoration: TextDecoration.underline),
                                    ),
                                  )
                                : const SizedBox.shrink()),
                            DataCell(
                              invoices.isNotEmpty
                                  ? _buildContentText(
                                      invoices[index].createdAt.toDate(),
                                      textTheme)
                                  : const SizedBox.shrink(),
                            ),
                            DataCell(
                              invoices.isNotEmpty
                                  ? _buildContentAmount(
                                      invoices[index].total, textTheme)
                                  : const SizedBox.shrink(),
                            ),
                            DataCell(ValueListenableBuilder<bool>(
                              valueListenable: isFetchingInvoicePdf,
                              builder: (ctx, state, _) {
                                return state && selectedItem == index
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator())
                                    : InkWell(
                                        onTap: () {
                                          selectedItem = index;
                                          handleInvoiceDownload(
                                              invoices[index]);
                                        },
                                        child: SvgPicture.asset(kDownloadSvg));
                              },
                            ))
                          ],
                        );
                      }));
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContentTextList(
      String text, TextTheme textTheme, List invoiceList) {
    List.generate(100, (e) {});
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
              child: invoiceList.length <= 2
                  ? Row(
                      children: invoiceList
                          .map((e) => Text(
                                '$e, ',
                                style: textTheme.bodyMedium?.copyWith(
                                  decoration: TextDecoration.underline,
                                  color: Palette.blackSecondary,
                                ),
                              ))
                          .toList(),
                    )
                  : Row(
                      children: [
                        Row(
                          children: invoiceList
                              .sublist(0, 2)
                              .map((e) => Text(
                                    '$e, ',
                                    style: textTheme.bodyMedium?.copyWith(
                                      decoration: TextDecoration.underline,
                                      color: Palette.blackSecondary,
                                    ),
                                  ))
                              .toList(),
                        ),
                        const Gap(2),
                        Text(
                          '+${invoiceList.length - 2}',
                          style: textTheme.bodyMedium?.copyWith(
                            decoration: TextDecoration.underline,
                            color: Palette.blackSecondary,
                          ),
                        )
                      ],
                    )),
        ),
      ],
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme) {
    return Row(
      children: [
        CurrencyWidget(
          amount,
          'NGN',
          amountStyle: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const Gap(5),
        Text(
          'NGN',
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.blackSecondary,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildContentText(String text, TextTheme textTheme,
      [bool isBold = false]) {
    return Text(
      text,
      style: textTheme.bodyMedium?.copyWith(
          fontSize: 12,
          color: Palette.blackSecondary,
          fontWeight: isBold ? FontWeight.w600 : null),
    );
  }

  Widget _buildContentBankCell(
      String bank, String bankNumber, TextTheme textTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          bank,
          style: textTheme.bodyMedium
              ?.copyWith(color: Palette.blackSecondary, fontSize: 12),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: CircleAvatar(
            backgroundColor: Palette.orangePrimaryDark,
            radius: 2,
          ),
        ),
        Text(
          bankNumber,
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: Palette.blackSecondary,
          ),
        ),
      ],
    );
  }

  String getStatusText(String status) {
    switch (status) {
      case "success":
        return "Success";
      case "pending":
        return "Pending";
      case "failed":
        return "Failed";
      default:
        return "";
    }
  }

  handleInvoiceDownload(Invoice invoice) async {
    isFetchingInvoicePdf.value = true;
    final response = await ref.read(downloadPayoutInvoiceUseCaseProvider(
        PayoutInvoiceParams(
            invoice.orders!.first.id!, invoice.retailOutletId!)));
    isFetchingInvoicePdf.value = false;
    response.when(
      success: (url) => openUri(url),
      failure: (err, _) => Toast.apiError(err, context),
    );
  }
}
