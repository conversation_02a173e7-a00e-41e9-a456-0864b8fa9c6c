import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/data/models/payout.dart';
import 'package:td_procurement/app/invoices/presentation/controllers/payout_controller.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/empty_payout.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/payout_tab.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/payout_table.dart';
import 'package:td_procurement/app/shell/presentation/widgets/top_bar.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class Payout extends ConsumerStatefulWidget {
  const Payout({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _PayoutState();
}

class _PayoutState extends ConsumerState<Payout> {
  final ValueNotifier<PayoutStatus> _selectedOption =
      ValueNotifier(PayoutStatus.all);
  final _controller = TextEditingController();
  List<Transaction>? payoutList;
  PayoutBankAccount? bankDetails;
  final _deBouncer = DeBouncer(milliseconds: 500);
  String queryText = '';

  void _onTextChanged(String query) {
    queryText = query;
    _deBouncer.run(() async {
      final params = ref.read(payoutArgProvider);
      ref.read(payoutArgProvider.notifier).state =
          params.querySearch(query, _selectedOption.value);
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    Widget widget = const SizedBox.shrink();
    var v = ref.watch(
      payoutControllerProvider(
        ref.watch(payoutArgProvider),
      ),
    );

    v.when(
      skipLoadingOnRefresh: false,
      data: (data) {
        payoutList = data.data!.transactions!;
        bankDetails = data.data!.account;
        if (data.data!.transactions!.isEmpty) {
          widget = const SizedBox(
            height: 500,
            child: Center(
              child: EmptyPayoutWidget(),
            ),
          );
        } else {
          widget =
              PayoutDataTable(payoutList!, bankDetails!, _selectedOption.value);
        }
      },
      error: (error, __) {
        widget = const SizedBox(
          height: 500,
          child: Center(
            child: EmptyPayoutWidget(),
          ),
        );
      },
      loading: () {
        widget = Skeletonizer(
          enabled: true,
          child: Row(
            children: [
              Expanded(
                child: PayoutDataTable(
                    List.filled(
                      10,
                      Transaction(),
                    ),
                    PayoutBankAccount(
                        accountNumber: '', accountName: '', bank: ''),
                    _selectedOption.value),
              ),
            ],
          ),
        );
      },
    );
    return Column(
      children: [
        Padding(
          padding:
              const EdgeInsets.only(left: 30, top: 20, bottom: 20, right: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: Text('Payouts', style: textTheme.headlineMedium)),
              const Gap(20),
              Row(
                children: [
                  ValueListenableBuilder<PayoutStatus>(
                    valueListenable: _selectedOption,
                    builder: (context, state, _) => Row(
                      children: PayoutStatus.values
                          .map(
                            (option) => InkWell(
                              splashColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              onTap: () {
                                if (state == option) {
                                  return;
                                }
                                _selectedOption.value = option;

                                final params = ref.read(payoutArgProvider);

                                ref.read(payoutArgProvider.notifier).state =
                                    params.querySearch(
                                        queryText, _selectedOption.value);
                              },
                              child: PayoutTab(
                                option: option,
                                isActive: state == option,
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                  const Spacer(),
                ],
              )
            ],
          ),
        ),
        Divider(thickness: 1, color: Palette.kE7E7E7, height: 1),
        Container(
          height: 48,
          padding: const EdgeInsets.only(left: 20),
          child: TextFormField(
            controller: _controller,
            // initialValue: null,
            decoration: InputDecoration(
              hintText: 'Type to search by payout id',
              prefixIcon: Padding(
                padding: const EdgeInsets.only(top: 2.0, right: 10),
                child: Skeleton.replace(
                  child: SvgPicture.asset(
                    '$kSvgDir/order/search.svg',
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              prefixIconConstraints: const BoxConstraints(
                minWidth: 22,
                minHeight: 22,
              ),
              hintStyle: textTheme.bodyMedium?.copyWith(
                color: Palette.placeholder,
                fontWeight: FontWeight.w400,
              ),
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
            ),
            cursorColor: Palette.strokePressed,
            cursorHeight: 18,
            onChanged: _onTextChanged,
          ),
        ),
        Expanded(child: widget),
      ],
    );
  }
}

class PayoutDataTable extends ConsumerWidget {
  final List<Transaction> payoutList;
  final PayoutBankAccount details;
  final PayoutStatus status;

  const PayoutDataTable(this.payoutList, this.details, this.status,
      {super.key});

  @override
  Widget build(BuildContext context, ref) {
    final params = ref.watch(payoutArgProvider);
    var payoutData =
        ref.read(payoutControllerProvider(params)).valueOrNull?.data;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: PayoutTable(payoutList, details, status == PayoutStatus.all),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 35, right: 25),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (payoutData != null)
                Text(
                  '${payoutData.batch > 1 ? (payoutData.batch * 10) - 9 : 1} - ${payoutData.transactions!.length < 10 ? ((payoutData.batch - 1) * 10) + payoutData.transactions!.length : payoutData.batch * 10} of ${payoutData.total}',
                  style: TextStyle(
                    color: Palette.blackSecondary,
                  ),
                ),
              const Spacer(),
              PayoutPagination(total: payoutData?.total),
              const Gap(50),
            ],
          ),
        ),
      ],
    );
  }
}

class PayoutPagination extends ConsumerWidget {
  final int? total;
  final int? totalPages;
  const PayoutPagination({super.key, this.total, this.totalPages});

  @override
  Widget build(BuildContext context, ref) {
    final params = ref.watch(payoutArgProvider);
    final int batches = totalPages ??
        ((ref.read(payoutControllerProvider(params)).valueOrNull?.data?.total ??
                    total ??
                    0) /
                params.limit)
            .ceil();
    return Align(
      alignment: Alignment.topRight,
      child: SizedBox(
        width: 500,
        child: NumberPagination(
          onPageChanged: (int pageNumber) {
            ref.read(payoutArgProvider.notifier).state =
                params.paginate(pageNumber);
          },
          visiblePagesCount: batches > 5 || batches == 0 ? 5 : batches,
          buttonElevation: 0.3,
          totalPages: batches,
          currentPage: params.batch,
          buttonRadius: 8,
          selectedButtonColor: Palette.primaryBlack,
          selectedNumberColor: Colors.white,
          unSelectedButtonColor: Colors.white,
          unSelectedNumberColor: Palette.blackSecondary,
          fontSize: 14,
          firstPageIcon: SvgPicture.asset(
            kDoubleChevronLeftSvg,
            width: 30,
            height: 30,
          ),
          previousPageIcon: SvgPicture.asset(
            kChevronLeftSvg,
            colorFilter:
                ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
            width: 12,
            height: 12,
          ),
          lastPageIcon: SvgPicture.asset(
            kDoubleChevronRightSvg,
            width: 30,
            height: 30,
          ),
          nextPageIcon: SvgPicture.asset(
            kChevronRightSvg,
            colorFilter:
                ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
            width: 12,
            height: 12,
          ),
        ),
      ),
    );
  }
}
