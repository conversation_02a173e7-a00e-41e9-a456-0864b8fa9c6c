import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/domain/entities/invoices_params.dart';
import 'package:td_procurement/app/invoices/presentation/controllers/invoices_controller.dart';
import 'package:td_procurement/app/invoices/presentation/controllers/sales_invoices_controller.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoices_table.dart';
import 'package:td_procurement/src/components/date_picker/index.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class Invoices extends ConsumerStatefulWidget {
  final InvoiceType type;
  const Invoices(this.type, {this.status, super.key});
  final InvoiceStatus? status;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _Invoices();
  }
}

class _Invoices extends ConsumerState<Invoices> {
  final _layerDateLink = LayerLink();
  OverlayEntry? _overlayEntry;
  final ValueNotifier<List<DateTime>> _selectedDates = ValueNotifier([]);
  int activeIndex = 0;

  @override
  void initState() {
    if (widget.status != null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        switchTab(widget.status!);
      });
    }
    super.initState();
  }

  @override
  void dispose() {
    _selectedDates.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant Invoices oldWidget) {
    if (oldWidget.type != widget.type) {
      _selectedDates.value = [];
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    const statusOptions = ["All", 'Unpaid', 'Paid', 'Overdue'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Gap(20),
        Padding(
            padding: const EdgeInsets.only(left: 40),
            child:
                Text('${widget.type.title}s', style: textTheme.headlineMedium)),
        const Gap(20),
        Container(
          padding: const EdgeInsets.only(left: 40).copyWith(bottom: 12),
          decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Palette.stroke))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 3,
                child: Row(
                    children: mapIndexed(statusOptions, (index, status) {
                  bool isSelected = index == activeIndex;
                  return Flexible(
                    child: StatusFilteringWidget(
                      status,
                      isSelected,
                      width: 130,
                      padding: const EdgeInsets.symmetric(vertical: 19),
                      onPressed: () {
                        if (isSelected) return;
                        setState(() {
                          activeIndex = index;
                        });
                        final params = ref.read(widget.type.argument);
                        ref.read(widget.type.argument.notifier).state =
                            params.switchStatus(InvoiceStatus.values[index]);
                      },
                    ),
                  );
                }).toList()),
              ),
              InkWell(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Palette.kE7E7E7),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                    boxShadow: const [
                      BoxShadow(
                          color: Palette.k0000000A,
                          offset: Offset(0, 4),
                          blurRadius: 12,
                          spreadRadius: 0)
                    ],
                  ),
                  height: 34,
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: CompositedTransformTarget(
                    link: _layerDateLink,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ValueListenableBuilder<List<DateTime>>(
                          valueListenable: _selectedDates,
                          builder: (context, dates, _) => Text(
                            dates.isEmpty
                                ? 'Filter Date'
                                : '${dates.first.toDayMonth()} - ${dates.last.toDayMonth()}',
                            style: textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w500),
                          ),
                        ),
                        const Gap(10),
                        SvgPicture.asset(kCalendarSvg),
                        const Gap(8),
                      ],
                    ),
                  ),
                ),
                onTap: () => createDatePickerOverlay(),
              ),
              const Gap(20)
            ],
          ),
        ),
        Expanded(child: InvoicesTable(widget.type)),
        const Gap(20)
      ],
    );
  }

  void switchTab(InvoiceStatus status) {
    final params = ref.read(widget.type.argument);
    ref.read(widget.type.argument.notifier).state = params.switchStatus(status);
  }

  void createDatePickerOverlay() {
    final size = Config(width: 585, height: 325);
    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // A GestureDetector to close the overlay when tapped outside
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                _closeOverlay(_overlayEntry);
              },
            ),
          ),
          Positioned(
            width: size.width,
            height: size.height + 25,
            child: CompositedTransformFollower(
              link: _layerDateLink,
              offset: const Offset(
                  -470, 30), // Align horizontally (right) and place below
              showWhenUnlinked: false,
              child: Material(
                elevation: 0.0,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Palette.stroke),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: MultiDatePickerOverlay(
                      currentStart: _selectedDates.value.firstOrNull,
                      currentEnd: _selectedDates.value.lastOrNull,
                      onSelectRange: (startDate, endDate) {
                        _closeOverlay(_overlayEntry);
                        final params = ref.read(widget.type.argument);
                        ref.read(widget.type.argument.notifier).state =
                            params.filterByDate(startDate, endDate);
                        _selectedDates.value =
                            (startDate != null && endDate != null)
                                ? [startDate, endDate]
                                : [];
                      },
                      onCancel: () => _closeOverlay(_overlayEntry),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _closeOverlay(OverlayEntry? overlayEntry) {
    overlayEntry?.remove();
    overlayEntry = null;
  }
}

extension InvoiceTypeProviders on InvoiceType {
  AutoDisposeAsyncNotifierProviderFamily<
          AutoDisposeFamilyAsyncNotifier<InvoiceStatement, InvoicesParams>, InvoiceStatement, InvoicesParams>
      get controller => switch (this) {
            InvoiceType.purchase => invoicesControllerProvider,
            InvoiceType.sales => salesInvoicesControllerProvider,
          };

  AutoDisposeStateProvider<InvoicesParams> get argument => switch (this) {
        InvoiceType.purchase => invoicesArgProvider,
        InvoiceType.sales => salesInvoicesArgProvider,
      };
}
