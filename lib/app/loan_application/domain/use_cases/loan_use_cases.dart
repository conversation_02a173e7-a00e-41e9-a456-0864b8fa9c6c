import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';
import 'package:td_procurement/app/loan_application/data/repositories/loan_repo.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final applyForLoanUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<dynamic>>, NoParams>(
  (ref, arg) => UseCase<dynamic>().call(
    () => ref.read(loanRepoProvider).applyForLoan(),
  ),
);
