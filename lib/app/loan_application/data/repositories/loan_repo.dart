import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/loan_application/data/sources/loan_data_source.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

abstract class LoanRepo {
  Future<ApiResponse<dynamic>> applyForLoan();
}

final loanRepoProvider = Provider<LoanRepo>((ref) {
  return LoanRepoImplementation(ref);
});

class LoanRepoImplementation extends LoanRepo {
  final Ref _ref;

  LoanRepoImplementation(this._ref);

  late final LoanDataSource _dataSource = _ref.read(loanDataProvider);

  @override
  Future<ApiResponse> applyForLoan() {
    return dioInterceptor(() => _dataSource.applyForLoan(), _ref);
  }
}
