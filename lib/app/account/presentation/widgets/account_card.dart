import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/account/domain/entities/statement_params.dart';
import 'package:td_procurement/app/account/presentation/controllers/account_controller.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class AccountCard extends ConsumerWidget {
  const AccountCard(
    this.amount,
    this.paymentType, {
    super.key,
    this.currency,
  });
  final num amount;
  final Currency? currency;
  final StatementPaymentType paymentType;

  @override
  Widget build(BuildContext context, ref) {
    final textTheme = Theme.of(context).textTheme;
    final params = ref.watch(statementArgProvider);
    final isActive =
        (params?.paymentType ?? StatementPaymentType.balance) == paymentType;
    return Expanded(
      child: InkWell(
        child: Container(
          height: 129,
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: isActive ? Palette.primaryBlack : Colors.white,
            border: isActive ? null : Border.all(color: Palette.stroke),
            boxShadow: [
              isActive
                  ? const BoxShadow(
                      color: Palette.k0000002B,
                      blurRadius: 7,
                      spreadRadius: 0,
                      offset: Offset(0, 4),
                    )
                  : const BoxShadow(
                      color: Palette.k0000000A,
                      blurRadius: 2,
                      spreadRadius: -1,
                      offset: Offset(0, 2),
                    ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  paymentType.title,
                  style: textTheme.bodyMedium?.copyWith(
                      color: isActive ? Colors.white : Palette.primaryBlack,
                      fontWeight: FontWeight.w700),
                ),
                CurrencyWidget(
                  allowNegative: true,
                  decimalDigits: 2,
                  amount,
                  currency?.iso ?? kDefaultCurrency,
                  amountStyle: textTheme.headlineMedium?.copyWith(
                      color: isActive &&
                              paymentType == StatementPaymentType.balance
                          ? Colors.white
                          : paymentType.color,
                      fontWeight: FontWeight.w400),
                ),
              ],
            ),
          ),
        ),
        onTap: () => fetchByPaymentType(ref),
      ),
    );
  }

  void fetchByPaymentType(WidgetRef ref) {
    final params = ref.read(statementArgProvider);
    ref.read(statementArgProvider.notifier).state =
        params?.payment(paymentType) ??
            StatementParams(paymentType: paymentType);
  }
}
