import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AccountAction extends StatelessWidget {
  final String text;
  final String icon;
  final VoidCallback onPressed;

  const AccountAction(
      {super.key,
      required this.text,
      required this.icon,
      required this.onPressed});
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      splashColor: Colors.yellow,
      highlightColor: Colors.green,
      hoverColor: Colors.red,
      child: Container(
        padding: const EdgeInsets.all(8),
        height: 34,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.transparent,
          border: Border.all(color: Palette.kE7E7E7),
          boxShadow: const [
            BoxShadow(
              color: Palette.k0000000A,
              blurRadius: 12,
              spreadRadius: 0,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Text(
              text,
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(fontWeight: FontWeight.w500),
            ),
            const Gap(10),
            SvgPicture.asset(
              icon,
            ),
          ],
        ),
      ),
    );
  }
}
