import 'dart:ui';

class StatementParams {
  final int batch;
  final int limit;
  final StatementType type;
  final StatementSorting sortBy;
  final StatementPaymentType? paymentType;
  final DateTime? startDate;
  final DateTime? endDate;

  StatementParams(
      {this.batch = 1,
      this.limit = 10,
      this.type = StatementType.list,
      this.sortBy = StatementSorting.desc,
      this.paymentType = StatementPaymentType.balance,
      this.endDate,
      this.startDate});

  StatementParams email() => StatementParams(
      type: StatementType.email,
      startDate: startDate,
      endDate: endDate,
      paymentType: paymentType);

  StatementParams paginate(int index) => StatementParams(
      batch: index,
      type: type,
      paymentType: paymentType,
      limit: limit,
      sortBy: sortBy,
      startDate: startDate,
      endDate: endDate);

  StatementParams sort(StatementSorting sort) => StatementParams(
      batch: batch,
      type: type,
      limit: limit,
      sortBy: sort,
      paymentType: paymentType,
      startDate: startDate,
      endDate: endDate);

  StatementParams payment(StatementPaymentType paymentType) => StatementParams(
      batch: batch,
      type: type,
      limit: limit,
      sortBy: sortBy,
      paymentType: paymentType,
      startDate: startDate,
      endDate: endDate);

  Map<String, Object?> toMap() {
    return {
      'batch': batch,
      'limit': limit,
      'type': type.name,
      'sortBy': sortBy.name,
      if (paymentType != StatementPaymentType.balance)
        'paymentType': paymentType?.name,
      if (startDate != null && endDate != null) ...{
        'startDate': startDate!.millisecondsSinceEpoch ~/ 1000,
        'endDate': endDate!.millisecondsSinceEpoch ~/ 1000,
      }
    };
  }
}

enum StatementType { list, email }

enum StatementSorting { desc, asc }

enum StatementPaymentType {
  balance(
    'Balance',
    Color.fromRGBO(8, 31, 36, 1),
  ),
  credits(
    'Total Credit',
    Color.fromRGBO(12, 166, 83, 1),
  ),
  debits(
    'Total Debit',
    Color.fromRGBO(230, 16, 16, 1),
  );

  const StatementPaymentType(this.title, this.color);
  final String title;
  final Color color;
}

extension SortLabel on StatementSorting {
  get label => switch (this) {
        StatementSorting.desc => 'Descending',
        StatementSorting.asc => 'Ascending',
      };
}
