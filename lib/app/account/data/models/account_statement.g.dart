// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_statement.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AccountStatement _$AccountStatementFromJson(Map<String, dynamic> json) =>
    AccountStatement(
      (json['transactions'] as List<dynamic>)
          .map((e) => AccountTransactions.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['balance'] as num,
      json['totalCredits'] as num,
      json['totalDebits'] as num,
      json['totalCount'] as num,
      Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
      Currency.fromJson(json['currency'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AccountStatementToJson(AccountStatement instance) =>
    <String, dynamic>{
      'transactions': instance.transactions,
      'balance': instance.balance,
      'totalCredits': instance.totalCredits,
      'totalDebits': instance.totalDebits,
      'totalCount': instance.totalCount,
      'pagination': instance.pagination,
      'currency': instance.currency,
    };

Pagination _$PaginationFromJson(Map<String, dynamic> json) => Pagination(
      (json['page'] as num).toInt(),
      (json['perPage'] as num).toInt(),
    );

Map<String, dynamic> _$PaginationToJson(Pagination instance) =>
    <String, dynamic>{
      'page': instance.page,
      'perPage': instance.perPage,
    };

Currency _$CurrencyFromJson(Map<String, dynamic> json) => Currency(
      json['iso'] as String,
      json['symbol'] as String,
    );

Map<String, dynamic> _$CurrencyToJson(Currency instance) => <String, dynamic>{
      'iso': instance.iso,
      'symbol': instance.symbol,
    };
