import 'package:json_annotation/json_annotation.dart';

part 'account_transactions.g.dart';

@JsonSerializable()
class AccountTransactions {
  @JsonKey(name: '_id')
  final String id;
  @JsonKey(fromJson: invoiceNumberFromJson)
  final String invoiceNumber;
  final String retailOutletId;
  final num amount;
  final DateTime createdAt;
  final AccountTransactionType transactionType;


  static String invoiceNumberFromJson(Object number) {
    return number is int ? number.toString() : number as String;
  }


  factory AccountTransactions.fromJson(Map<String, Object?> json) => _$AccountTransactionsFromJson(json);

  AccountTransactions(this.id, this.invoiceNumber, this.retailOutletId, this.amount, this.createdAt, this.transactionType);
  Map<String, Object?> toJson() => _$AccountTransactionsToJson(this);
}

enum AccountTransactionType {debits,credits}
