import 'package:json_annotation/json_annotation.dart';
import 'package:td_procurement/app/account/data/models/account_transactions.dart';

part 'account_statement.g.dart';

@JsonSerializable()
class AccountStatement {
  final List<AccountTransactions> transactions;
  final num balance;
  final num totalCredits;
  final num totalDebits;
  final num totalCount;
  final Pagination pagination;
  final Currency currency;

  factory AccountStatement.fromJson(Map<String, Object?> json) =>
      _$AccountStatementFromJson(json);

  AccountStatement(this.transactions, this.balance, this.totalCredits,
      this.totalDebits, this.totalCount, this.pagination, this.currency);
  Map<String, Object?> toJson() => _$AccountStatementToJson(this);
}

@JsonSerializable()
class Pagination {
  final int page;
  final int perPage;

  factory Pagination.fromJson(Map<String, Object?> json) =>
      _$PaginationFromJson(json);
  Map<String, Object?> toJson() => _$PaginationToJson(this);
  Pagination(this.page, this.perPage);
}

@JsonSerializable()
class Currency {
  final String iso;
  final String symbol;

  factory Currency.fromJson(Map<String, Object?> json) =>
      _$CurrencyFromJson(json);
  Map<String, Object?> toJson() => _$CurrencyToJson(this);
  Currency(this.iso, this.symbol);
}
