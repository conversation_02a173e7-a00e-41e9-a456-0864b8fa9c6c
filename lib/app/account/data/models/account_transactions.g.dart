// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_transactions.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AccountTransactions _$AccountTransactionsFromJson(Map<String, dynamic> json) =>
    AccountTransactions(
      json['_id'] as String,
      AccountTransactions.invoiceNumberFromJson(
          json['invoiceNumber'] as Object),
      json['retailOutletId'] as String,
      json['amount'] as num,
      DateTime.parse(json['createdAt'] as String),
      $enumDecode(_$AccountTransactionTypeEnumMap, json['transactionType']),
    );

Map<String, dynamic> _$AccountTransactionsToJson(
        AccountTransactions instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'invoiceNumber': instance.invoiceNumber,
      'retailOutletId': instance.retailOutletId,
      'amount': instance.amount,
      'createdAt': instance.createdAt.toIso8601String(),
      'transactionType':
          _$AccountTransactionTypeEnumMap[instance.transactionType]!,
    };

const _$AccountTransactionTypeEnumMap = {
  AccountTransactionType.debits: 'debits',
  AccountTransactionType.credits: 'credits',
};
