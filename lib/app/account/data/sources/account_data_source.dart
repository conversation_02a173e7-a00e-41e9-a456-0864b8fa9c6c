import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/account/domain/entities/statement_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

class AccountDataSourceImplementation extends AccountDataSource {
  final Ref _ref;

  AccountDataSourceImplementation(this._ref);

  late final TdApiClient _apiClient = _ref.read(apiClientProvider);
  late final config = _ref.read(appConfigProvider);

  @override
  Future<AccountStatement> checkAccountStatement(StatementParams params) async{
    final res = await _apiClient.get(
      "${config.awsApiUrlV4}$kAccountStatementApiPath",
      queryParameters: params.toMap(),
    );
    return AccountStatement.fromJson(res.data['data']);
  }

  @override
  Future<bool> sendAccountStatement(StatementParams params) async{
     await _apiClient.get(
      "${config.awsApiUrlV4}$kAccountStatementApiPath",
      queryParameters: params.toMap(),
    );
    return true;
  }
}

abstract class AccountDataSource{
  Future<AccountStatement> checkAccountStatement(StatementParams params);
  Future<bool> sendAccountStatement(StatementParams params);
}

final accountDataProvider = Provider<AccountDataSource>((ref) {
  return AccountDataSourceImplementation(ref);
});