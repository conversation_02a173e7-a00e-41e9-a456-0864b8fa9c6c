import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/account/data/sources/account_data_source.dart';
import 'package:td_procurement/app/account/domain/entities/statement_params.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

class AccountRepoImplementation extends AccountRepo {
  final Ref _ref;

  AccountRepoImplementation(this._ref);

  late final AccountDataSource _dataSource = _ref.read(accountDataProvider);

  @override
  Future<ApiResponse<AccountStatement>> fetchAccountStatement(
      StatementParams params) {
    return dioInterceptor(
        () => _dataSource.checkAccountStatement(params), _ref);
  }

  @override
  Future<ApiResponse<bool>> sendAccountStatement(StatementParams params) {
    return dioInterceptor(() => _dataSource.sendAccountStatement(params), _ref);
  }
}

abstract class AccountRepo {
  Future<ApiResponse<AccountStatement>> fetchAccountStatement(
      StatementParams params);
  Future<ApiResponse<bool>> sendAccountStatement(StatementParams params);
}

final accountRepoProvider = Provider<AccountRepo>((ref) {
  return AccountRepoImplementation(ref);
});
