/*import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/auth/domain/params/initiate_stripe_response.dart';
//import 'dart:ui_web' as ui;
//import 'package:web/web.dart' as web;

//import 'package:td_procurement/core/router/router.dart';

class VerificationScreen extends StatefulWidget {
  const VerificationScreen(this.response, {super.key});

  final InitiateStripeResponse response;

  @override
  State<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  // late web.HTMLIFrameElement iframeElement;

  @override
  void initState() {
    // Registering the iframe element with a unique viewType
    /* final String viewType = 'iframeElement-${widget.response.verificationUrl}';

    iframeElement = web.HTMLIFrameElement()
      ..src = widget.response.verificationUrl!
      ..style.border = 'none'
      ..width = '100%'
      ..height = '100%';

    // Add an event listener for when the iframe's content is loaded or navigated
    iframeElement.onLoad.listen((event) {
      String currentUrl = iframeElement.src!;

      // Check if the iframe has redirected to the specific URL
      if (currentUrl == widget.response.redirectUrl ||
          currentUrl.contains(widget.response.redirectUrl!)) {
        context.pop();
        context.pop();
      }
    });

// Register the view factory with the viewType
    ui.platformViewRegistry
        .registerViewFactory(viewType, (int viewId) => iframeElement);*/
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InAppWebView(
      initialUrlRequest:
          URLRequest(url: WebUri(widget.response.verificationUrl!)),
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final uri = navigationAction.request.url;

        //track quick links on ios and navigate accordingly
        if (uri.toString() == widget.response.redirectUrl) {
          return NavigationActionPolicy.CANCEL;
        }

        return NavigationActionPolicy.ALLOW;
      },
    );

    /* HtmlElementView(
      viewType: 'iframeElement-${widget.response.verificationUrl}',
    );*/
  }
}
*/