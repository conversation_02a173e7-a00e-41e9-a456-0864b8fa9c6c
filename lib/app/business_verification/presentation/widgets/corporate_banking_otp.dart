import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

class CorporateBankingOtp extends StatefulWidget {
  const CorporateBankingOtp({super.key});

  @override
  State<CorporateBankingOtp> createState() => _CorporateBankingOtpState();
}

class _CorporateBankingOtpState extends State<CorporateBankingOtp> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Corporate Banking Information',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              'Enter the code sent to the account signatory’s BVN in the previous step',
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      enabled: true,
                      hintText: 'Enter code',
                      //    suffixIcon: Text('What is this?'),
                      //  suffixText: 'What is this?'
                      suffixIcon: Padding(
                        padding: const EdgeInsets.only(right: 8, top: 10),
                        child: InkWell(
                            onTap: () {},
                            child: Text(
                              'Resend in 60s',
                              style: TextStyle(color: Palette.statusPending),
                            )),
                      ),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    //style: ,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
