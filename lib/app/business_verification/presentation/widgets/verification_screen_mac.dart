/*import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/auth/domain/params/initiate_stripe_response.dart';

class VerificationScreen extends StatefulWidget {
  const VerificationScreen(this.response, {super.key});

  final InitiateStripeResponse response;

  @override
  State<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  @override
  Widget build(BuildContext context) {
    return InAppWebView(
      initialUrlRequest:
          URLRequest(url: WebUri(widget.response.verificationUrl!)),
    );
  }
}
*/