import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/index.dart' hide Container;
import 'package:td_procurement/app/auth/domain/entities/initiate_stripe_response.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/business_verification/domain/use_cases/business_use_cases.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:url_launcher/url_launcher.dart';

class VerifyIdentity extends ConsumerWidget {
  VerifyIdentity({super.key});

  final ValueNotifier<bool> _loader = ValueNotifier(false);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;

    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verify your Identity',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              "To complete your registration, we'll need you to provide some additional information to verify your identity.",
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            CustomFilledButton(
              onPressed: () {
                submit(ref, outlet!, context);
              },
              text: 'Start Verification',
              loaderNotifier: _loader,
            ),
            const Gap(10),
            Center(
                child: TextButton(
                    onPressed: () => context.pop(),
                    child: const Text('Back to Business details')))
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(url) async {
    if (!await launchUrl(Uri.parse(url), webOnlyWindowName: "_self")) {
      throw Exception('Could not launch');
    }
  }

  Future<void> submit(
      WidgetRef ref, RetailOutlet outlet, BuildContext context) async {
    _loader.value = true;

    final response = await ref.read(verifyIdentityUseCaseProvider(
        InitiateStripeRequest(
            redirectUrl: 'https://flutter.account.tradedepot.co/home')));
    switch (response) {
      case Success data:
        /*context.pushNamed(kStripeRoute,
            extra: data.data as InitiateStripeResponse);*/
        _loader.value = false;
        _launchUrl((data.data as InitiateStripeResponse).verificationUrl);
      case Failure error:
        {
          _loader.value = false;
          Toast.apiError(error.error, context);
        }
    }
  }
}


//.