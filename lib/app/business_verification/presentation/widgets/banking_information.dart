import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/business_verification/domain/entities/bank_data.dart';
import 'package:td_procurement/app/business_verification/domain/entities/business_verification_request.dart';
import 'package:td_procurement/app/business_verification/domain/use_cases/business_use_cases.dart';
import 'package:td_procurement/app/business_verification/presentation/logic/states/business_verification_state.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class BankingInformation extends ConsumerStatefulWidget {
  const BankingInformation({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BankingInformationState();
}

class _BankingInformationState extends ConsumerState<BankingInformation> {
  late TextEditingController accountNameController;
  late TextEditingController accountNumberController;
  late TextEditingController bvnController;
  final _key = GlobalKey<FormState>();
  final ValueNotifier<bool> _loader = ValueNotifier(false);
  final ValueNotifier<bool> _bankLoader = ValueNotifier(true);
  String? selectedBank;
  List<BankListData> bankList = [];

  @override
  void initState() {
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    accountNameController =
        TextEditingController(text: outlet?.walletAccount?.bankName);
    selectedBank = outlet?.walletAccount?.bankName;
    accountNumberController =
        TextEditingController(text: outlet?.walletAccount?.accountNumber);
    bvnController = TextEditingController(text: outlet?.company?.kyb?.bvn);

    loadBankList();
    super.initState();
  }

  loadBankList() {
    _bankLoader.value = true;

    ref.read(getBanksUseCaseProvider(null)).then((response) {
      switch (response) {
        case Success data:
          bankList.addAll((data.data as BankData).data);
          _bankLoader.value = false;
        case Failure error:
          {
            _bankLoader.value = false;
            Toast.apiError(error.error, context);
          }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;

    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Form(
          key: _key,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(kBankingSvg),
              const Gap(20),
              Text(
                'Banking Information',
                style: theme.textTheme.bodyLarge
                    ?.copyWith(fontWeight: FontWeight.w600),
              ),
              const Gap(5),
              Text(
                'Fill in your organization\'s bank details ',
                style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w400, color: Palette.blackSecondary),
              ),
              const Gap(10),
              ValueListenableBuilder<bool>(
                valueListenable: _bankLoader,
                builder: (_, loaderValue, __) {
                  return Theme(
                    data: ThemeData(
                        popupMenuTheme:
                            PopupMenuThemeData(color: Palette.kFFFFFF)),
                    child: PopupMenuButton<String>(
                      itemBuilder: (context) {
                        var list = <PopupMenuEntry<String>>[];
                        for (var e in bankList) {
                          list.add(
                            PopupMenuItem(
                              value: e.name,
                              child: Padding(
                                padding: const EdgeInsets.only(left: 18),
                                child: Text(
                                  e.name,
                                  maxLines: 1,
                                ),
                              ),
                            ),
                          );
                        }
                        return list;
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                        side: const BorderSide(color: Colors.transparent),
                      ),
                      elevation: 0.7,
                      padding: const EdgeInsets.all(0),
                      onSelected: (value) {
                        setState(() {
                          selectedBank = value;
                        });
                      },
                      tooltip: null,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                              width: .2),
                        ),
                        height: 50,
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(selectedBank ?? 'Select Bank'),
                            ),
                            loaderValue
                                ? ConstrainedBox(
                                    constraints: const BoxConstraints.tightFor(
                                        width: 30, height: 30),
                                    child: CircularProgressIndicator(
                                      color: theme.colorScheme.primary,
                                    ))
                                : bankList.isEmpty
                                    ? InkWell(
                                        onTap: () {
                                          loadBankList();
                                        },
                                        child: Text(
                                          'Retry',
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                                  color: Palette.deleteRed),
                                        ),
                                      )
                                    : const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
              const Gap(10),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: accountNumberController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Field required";
                        } else if (value.length < 10) {
                          return "Minimum number of characters is 10";
                        } else {
                          return null;
                        }
                      },
                      decoration: const InputDecoration(
                          hintText: 'Business Account number'),
                      keyboardType: TextInputType.emailAddress,
                      //style: ,
                    ),
                  ),
                ],
              ),
              const Gap(10),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: bvnController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Field required";
                        } else if (value.length < 11) {
                          return "Minimum number of characters is 11";
                        } else {
                          return null;
                        }
                      },
                      decoration: const InputDecoration(
                          hintText: 'Account signatory BVN'),
                      keyboardType: TextInputType.emailAddress,
                      onFieldSubmitted: (_) {
                        if (selectedBank == null) {
                          Toast.error('Select Bank', context);
                          return;
                        }
                        if (_key.currentState!.validate()) {
                          submit(outlet!, ref, context);
                        }
                      },
                      //style: ,
                    ),
                  ),
                ],
              ),
              const Gap(10),
              Row(
                children: [
                  Expanded(
                    child: CustomFilledButton(
                      onPressed: () {
                        if (selectedBank == null) {
                          Toast.error('Select Bank', context);
                          return;
                        }
                        if (_key.currentState!.validate()) {
                          submit(outlet!, ref, context);
                        }
                      },
                      text: 'Save Data',
                      loaderNotifier: _loader,
                    ),
                  ),
                ],
              ),
              const Gap(10),
              /* Center(
                  child: TextButton(
                      onPressed: () {
                        ref
                            .read(businessVerificationProvider.notifier)
                            .navigatePageForward(
                                outlet?.country?.toLowerCase() == "ng");
                      },
                      child: const Text('Skip'))),*/
              /* Container(
                decoration: BoxDecoration(
                    color: Palette.statusSuccess.withOpacity(.1),
                    borderRadius: BorderRadius.circular(8)),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Account details verified',
                      style: theme.textTheme.bodySmall
                          ?.copyWith(color: Palette.statusSuccess),
                    ),
                    Icon(
                      Icons.check_circle,
                      color: Palette.statusSuccess,
                      size: 10,
                    )
                  ],
                ),
              )*/
            ],
          ),
        ),
      ),
    );
  }

  Future<void> submit(
      RetailOutlet outlet, WidgetRef ref, BuildContext context) async {
    // bool? step4Complete = outlet.kyc?.idVerified;
    _loader.value = true;

    final response = await ref.read(updateBusinessUseCaseProvider(
        BusinessVerificationRequest(
            id: outlet.id,
            businessName: outlet.outletBusinessName,
            companyBankAccountName: selectedBank,
            companyBankAccountNumber: accountNumberController.text,
            companyAccountSignatoryBvn: bvnController.text)));
    switch (response) {
      case Success():
        ref
            .read(businessVerificationProvider.notifier)
            .navigatePageForward(outlet.country?.toLowerCase() == "ng");
        _loader.value = false;
        ref.read(userControllerProvider.notifier).updateOutlet(response.data);
      case Failure error:
        {
          _loader.value = false;
          Toast.apiError(error.error, context);
        }
    }
  }
}
