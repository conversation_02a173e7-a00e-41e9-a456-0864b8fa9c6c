import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/business_information_ng.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/company_role.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/corporate_banking.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/corporate_banking_otp.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/registration_documents.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/registered_business.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/verify_business_ng.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class BusinessVerificationNG extends ConsumerStatefulWidget {
  const BusinessVerificationNG({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _BusinessVerificationNG();
  }
}

class _BusinessVerificationNG extends ConsumerState<BusinessVerificationNG> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 10),
            color: theme.secondaryHeaderColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    InkWell(
                      onTap: () {},
                      child: const Icon(
                        Icons.clear,
                        color: Colors.black,
                        size: 15,
                      ),
                    ),
                    const Gap(20),
                    Text(
                      'Complete Onboarding',
                      style: theme.textTheme.bodyLarge,
                    )
                  ],
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.arrow_back_ios,
                      size: 15,
                    ),
                    const Gap(10),
                    SizedBox(
                      width: 150,
                      child: LinearProgressIndicator(
                        color: Palette.orangePrimaryDark,
                        backgroundColor: Colors.grey.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                        minHeight: 7.5,
                        value: currentIndex / 6,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: const Color(0XFFFF8D06).withValues(alpha: 0.12)),
                        child: const Text(
                          'Finish Later',
                          style: TextStyle(color: Color(0XFFFF8D06)),
                        ),
                      ),
                    ),
                    const Gap(20),
                    InkWell(
                      onTap: () {
                        if (currentIndex < 6) {
                          setState(() {
                            currentIndex++;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Palette.brandBlack),
                        child: const Text(
                          'Continue',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
                /*    ConstrainedBox(
                    constraints: const BoxConstraints.tightFor(width: 130),
                    child: FilledButton(
                        onPressed: () {}, child: const Text('Back to home')))*/
              ],
            ),
          ),
          Expanded(
            child: pageMapper(currentIndex),
          ),
        ],
      ),
    );
  }

  Widget pageMapper(int index) {
    switch (index) {
      case 0:
        return const VerifyBusinessNG();
      case 1:
        return const BusinessInformationNG();
      case 2:
        return const RegisteredBusiness();
      case 3:
        return const RegistrationDocuments();
      case 4:
        return const CompanyRole();
      case 5:
        return const CorporateBanking();
      case 6:
        return const CorporateBankingOtp();
      default:
        return const SizedBox.shrink();
    }
  }
}
