import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:td_procurement/src/components/toast/toast.dart';

mixin FileUpload {
  bool isValidFileSize(Uint8List size, BuildContext context) {
    const int maxSizeInBytes = 1 * 1024 * 1024;
    if (size.lengthInBytes <= maxSizeInBytes) {
      return true;
    } else {
      Toast.info('File size must not be more than 1MB', context);
      return false;
    }
  }
}
