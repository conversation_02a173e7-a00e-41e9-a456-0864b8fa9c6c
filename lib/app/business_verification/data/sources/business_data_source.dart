import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/auth/domain/entities/initiate_stripe_response.dart';
import 'package:td_procurement/app/business_verification/data/sources/business_data_source_impl.dart';
import 'package:td_procurement/app/business_verification/domain/entities/bank_data.dart';
import 'package:td_procurement/app/business_verification/domain/entities/business_verification_request.dart';
import 'package:td_procurement/core/DI/di_providers.dart';

abstract class BusinessDataSource {
  Future<RetailOutlet> updateBusinessDetails(
      BusinessVerificationRequest params);
  Future<InitiateStripeResponse> initiateStripeVerification(
      InitiateStripeRequest request);
  Future<BankData> getBankList();
}

final businessDataSourceProvider = Provider<BusinessDataSource>((ref) {
  final appConfig = ref.read(appConfigProvider);
  final apiConfig = ref.read(apiConfigProvider);
  final apiClient = TdApiClient(apiConfig);
  return BusinessDataSourceImplementation(apiClient, appConfig);
});

final businessDataSourceProvider2 = Provider<BusinessDataSource>((ref) {
  final appConfig = ref.read(appConfigProvider);
  final apiConfig = ref.read(apiConfigProvider);

  final apiClient = TdApiClient(apiConfig);
  return BusinessDataSourceImplementation(apiClient, appConfig);
});
