import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/domain/entities/initiate_stripe_response.dart';
import 'package:td_procurement/app/business_verification/domain/entities/bank_data.dart';
import 'package:td_procurement/app/business_verification/domain/entities/business_verification_request.dart';
import 'package:td_procurement/app/business_verification/domain/repositories/business_repo.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final updateBusinessUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<RetailOutlet>>, BusinessVerificationRequest>(
  (ref, arg) => UseCase<RetailOutlet>().call(
    () => ref.read(businessRepoProvider).updateBusinessDetails(arg),
  ),
);

final verifyIdentityUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<InitiateStripeResponse>>, InitiateStripeRequest>(
  (ref, arg) => UseCase<InitiateStripeResponse>().call(
      () => ref.read(businessRepoProvider).initiateStripeVerification(arg)),
);

final getBanksUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<BankData>>, dynamic>(
  (ref, arg) => UseCase<BankData>()
      .call(() => ref.read(businessRepoProvider).getBankList()),
);
