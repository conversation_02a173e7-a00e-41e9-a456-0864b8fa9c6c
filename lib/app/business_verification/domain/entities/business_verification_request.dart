class LegalRequestModel {
  String? id;
  String? outletBusinessName;
  String? businessStatus;
  String? registrationNumber;

  LegalRequestModel(
      {this.id,
      this.businessStatus,
      this.outletBusinessName,
      this.registrationNumber});

  factory LegalRequestModel.fromMap(Map<String, dynamic> json) {
    return LegalRequestModel(
        id: json['id'],
        outletBusinessName: json['outletBusinessName'],
        businessStatus: json['businessStatus'],
        registrationNumber: json['registrationNumber']);
  }
}

class BusinessVerificationRequest {
  BusinessVerificationRequest(
      {this.id,
      this.businessName,
      this.contactRole,
      this.contactName,
      this.businessStatus,
      this.registrationNumber,
      this.taxId,
      this.companyBankAccountName,
      this.companyBankAccountNumber,
      this.companyAccountSignatoryBvn,
      this.images,
      this.countryOfIncorporation});
  late final String? id;
  late final String? businessName;
  late final String? contactRole;
  late final String? contactName;
  late final String? businessStatus;
  late final String? registrationNumber;
  late final String? taxId;
  late final String? companyBankAccountName;
  late final String? companyBankAccountNumber;
  late final String? companyAccountSignatoryBvn;
  late final List<Map<String, dynamic>>? images;
  late final String? countryOfIncorporation;

  BusinessVerificationRequest.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    businessName = json['outletBusinessName'];
    contactRole = json['contactRole'];
    contactName = json['contactName'];
    businessStatus = json['businessStatus'];
    registrationNumber = json['registrationNumber'];
    taxId = json['taxId'];
    companyBankAccountName = json['companyBankAccountName'];
    companyBankAccountNumber = json['companyBankAccountNumber'];
    companyAccountSignatoryBvn = json['companyAccountSignatoryBvn'];
    countryOfIncorporation = json['countryOfIncorporation'];
    // images = List.from(json['images']).map((e) => Images.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{
      if (id != null) '_id': id,
      if (businessName != null) 'outletBusinessName': businessName,
      if (contactRole != null) 'contactRole': contactRole,
      if (contactName != null) 'contactName': contactName,
      if (businessStatus != null) 'businessStatus': businessStatus,
      if (registrationNumber != null) 'registrationNumber': registrationNumber,
      if (taxId != null) 'taxId': taxId,
      if (companyBankAccountName != null)
        'companyBankAccountName': companyBankAccountName,
      if (companyBankAccountNumber != null)
        'companyBankAccountNumber': companyBankAccountNumber,
      if (companyAccountSignatoryBvn != null)
        'companyAccountSignatoryBvn': companyAccountSignatoryBvn,
      if (images != null) 'images': images,
      if (countryOfIncorporation != null)
        'countryOfIncorporation': countryOfIncorporation,
      "isAdvance": true
    };

    return _data;
  }
}

class Images {
  Images({
    required this.type,
    required this.file,
    required this.file64,
  });
  late final String type;
  late final String file;
  late final String file64;

  Images.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    file = json['file'];
    file64 = json['file64'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['type'] = type;
    _data['file'] = file;
    _data['file64'] = file64;
    return _data;
  }
}
