import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/domain/entities/initiate_stripe_response.dart';
import 'package:td_procurement/app/business_verification/domain/entities/bank_data.dart';
import 'package:td_procurement/app/business_verification/domain/entities/business_verification_request.dart';
import 'package:td_procurement/app/business_verification/domain/repositories/business_repo_impl.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

abstract class BusinessRepo {
  Future<ApiResponse<RetailOutlet>> updateBusinessDetails(
      BusinessVerificationRequest params);

  Future<ApiResponse<InitiateStripeResponse>> initiateStripeVerification(
      InitiateStripeRequest request);

  Future<ApiResponse<BankData>> getBankList();
}

final businessRepoProvider = Provider<BusinessRepo>((ref) {
  return BusinessRepoImplementation(ref);
});
