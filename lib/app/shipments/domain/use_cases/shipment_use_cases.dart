import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/app/shipments/data/repositories/shipment_repository.dart';
import 'package:td_procurement/app/shipments/domain/entities/shipment_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/models/document.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final fetchShipmentsUseCaseProvider =
    Provider.autoDispose<FetchShipmentsUseCase>(
  (ref) {
    return (FetchShipmentsParam params) {
      return UseCase<FetchShipmentsResponse>().call(
        () => ref.read(shipmentRepoProvider).fetchShipments(params),
      );
    };
  },
);

final getShipmentUseCaseProvider = Provider.autoDispose<GetShipmentUseCase>(
  (ref) {
    return (String shippingId) {
      return UseCase<Shipment>().call(
        () => ref.read(shipmentRepoProvider).getShipment(shippingId),
      );
    };
  },
);

final fetchDocumentsUseCaseProvider =
    Provider.autoDispose<FetchDocumentsUseCase>(
  (ref) {
    return (String shippingId) {
      return UseCase<List<Document>>().call(
        () => ref.read(shipmentRepoProvider).fetchDocuments(shippingId),
      );
    };
  },
);

final getInvoiceUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<Invoice>>, String>(
  (ref, orderId) {
    final shipmentRepository = ref.read(shipmentRepoProvider);
    final useCase = UseCase<Invoice>();
    return useCase.call(() => shipmentRepository.getInvoice(orderId));
  },
);

final getInvoiceUrlUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<String>>, String>(
  (ref, orderId) {
    final shipmentRepository = ref.read(shipmentRepoProvider);
    final useCase = UseCase<String>();
    return useCase.call(() => shipmentRepository.getInvoiceUrl(orderId));
  },
);

typedef FetchShipmentsUseCase = Future<ApiResponse<FetchShipmentsResponse>>
    Function(FetchShipmentsParam params);

typedef GetShipmentUseCase = Future<ApiResponse<Shipment>> Function(
    String shippingId);

typedef FetchDocumentsUseCase = Future<ApiResponse<List<Document>>> Function(
    String shippingId);
