import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/app/shipments/domain/entities/shipment_params.dart';
import 'package:td_procurement/core/models/document.dart';
import 'package:td_procurement/core/models/optional.dart';

@immutable
class ShipmentState extends Equatable {
  final AsyncValue<List<Shipment>> shipments;
  final List<String> shipmentStatusOptions;
  final FetchShipmentsParam fetchShipmentsParams;
  final DateTime? selectedStartDate;
  final DateTime? selectedEndDate;
  final LayerLink layerDateLink;
  final AsyncValue<Shipment?> shipment;
  final List<Document> documents;

  const ShipmentState({
    required this.shipments,
    this.shipmentStatusOptions = const [
      "All shipments",
      "Pending",
      "Processing",
      "Dispatched",
      "Delivered"
    ],
    required this.fetchShipmentsParams,
    this.selectedStartDate,
    this.selectedEndDate,
    required this.layerDateLink,
    required this.shipment,
    required this.documents,
  });

  factory ShipmentState.initial() {
    return ShipmentState(
      shipments: const AsyncData([]),
      fetchShipmentsParams: FetchShipmentsParam.defaultValue(),
      layerDateLink: LayerLink(),
      shipment: const AsyncData(null),
      documents: const [],
    );
  }

  @override
  List<Object?> get props => [
        shipments,
        fetchShipmentsParams,
        selectedStartDate,
        selectedEndDate,
        layerDateLink,
        shipment,
        documents,
      ];

  ShipmentState copyWith({
    AsyncValue<List<Shipment>>? shipments,
    FetchShipmentsParam? fetchShipmentsParams,
    Optional<DateTime?>? selectedStartDate,
    Optional<DateTime?>? selectedEndDate,
    AsyncValue<Shipment>? shipment,
    List<Document>? documents,
  }) {
    return ShipmentState(
        shipments: shipments ?? this.shipments,
        fetchShipmentsParams: fetchShipmentsParams ?? this.fetchShipmentsParams,
        selectedStartDate: selectedStartDate != null
            ? selectedStartDate.value
            : this.selectedStartDate,
        selectedEndDate: selectedEndDate != null
            ? selectedEndDate.value
            : this.selectedEndDate,
        layerDateLink: layerDateLink,
        shipment: shipment ?? this.shipment,
        documents: documents ?? this.documents);
  }
}
