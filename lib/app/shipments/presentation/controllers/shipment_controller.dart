import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/auth/presentation/controllers/session_controller.dart';
import 'package:td_procurement/app/shipments/domain/entities/shipment_params.dart';
import 'package:td_procurement/app/shipments/domain/use_cases/shipment_use_cases.dart';
import 'package:td_procurement/app/shipments/presentation/controllers/shipment_state.dart';
import 'package:td_procurement/core/models/optional.dart';

final shipmentControllerProvider =
    NotifierProvider<ShipmentController, ShipmentState>(
        () => ShipmentController());

class ShipmentController extends Notifier<ShipmentState> {
  @override
  ShipmentState build() {
    final session = ref.watch(sessionController);

    // If accessToken is null or empty (logged out), return initial state
    if (session.accessToken == null || session.accessToken!.isEmpty) {
      return ShipmentState.initial();
    }

    return ShipmentState.initial();
  }

  Future<void> fetchShipments(
    FetchShipmentsParam params, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    state = state.copyWith(
        fetchShipmentsParams: state.fetchShipmentsParams
            .copyWith(activeIndex: params.activeIndex));

    if (forced) {
      state = state.copyWith(shipments: const AsyncLoading());
    } else {
      if (params.currentPage == 1 &&
          (state.shipments.hasValue && state.shipments.value!.isNotEmpty)) {
        return;
      }

      // return if query has reached the max page or if loading or loading more orders
      if (state.fetchShipmentsParams.currentPage ==
              state.fetchShipmentsParams.totalPages ||
          state.shipments.isLoading ||
          state.fetchShipmentsParams.loadingMore) {
        return;
      }

      if (state.fetchShipmentsParams.loaded) {
        state = state.copyWith(
            fetchShipmentsParams:
                state.fetchShipmentsParams.copyWith(loadingMore: true));
      } else {
        state = state.copyWith(shipments: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await ref.read(fetchShipmentsUseCaseProvider).call(params);

      res.when(
        success: (data) {
          state = state.copyWith(
            shipments:
                AsyncData([...?state.shipments.value, ...data.shipments]),
            fetchShipmentsParams: params
                .fromQueryParams(data.queryParams)
                .copyWith(loaded: true, loadingMore: false),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            if (!state.fetchShipmentsParams.loaded) {
              state = state.copyWith(
                  shipments: AsyncError(e, StackTrace.current),
                  fetchShipmentsParams: state.fetchShipmentsParams
                      .copyWith(loaded: false, loadingMore: false));
            }
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> getShipment(
    String shippingId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(shipment: const AsyncLoading());
    } else {
      if (state.shipment.valueOrNull?.id == shippingId) {
        return;
      } else {
        state = state.copyWith(shipment: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await ref.read(getShipmentUseCaseProvider).call(shippingId);

      res.when(
        success: (data) {
          state = state.copyWith(shipment: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(shipment: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchDocuments(
    String shippingId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (state.shipment.valueOrNull?.id == shippingId) {
      return;
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res =
          await ref.read(fetchDocumentsUseCaseProvider).call(shippingId);

      res.when(
        success: (data) {
          state = state.copyWith(documents: data);
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  void setDateFilter(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
        selectedStartDate: Optional(startDate),
        selectedEndDate: Optional(endDate));
  }
}
