import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_action_bar.dart';
import 'package:td_procurement/app/order/presentation/widgets/table_summary_row.dart';
import 'package:td_procurement/app/shipments/domain/use_cases/shipment_use_cases.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/app_values/app_values.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class InvoiceDetailsWidget extends ConsumerWidget {
  InvoiceDetailsWidget(this.invoice, {super.key, this.isCloseIcon = false});

  final Invoice invoice;
  final bool isCloseIcon;

  final isLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (context, loading, _) {
            return OrderActionBarWidget(
              leftText: '${invoice.invoiceType} Invoice Summary',
              isCloseIcon: isCloseIcon,
              leftIconAction: () => Navigator.pop(context),
              rightButton1Text: 'Print',
              rightButton1Loading: loading,
              rightButton1Disabled: loading,
              rightButton1Action:
                  loading ? null : () => handlePrint(context, ref),
            );
          },
        ),
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverFillRemaining(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Gap(10),
                      Text(
                        'Invoice #${invoice.invoiceNumber}',
                        style: textTheme.titleMedium,
                      ),
                      const Gap(10),
                      Row(
                        children: [
                          Text(
                            'Due ${invoice.createdAt?.toFullDate()}',
                            style: textTheme.bodyMedium?.copyWith(
                              color: Palette.blackSecondary,
                            ),
                          ),
                          const Gap(6),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color:
                                  HexColor('#117BFF').withValues(alpha: 0.07),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              invoice.status ?? '-',
                              style: textTheme.bodyMedium?.copyWith(
                                color: HexColor('#117BFF'),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Gap(10),
                      Row(
                        children: [
                          Text(
                            'Deliver To',
                            style: textTheme.bodyMedium?.copyWith(
                              color: Palette.blackSecondary,
                            ),
                          ),
                          const Gap(20),
                          Text(
                            invoice.shippingAddress?.address1?.toUpperCase() ??
                                '-',
                            style: textTheme.bodyMedium,
                          ),
                        ],
                      ),
                      const Gap(20),
                      _buildTableHeader(textTheme),
                      _buildTableRows(context, outlet, textTheme),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> handlePrint(BuildContext context, WidgetRef ref) async {
    final orderId = (invoice.orderIds != null && invoice.orderIds!.isNotEmpty)
        ? invoice.orderIds!.first
        : '';

    if (orderId.isEmpty) return;

    isLoading.value = true;

    final res = await ref.read(getInvoiceUrlUseCaseProvider(orderId));

    res.when(
      success: (invoiceUrl) {
        isLoading.value = false;
        openUri(invoiceUrl);
      },
      failure: (error, _) {
        isLoading.value = false;
        Toast.apiError(error, context);
      },
    );
  }

  Widget _buildTableHeader(TextTheme textTheme) {
    final options = [
      'Description',
      'Qty',
      'Unit Price',
      'Discount',
      'Tax',
      'Amount'
    ];
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Palette.stroke))),
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(4),
          1: FlexColumnWidth(1),
          2: FlexColumnWidth(1.2),
          3: FlexColumnWidth(1.2),
          4: FlexColumnWidth(0.5),
          5: FlexColumnWidth(1.2),
        },
        children: [
          TableRow(
            children: [
              ...mapIndexed(
                  options,
                  (index, item) => _buildHeaderCell(
                      item, textTheme, index == options.length - 1))
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme, bool isLast) {
    return Text(
      text,
      style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      textAlign: isLast ? TextAlign.right : TextAlign.left,
    );
  }

  Widget _buildTableRows(
      BuildContext context, RetailOutlet? outlet, TextTheme textTheme) {
    final items = invoice.items ?? [];
    final currencyCode = invoice.currency?.symbol ?? defaultCurrency.iso!;

    return Column(
      children: [
        Table(
          columnWidths: const {
            0: FlexColumnWidth(4),
            1: FlexColumnWidth(1),
            2: FlexColumnWidth(1.2),
            3: FlexColumnWidth(1.2),
            4: FlexColumnWidth(0.5),
            5: FlexColumnWidth(1.2),
          },
          children: [
            if (items.isEmpty)
              const TableRow(
                children: [
                  SizedBox(),
                  SizedBox(),
                  SizedBox(),
                  SizedBox(),
                  SizedBox(),
                  SizedBox(),
                ],
              ),
            ...mapIndexed(
              items,
              (index, item) => TableRow(
                decoration: BoxDecoration(
                  border: index == items.length - 1
                      ? null
                      : Border(
                          bottom: BorderSide(color: Palette.stroke),
                        ),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Text(
                      item.name ?? '-',
                      style: textTheme.bodyMedium,
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                    child: Text(
                      item.quantity?.toString() ?? '-',
                      style: textTheme.bodyMedium,
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                    child: CurrencyWidget(item.price ?? 0, currencyCode),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                    child: CurrencyWidget(item.discount ?? 0, currencyCode),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                    child: CurrencyWidget(item.tax ?? 0, currencyCode),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: CurrencyWidget(
                          ((item.price ?? 0) * (item.quantity ?? 0)),
                          currencyCode),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
        const Gap(20),
        TableSummaryRow(
          'Subtotal',
          CurrencyWidget.value(context, currencyCode, invoice.subTotal ?? 0),
          flex: 4,
        ),
        // const TableSummaryRow('VAT (20%)', '£0.00'),
        TableSummaryRow(
          'Discounts',
          CurrencyWidget.value(
              context, currencyCode, invoice.shippingDiscount ?? 0),
          flex: 4,
        ),
        TableSummaryRow('Taxes',
            CurrencyWidget.value(context, currencyCode, invoice.taxes ?? 0),
            flex: 4),
        TableSummaryRow(
            'Shipping Costs',
            CurrencyWidget.value(
                context, currencyCode, invoice.shippingCosts ?? 0),
            flex: 4),
        const Gap(4),
        TableSummaryRow('Amount Due',
            CurrencyWidget.value(context, currencyCode, invoice.total ?? 0),
            useBoldFontWeight: true, flex: 4),
        // const Spacer(),
        const Gap(50),
        Container(
          width: double.maxFinite,
          decoration: BoxDecoration(
              border: Border.all(color: Palette.stroke),
              borderRadius: BorderRadius.circular(10)),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How to pay',
                  style: textTheme.bodyLarge?.copyWith(
                    color: Palette.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text.rich(
                  TextSpan(text: '', children: [
                    TextSpan(
                      text:
                          'Pay ${CurrencyWidget.value(context, currencyCode, invoice.total ?? 0)}',
                      style: textTheme.titleMedium,
                    ),
                    TextSpan(
                      text: ' with bank transfer',
                      style: textTheme.titleMedium?.copyWith(
                        // fontWeight: FontWeight.w500,
                        color: Palette.blackSecondary,
                      ),
                    ),
                  ]),
                ),
                Text(
                  'Transfer funds using the following bank information.',
                  style: textTheme.bodyMedium?.copyWith(
                      color: Palette.blackSecondary,
                      fontWeight: FontWeight.w500),
                ),
                Divider(
                  height: 40,
                  color: Palette.stroke,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Account Number',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      invoice.bankAccount?.accountNumber ??
                          outlet?.walletAccount?.colAccountNumber ??
                          '-',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const Gap(5),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Bank Name',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      invoice.bankAccount?.bankName ??
                          outlet?.walletAccount?.colBankName ??
                          '-',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
