import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/app/shipments/presentation/controllers/shipment_controller.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/models/document.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:url_launcher/url_launcher.dart';

class DocumentsWidget extends ConsumerWidget {
  const DocumentsWidget(this.shipment, {super.key});

  final Shipment shipment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    final documents = ref.watch(shipmentControllerProvider).documents;
    final consoleUrl = ref.read(appConfigProvider).consoleUrl;

    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
          border: Border.all(color: Palette.stroke),
          borderRadius: BorderRadius.circular(10)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
            child: Text(
              'Documents',
              style: textTheme.headlineSmall,
            ),
          ),
          const Gap(16),
          _buildTableHeader(context),
          _buildTableRow(documents, false, textTheme, consoleUrl, context),
        ],
      ),
    );
  }

  Widget _buildTableHeader(context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: Palette.kF7F7F7,
      // height: 36,
      padding: const EdgeInsets.symmetric(vertical: 10),
      alignment: Alignment.center,
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(3),
          1: FlexColumnWidth(6),
        },
        children: [
          TableRow(
            children: [
              _buildHeaderCell('Name', textTheme),
              Container(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildTableRow(
    List<Document> documents,
    bool isHovered,
    TextTheme textTheme,
    String consoleUrl,
    BuildContext context,
  ) {
    return Table(
      columnWidths: const {
        0: FlexColumnWidth(3),
        1: FlexColumnWidth(6),
      },
      children: [
        ...documents.map((el) {
          return TableRow(
            decoration: BoxDecoration(
                border: Border(
              bottom: BorderSide(color: Palette.stroke),
            )),
            children: [
              _buildContentCell(
                  '${el.documentType}.${el.extension}', textTheme),
              _buildContentCell(
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                          onTap: () =>
                              handleDocumentDownload(el, consoleUrl, context),
                          child: SvgPicture.asset(
                              '$kSvgDir/shipment/download.svg')),
                    ],
                  ),
                  textTheme),
            ],
          );
        })
      ],
    );
  }

  Widget _buildContentCell(
    dynamic content,
    TextTheme textTheme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: content is String
          ? Text(
              content,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            )
          : content,
    );
  }

  Future<void> handleDocumentDownload(
      Document doc, String consoleUrl, BuildContext context) async {
    final url = '$consoleUrl/api/v3/shipment/${doc.id}/download';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      if (context.mounted) {
        Toast.error(
            'Unable to download this document at this time. Please try again later',
            context);
      }
    }
  }
}
