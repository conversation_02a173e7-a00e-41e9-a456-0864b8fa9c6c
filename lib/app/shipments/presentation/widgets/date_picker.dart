import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:td_procurement/app/shipments/presentation/controllers/shipment_controller.dart';
import 'package:td_procurement/core/models/sized_item.dart';
import 'package:td_procurement/src/components/widgets/multi_select_date_picker.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

typedef DatesSelectedCallback = void Function(
    {DateTime? startDate, DateTime? endDate});

class DatePickerWidget extends ConsumerStatefulWidget {
  final DateTime? initialDate;
  final DateTime? selectedStartDate;
  final DateTime? selectedEndDate;

  /// returns the [startSate] and [endDate]
  final DatesSelectedCallback onDatesSelected;
  final VoidCallback onCancel;
  final String label;
  final Function() getValueLabel;
  final Decoration? decoration;
  final Offset? offset;

  const DatePickerWidget({
    super.key,
    required this.onDatesSelected,
    required this.onCancel,
    this.initialDate,
    this.selectedStartDate,
    this.selectedEndDate,
    this.label = 'Select Date',
    required this.getValueLabel,
    this.decoration,
    this.offset,
  });

  @override
  ConsumerState<DatePickerWidget> createState() => _DatePickerWidgetState();
}

class _DatePickerWidgetState extends ConsumerState<DatePickerWidget> {
  OverlayEntry? _overlayEntry;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    const double width = 100;

    final shipmentState = ref.watch(shipmentControllerProvider);

    return InkWell(
      onTap: () =>
          _showDatePickerWidgetOverlay(context, shipmentState.layerDateLink),
      child: CompositedTransformTarget(
        link: shipmentState.layerDateLink,
        child: IntrinsicWidth(
          child: Container(
            constraints: const BoxConstraints(
              minWidth: width,
            ),
            height: kOrderOptionsHeight,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: widget.decoration ??
                BoxDecoration(
                  border: Border.all(color: Palette.stroke),
                  borderRadius: BorderRadius.circular(8),
                ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      widget.getValueLabel(),
                      style: textTheme.bodyMedium,
                    ),
                  ),
                ),
                SvgPicture.asset('$kSvgDir/order/calender.svg'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Show overlay for date picker
  void _showDatePickerWidgetOverlay(
      BuildContext context, LayerLink layerDateLink) {
    _overlayEntry = createOverlayEntry(
      context,
      size: SizedItem(
          width: 620, height: 400, top: 0.15, right: 0.012), // w580, h383
      layerLink: layerDateLink,
      onCloseOverlay: () {
        _closeOverlay();
      },
      child: _buildDatePickerWidget(),
      offset: widget.offset,
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  Widget _buildDatePickerWidget() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: MultiDatePicker(
        initialDate: widget.initialDate,
        selectedStartDate: widget.selectedStartDate,
        selectedEndDate: widget.selectedEndDate,
        onSelectRange: (startDate, endDate) async {
          _closeOverlay();
          widget.onDatesSelected(startDate: startDate, endDate: endDate);
        },
        onCancel: () async {
          _closeOverlay();
          widget.onCancel();
        },
      ),
    );
  }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

String getFormattedDateRange(DateTime? start, DateTime? end,
    [String? defaultLabel]) {
  if (start == null && end == null) {
    return defaultLabel ?? 'Filter By Date';
  }

  start ??= end;
  end ??= start;

  final isSameDay = start!.day == end!.day;
  final startDateFormat = (start.year == end.year) ? 'MMM d' : 'MMM d, yyyy';
  const endDateFormat = 'MMM d, yyyy';

  final formattedStart = DateFormat(startDateFormat).format(start);
  final formattedEnd = DateFormat(endDateFormat).format(end);
  final formattedSingleDay = DateFormat(endDateFormat).format(start);

  return isSameDay ? formattedSingleDay : '$formattedStart - $formattedEnd';
}
