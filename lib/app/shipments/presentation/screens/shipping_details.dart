import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/app/shipments/domain/entities/shipment_params.dart';
import 'package:td_procurement/app/shipments/presentation/controllers/shipment_controller.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/documents.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/information.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/shipment_items.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/tile.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

enum TabDetail { informations, documents, items }

class ShippingDetails extends ConsumerStatefulWidget {
  const ShippingDetails(this.shippingId, {super.key, this.isCloseIcon = false});

  final String shippingId;
  final bool isCloseIcon;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ShippingDetailsState();
}

class _ShippingDetailsState extends ConsumerState<ShippingDetails> {
  late final String shippingId = widget.shippingId;
  TabDetail selectedTab = TabDetail.informations;

  @override
  void initState() {
    super.initState();
    _fetchInitialData();
  }

  void _fetchInitialData() {
    Future.microtask(() {
      final shipmentNotifier = ref.read(shipmentControllerProvider.notifier);
      shipmentNotifier.getShipment(shippingId);
      shipmentNotifier.fetchDocuments(shippingId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final shipmentState = ref.watch(shipmentControllerProvider);

    return shipmentState.shipment.when(
      data: (shipment) => _buildShipmentDetails(shipment!),
      error: (error, stackTrace) => FailureWidget(
          fullScreen: true,
          heightFactor: 0.7,
          e: error,
          retry: _fetchInitialData),
      loading: () => Skeletonizer(
          enabled: true, child: _buildShipmentDetails(Shipment.defaultValue())),
    );
  }

  Widget _buildShipmentDetails(Shipment shipment) {
    final textTheme = Theme.of(context).textTheme;
    final shipmentNumber = shipment.shipmentNumber?.toString() ?? '';
    final trackingStatuses = getTrackingStatuses(shipment);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(shipmentNumber, textTheme),
        _buildShipmentInfo(shipmentNumber, shipment, textTheme),
        const Gap(10),
        Divider(color: Palette.stroke, height: 0),
        Expanded(
          child: ListView(
            children: [
              IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 60),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMainContent(shipment, textTheme),
                      _buildTrackingTimeline(trackingStatuses, textTheme),
                    ],
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildHeader(String shipmentNumber, TextTheme textTheme) {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 40),
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
      ),
      child: Row(
        children: [
          Flexible(
            child: IconButton(
              icon: Skeleton.shade(
                  child: SvgPicture.asset(
                      widget.isCloseIcon ? kCloseSvg : kSvgArrowBackIcon)),
              onPressed: () => context.pop(),
            ),
          ),
          const Gap(10),
          Flexible(
            child: InkWell(
              focusColor: Colors.transparent,
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              hoverColor: Colors.transparent,
              onTap: () => context.pop(),
              child: Text(
                'Shipments',
                style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
              ),
            ),
          ),
          const Gap(16),
          Skeleton.shade(
            child: SvgPicture.asset('$kSvgDir/packs/chevron_right.svg',
                width: 14, height: 12),
          ),
          const Gap(10),
          Flexible(
            child: Text(
              shipmentNumber.toString(),
              style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipmentInfo(
      String shipmentNumber, Shipment shipment, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 60),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Gap(20),
          Text(
            shipmentNumber.toString(),
            style: textTheme.headlineSmall,
          ),
          const Gap(20),
          Flexible(
            child: Wrap(
              spacing: 30,
              runSpacing: 10,
              children: [
                TileWidget(
                  title: 'SHIP TO'.toUpperCase(),
                  subtitle: shipment.shippingAddress?.address1,
                ),
                TileWidget(
                  title: 'BILL OF LADING'.toUpperCase(),
                  subtitle: shipment.shippingDetails?.billOfLading,
                ),
                TileWidget(
                  title: 'STATUS'.toUpperCase(),
                  subtitle: shipment.shippingStatus,
                ),
                TileWidget(
                  title: 'CARRIER'.toUpperCase(),
                  subtitle: shipment.shippingDetails?.carrierName,
                ),
                TileWidget(
                  title: 'TRACKING URL'.toUpperCase(),
                  subtitle: shipment.trackingUrl,
                  hyperLink: true,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildMainContent(Shipment shipment, TextTheme textTheme) {
    return Expanded(
      flex: 3,
      child: Column(
        children: [
          const Gap(20),
          _buildTabBar(textTheme),
          const Gap(20),
          _buildTabContent(shipment),
        ],
      ),
    );
  }

  Widget _buildTabBar(TextTheme textTheme) {
    return Row(
      children: [
        ...TabDetail.values.map((tab) {
          final isSelected = tab == selectedTab;
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: OutlinedButton(
              onPressed: () => setState(() => selectedTab = tab),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                    color: isSelected ? Palette.primaryBlack : Palette.stroke,
                    width: isSelected ? 2 : 1),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              ),
              child: Text(
                capitalize(tab.name),
                style: textTheme.bodyMedium?.copyWith(
                  color: isSelected
                      ? Palette.primaryBlack
                      : Palette.blackSecondary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          );
        })
      ],
    );
  }

  Widget _buildTabContent(Shipment shipment) {
    return switch (selectedTab) {
      TabDetail.informations => InformationWidget(shipment),
      TabDetail.documents => DocumentsWidget(shipment),
      TabDetail.items => ShipmentItemsWidget(shipment),
    };
  }

  Widget _buildTrackingTimeline(
      List<TrackingStatus> trackingStatuses, TextTheme textTheme) {
    return Expanded(
      flex: 2,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 16, left: 14),
                    child: Text(
                      'Tracking & Alerts',
                      style: textTheme.headlineSmall,
                    ),
                  ),
                  const Gap(20),
                  ...trackingStatuses.mapIndexed((index, tracking) {
                    return _buildTrackingStep(
                        tracking, index, trackingStatuses, textTheme);
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackingStep(TrackingStatus tracking, int index,
      List<TrackingStatus> allStatuses, TextTheme textTheme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.only(left: 15, bottom: 15),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          Skeleton.shade(
            child: SvgPicture.asset(
              '$kSvgDir/shipment/circle.svg',
              colorFilter: ColorFilter.mode(
                  tracking.completed
                      ? Colors.green
                      : tracking.pending
                          ? Colors.orange
                          : Colors.grey,
                  BlendMode.srcIn),
            ),
          ),
          const Gap(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tracking.status,
                  style: textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (tracking.createdAt != null) ...[
                  const Gap(4),
                  Row(
                    children: [
                      Skeleton.shade(
                        child:
                            SvgPicture.asset('$kSvgDir/shipment/calendar.svg'),
                      ),
                      const Gap(4),
                      Text(
                        tracking.createdAt?.toFullDateTime() ?? '-',
                        style: textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
                if (tracking.completed)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      'Shipment ${tracking.status.toLowerCase()}'
                      '${index > 0 ? ' and confirmed for ${allStatuses[index - 1].status.toLowerCase()}' : ''}',
                      style: textTheme.bodySmall,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<TrackingStatus> getTrackingStatuses(Shipment shipment) {
    final history = shipment.history;
    final shippingStatus = shipment.shippingStatus?.toLowerCase();

    if (history == null || shippingStatus == null) return [];

    final statuses = [
      "Open",
      "Processing",
      "Ready",
      "Scheduled",
      "Dispatched",
      "Skipped",
      "Delivered",
    ].map((status) => TrackingStatus(status: status)).toList();

    // Set initial Open status
    statuses[0] = TrackingStatus(
      status: "Open",
      completed: true,
      pending: false,
      createdAt: shipment.createdAt,
    );

    // Update statuses based on history
    void updateStatus(int index, String statusName) {
      final event = history.firstWhereOrNull(
          (event) => event.newValue?.toLowerCase() == statusName.toLowerCase());

      if (event != null) {
        statuses[index] = TrackingStatus(
          status: statusName,
          completed: true,
          createdAt: event.createdAt,
        );

        if (shippingStatus == statusName.toLowerCase()) {
          final nextIndex = index + 1;
          if (nextIndex < statuses.length) {
            statuses[nextIndex] = statuses[nextIndex].copyWith(pending: true);
          }
        }
      }
    }

    updateStatus(1, "Processing");
    updateStatus(2, "Ready");
    updateStatus(3, "Scheduled");
    updateStatus(4, "Dispatched");
    updateStatus(5, "Skipped");
    updateStatus(6, "Delivered");

    // Handle skipped status special case
    if (shippingStatus == "skipped") {
      statuses[6] = statuses[6].copyWith(pending: true);
    }

    return statuses.reversed.toList();
  }
}
