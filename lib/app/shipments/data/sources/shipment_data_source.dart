import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/shipments/domain/entities/shipment_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/models/document.dart';
import 'package:td_procurement/core/models/query_parameters.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';
import 'package:td_procurement/src/utils/shipment_utils.dart';

final shipmentDataSourceProvider =
    Provider.autoDispose<ShipmentDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  final config = ref.read(appConfigProvider);

  return ShipmentDataSourceImplementation(apiClient, config);
});

abstract class ShipmentDataSource {
  Future<FetchShipmentsResponse> fetchShipments(FetchShipmentsParam params);
  Future<Shipment> getShipment(String shippingId);
  Future<List<Document>> fetchDocuments(String shippingId);
  Future<Invoice> getInvoice(String orderId);
  Future<String> getInvoiceUrl(String orderId);
}

class ShipmentDataSourceImplementation implements ShipmentDataSource {
  final TdApiClient _apiClient;
  final AppConfig _config;

  ShipmentDataSourceImplementation(this._apiClient, this._config);

  @override
  Future<FetchShipmentsResponse> fetchShipments(
      FetchShipmentsParam params) async {
    final queryString = getQueryString(params);

    final res = await _apiClient.get(
        '${_config.consoleUrl}${kShipmentsApiPath.replaceFirst(':queryString', queryString)}');

    final data = res.data;
    final List<dynamic> shipments = data['data'];

    final perPage = params.perPage;
    final total = data?['total'] ?? params.totalPages;

    final Map<String, dynamic> pagination = {
      'page': data?['page'] ?? 1,
      'totalPages': ((total + perPage - 1) / perPage).floor(),
      'perPage': perPage
    };

    return FetchShipmentsResponse(
      shipments: shipments.map((x) => Shipment.fromMap(x)).toList(),
      queryParams: QueryParameters.fromMap(pagination),
    );
  }

  @override
  Future<Shipment> getShipment(String shippingId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kShipmentPath.replaceFirst(':id', shippingId)}');
    return Shipment.fromMap(res.data['data']);
  }

  @override
  Future<List<Document>> fetchDocuments(String shippingId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kDocumentsApiPath.replaceFirst(':id', shippingId)}');

    final List<dynamic> documentList = res.data;

    return documentList.map((x) => Document.fromMap(x)).toList();
  }

  @override
  Future<Invoice> getInvoice(String orderId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kInvoiceApiPath.replaceFirst(':orderId', orderId)}');
    return Invoice.fromMap(res.data['data']['invoice']);
  }

  @override
  Future<String> getInvoiceUrl(String orderId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kInvoiceApiPath.replaceFirst(':orderId', orderId)}');
    return res.data['data']['invoiceUrl'];
  }
}
