import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/shell/presentation/widgets/side_bar.dart';
import 'package:td_procurement/app/shell/presentation/widgets/top_bar.dart';
import 'package:td_procurement/src/components/flexible_box/flexible_box.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class ConsoleShell extends ConsumerStatefulWidget {
  final Widget child;

  const ConsoleShell(this.child, {super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _AppShell();
  }
}

class _AppShell extends ConsumerState<ConsoleShell> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: FlexibleConstrainedBox(
          physics: const NeverScrollableScrollPhysics(),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ScrollConfiguration(
                behavior: ScrollConfiguration.of(context).copyWith(
                  scrollbars: false,
                  physics: const ClampingScrollPhysics(),
                ),
                child: const SideBar(),
              ),
              Expanded(
                child: Column(
                  children: [
                    const TopBar(),
                    // const Gap(10),
                    Expanded(
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(16),
                              ),
                              border: Border.all(color: Palette.stroke),
                              color: Colors.white),
                          child: widget.child,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
