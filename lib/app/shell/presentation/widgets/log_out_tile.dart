import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

class LogOutTile extends ConsumerWidget {
  const LogOutTile({super.key});

  @override
  Widget build(BuildContext context, ref) {
    final textTheme = Theme.of(context).textTheme;
    final isExtended = MediaQuery.of(context).size.width > kSideBarMinWidth;
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: InkWell(
        onTap: () {
          resetCart(ref);
          ref.read(userControllerProvider.notifier).onLogout();
        },
        child: Container(
          constraints: BoxConstraints.loose(
            Size(isExtended ? 194 : 70, 55),
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.transparent,
          ),
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                kLogOutSvg,
                colorFilter:
                    ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
              ),
              if (isExtended) ...[
                const Gap(10),
                Text(
                  'Log Out',
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ]
            ],
          ),
        ),
      ),
    );
  }
}
