import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/shell/data/models/global_search.model.dart';
import 'package:td_procurement/core/DI/di_providers.dart';

class ShellDataSourceImplementation extends ShellDataSource {
  final Ref _ref;
  ShellDataSourceImplementation(this._ref);

  late final TdApiClient _apiClient = _ref.read(apiClientProvider);
  late final config = _ref.read(appConfigProvider);

  @override
  Future<GlobalSearch> globalSearch(String params) async {
    final res = await _apiClient.post(
      "${config.searchUrl}/global-search",
      data: {
        "searchTerm": params,
        "query": {"limit": 3}
      },
    );
    return GlobalSearch.fromJson(res.data['body']);
  }
}

abstract class ShellDataSource {
  Future<GlobalSearch> globalSearch(String params);
}

final shellProvider = Provider<ShellDataSource>((ref) {
  return ShellDataSourceImplementation(ref);
});
