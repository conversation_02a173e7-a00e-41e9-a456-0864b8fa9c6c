import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/auth/domain/use_cases/auth_use_cases.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

class DeleteAccount extends ConsumerStatefulWidget {
  const DeleteAccount({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _DeleteAccount();
  }
}

class _DeleteAccount extends ConsumerState<DeleteAccount> {
  final ValueNotifier<DeleteAccountView> _view =
      ValueNotifier(DeleteAccountView.confirm);
  final ValueNotifier<bool> _loader = ValueNotifier(false);

  @override
  void dispose() {
    _view.dispose();
    _loader.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: context.pop,
                icon: const Icon(Icons.close_outlined),
              ),
              const Gap(20),
              Text(
                'Delete account',
                style: textTheme.bodyLarge?.copyWith(
                    color: Palette.k6B797C, fontWeight: FontWeight.w500),
              ),
            ],
          ),
          const Gap(20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 80, vertical: 50),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Palette.primary,
                  child: SvgPicture.asset(kDeleteSvg),
                ),
                const Gap(20),
                ValueListenableBuilder(
                    valueListenable: _view,
                    builder: (context, state, _) {
                      return switch (state) {
                        DeleteAccountView.confirm => Column(
                            children: [
                              Text(
                                'Are you sure you want to delete your account?',
                                style: textTheme.headlineMedium,
                              ),
                              const Gap(5),
                              Text(
                                'Deleting your account will erase your data and you will no long have access to TradeDepot [wholesale]',
                                style: textTheme.bodyLarge
                                    ?.copyWith(color: Palette.blackSecondary),
                              ),
                              const Gap(30),
                              FilledButton(
                                onPressed: () =>
                                    _view.value = DeleteAccountView.info,
                                style: FilledButton.styleFrom(
                                  minimumSize: const Size(double.infinity, 49),
                                ),
                                child: const Text(
                                    'Yes, I want to delete my account'),
                              ),
                              const Gap(15),
                              TextButton(
                                onPressed: () {
                                  context.pop();
                                },
                                child: const Text('Cancel'),
                              )
                            ],
                          ),
                        DeleteAccountView.info => Column(
                            children: [
                              Text(
                                'Once your account is deleted, TradeDepot [Wholesale] won’t remember the information you might have shared including:',
                                style: textTheme.headlineMedium,
                              ),
                              const Gap(20),
                              const DeleteInfoTile('Your name'),
                              const DeleteInfoTile('Email address'),
                              const DeleteInfoTile('Phone number'),
                              const Gap(30),
                              CustomFilledButton(
                                loaderNotifier: _loader,
                                onPressed: deleteAccount,
                                style: FilledButton.styleFrom(
                                  minimumSize: const Size(double.infinity, 49),
                                ),
                                text: 'Delete',
                              ),
                              const Gap(15),
                              TextButton(
                                onPressed: () =>
                                    _view.value = DeleteAccountView.confirm,
                                child: const Text('Go back'),
                              ),
                              const Gap(30),
                              Text.rich(
                                TextSpan(
                                    text:
                                        "For more information about how we handle your data, see our ",
                                    style: textTheme.bodySmall?.copyWith(
                                        color: Palette.blackSecondary),
                                    children: [
                                      WidgetSpan(
                                          child: InkWell(
                                            onTap: () =>
                                                _launchUrl(kPrivacyPolicy),
                                            child: Text(
                                              'Privacy Statement',
                                              style: textTheme.bodySmall
                                                  ?.copyWith(
                                                      color: Palette.primary),
                                            ),
                                          ),
                                          baseline: TextBaseline.alphabetic,
                                          alignment:
                                              ui.PlaceholderAlignment.baseline),
                                    ]),
                              ),
                            ],
                          ),
                      };
                    }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void deleteAccount() async {
    _loader.value = true;
    final result = await ref.read(deleteUserAccountUseCaseProvider);
    _loader.value = false;
    result.when(
      success: (_) => ref.read(userControllerProvider.notifier).onLogout(),
      failure: (error, _) => Toast.apiError(error, context),
    );
  }

  Future<void> _launchUrl(String url) async {
    if (!await launchUrl(Uri.parse(url))) {
      if (mounted) {
        Toast.error('Could not launch $url', context);
      }
    }
  }
}

enum DeleteAccountView { confirm, info }

class DeleteInfoTile extends StatelessWidget {
  const DeleteInfoTile(this.information, {super.key});

  final String information;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.only(bottom: 15, left: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 7,
            backgroundColor: Palette.blackSecondary,
            child: const Icon(
              Icons.check_sharp,
              size: 9,
              color: Colors.white,
            ),
          ),
          const Gap(10),
          Flexible(
            child: Text(
              information,
              style:
                  textTheme.bodyMedium?.copyWith(color: Palette.blackSecondary),
            ),
          )
        ],
      ),
    );
  }
}
