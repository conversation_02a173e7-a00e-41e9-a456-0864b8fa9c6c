import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/settings/presentation/widgets/delete_account.dart';
import 'package:td_procurement/app/settings/presentation/widgets/disabled_textfield.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class ProfileInformation extends ConsumerWidget {
  final PageController controller;
  const ProfileInformation(this.controller, {super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final user = ref.read(userControllerProvider);
    return SizedBox(
      width: double.infinity,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Palette.kE7E7E7),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  InkWell(
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    child: Text(
                      'Settings',
                      style: textTheme.bodyMedium
                          ?.copyWith(color: Palette.k6B797C),
                    ),
                    onTap: () => controller.animateToPage(1,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.linear),
                  ),
                  const Gap(12),
                  SvgPicture.asset(kChevronRightSvg),
                  const Gap(12),
                  Text(
                    'Your profile',
                    style: textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                  )
                ],
              ),
              const Gap(18),
              Text('Profile Information', style: textTheme.headlineSmall),
              const Gap(20),
              Row(
                children: [
                  Expanded(
                    child: DisabledTextField(
                        label: 'First name', value: user?.firstName),
                  ),
                  const Gap(20),
                  Expanded(
                    child: DisabledTextField(
                        label: 'Last name', value: user?.lastName),
                  )
                ],
              ),
              const Gap(20),
              DisabledTextField(label: 'Email address', value: user?.email),
              const Gap(20),
              DisabledTextField(
                  label: 'Phone number', value: user?.phoneNumber),
              const Gap(20),
              DisabledTextField(
                  label: 'Outlet Type',
                  value: user?.currentRetailOutlet?.outletType),
              const Gap(20),
              ListTile(
                leading: SvgPicture.asset(kTrashSvg),
                title: Text(
                  'Delete account',
                  style: textTheme.bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w500),
                ),
                trailing: SvgPicture.asset(kChevronRightSvg),
                onTap: (){
                  showCustomGeneralDialog(
                    context,
                    dismissible: true,
                    percentage: 0.35,
                    child: const DeleteAccount(),
                  );
                },
              ),
              const Gap(5),
            ],
          ),
        ),
      ),
    );
  }
}
