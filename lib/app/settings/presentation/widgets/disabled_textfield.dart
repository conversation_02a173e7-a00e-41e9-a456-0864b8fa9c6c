import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class DisabledTextField extends StatelessWidget{
  final String label;
  final String? value;

  const DisabledTextField({super.key, required this.label, required this.value});
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label,style: textTheme.bodySmall?.copyWith(color: Palette.blackSecondary,fontWeight: FontWeight.w500),),
        const Gap(8),
        TextField(
          decoration: InputDecoration(filled: true,fillColor: Palette.kF7F7F7),
          controller: TextEditingController(text: value),
          enabled: false,
          style: textTheme.bodyMedium?.copyWith(color: Palette.blackSecondary,fontWeight: FontWeight.w500),
        ),
      ],
    );
  }
  
}