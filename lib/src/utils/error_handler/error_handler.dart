import 'package:dio/dio.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/env/.env.dart';
import 'package:td_procurement/src/utils/exceptions/td_exception.dart';

class ErrorHandler {
  /// Exception will be logged to console during debug mode
  /// and sent to Sentry during release mode
  ///

  static void report(
    e,
    StackTrace? s, {
    Map<String, dynamic>? hint,
  }) {
    String? environment = appConfig['FLUTTER_APP_FLAVOR'];
    if (e is TdException && hint == null) {
      hint = e.hint;
    }

    if (kDebugMode || environment == ENV.dev.name) {
      debugPrint('---------Sentry DebugMode-------');
      // only print to console during debug mode
      debugPrint(e.toString());
      debugPrint(s.toString());

      if (hint != null) {
        debugPrint('---------hint-------');
        debugPrint(hint.toString());
      }
      return;
    }

    if (e is DioException) {
      if (e.response == null || e.response?.data == null) return;

      hint ??= {
        'last': true,
        'request': e.requestOptions.path,
        'response': e.response?.data,
      };
    }

    final message = e.toString().toLowerCase();

    // don't report the following
    final items = [
      'connection',
      'timeout',
      'timedout',
      'connecting',
      'handshake',
      'host lookup',
      'network',
      'bad state',
      'bad gateway',
      '502 bad gateway',
      'unimplementederror',
      'cloud_firestore/permission-denied',
      'cloud_firestore/unavailable',
      'stateerror',
      'only valid value is 0: 4',
      "exception caught by image resource service",

      // web
      'minified',
      'on null',
      "field 'box' has not been initialized",
      'method not found',
      'future already completed',
      'invalid value',
      'notices'
    ];

    if (items.any((item) => message.contains(item))) return;
    hint?.removeWhere((key, value) => value == null);
    Sentry.captureException(
      e,
      stackTrace: s,
      hint: removeNullValues(hint).isEmpty ? null : Hint()
        ?..addAll(
          removeNullValues(hint),
        ),
    );
  }
}

Map<String, Object> removeNullValues(Map<String, dynamic>? params) {
  if (params == null) {
    return {};
  }
  return Map.fromEntries(
    params.entries
        .where((entry) => entry.value != null && entry.key.isNotEmpty)
        .map(
          (entry) => MapEntry(entry.key, entry.value),
        ),
  );
}
