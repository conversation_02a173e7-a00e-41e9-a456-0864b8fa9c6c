import 'package:flutter/cupertino.dart';
import 'package:td_procurement/procurement10n/app_localizations.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

mixin ErrorMapper {
  static fetchErrorMessage(String error, BuildContext context) {
    return AppLocalizations.of(context)?.error_description(error);
  }

  static String? fetchErrorTitle(String error, BuildContext context) {
    String? code = num.tryParse(error)?.toString();
    bool isNum = code != null;
    String acronym = getTitleAcronym(error);
    String? mappedError =
        AppLocalizations.of(context)?.error_title(isNum ? code : acronym);
    return mappedError == acronym || mappedError == code ? null : mappedError;
  }

  static bool isDefinedError(Object error) => error is String;

  static bool is404(Object error) => error is int;

  static String getTitleAcronym(String error) {
    return error.trim().isEmpty
        ? error
        : error.split(" ").fold("", (prev, current) {
            return "$prev${current[0].toUpperCase()}";
          });
  }

  static CustomAction? getCustomAction(String? error) {
    if (error == null) return null;
    return customActionableCodes
        .firstWhere((element) => element?.code.name == "c$error",
            orElse: () => null)
        ?.action;
  }

  static handleCustomError(
      ErrorAction func, AppException error, BuildContext context) {
    final message = error.error;
    final is404Error = is404(message);
    func(
      is404Error
          ? 'Oops Something Went Wrong!'
          : fetchErrorTitle(message as String, context),
      is404Error || message is! String
          ? kDefaultErrMsg
          : fetchErrorMessage(message, context),
    );
  }

  static final List<CustomCodeAction?> customActionableCodes = [];

/*  static void _displayIntercom() async {
    try {
      await Intercom.instance.displayMessenger();
    } catch (e) {
      ErrorHandler.report(
        e,
        StackTrace.current,
        hint: {
          'info': 'in app intercom error (displayMessenger)',
        },
      );
    }
  }*/
}

enum CustomActionCodes { c100 }

class CustomCodeAction {
  final CustomActionCodes code;
  final CustomAction action;
  CustomCodeAction(this.code, this.action);
}

typedef ErrorAction = void Function(String?, String);
