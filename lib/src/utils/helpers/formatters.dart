import 'package:flutter/services.dart';

class NoLeadingZeroFormatter extends TextInputFormatter {
  NoLeadingZeroFormatter();

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String newText = newValue.text;

    if (newText.startsWith('0') && newText.length > 1) {
      newText = newText.replaceFirst(RegExp(r'^0+'), '');
    }
    if (newText.isEmpty) {
      newText = '0';
    }

    return newValue.copyWith(text: newText);
  }
}

// class NoLeadingZeroFormatter extends TextInputFormatter {
//   NoLeadingZeroFormatter();

//   @override
//   TextEditingValue formatEditUpdate(
//       TextEditingValue oldValue, TextEditingValue newValue) {
//     String newText = newValue.text;
//     int baseOffset = newValue.selection.baseOffset;

//     // Remove leading zeros except a single zero when necessary
//     if (newText.startsWith('0') && newText.length > 1) {
//       newText = newText.replaceFirst(RegExp(r'^0+'), '');
//     }
//     if (newText.isEmpty) {
//       newText = '0';
//     }

//     // Ensure selection does not go beyond the new text length
//     int newOffset = newText.length < baseOffset ? newText.length : baseOffset;

//     return newValue.copyWith(
//       text: newText,
//       selection: TextSelection.collapsed(offset: newOffset),
//     );
//   }
// }

class SingleLeadingZeroFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String newText = newValue.text;
    int baseOffset = newValue.selection.baseOffset;

    // Ensure at most a single leading zero
    if (newText.length > 1 && newText.startsWith('00')) {
      newText = newText.replaceFirst(RegExp(r'^0+'), '0');
    }

    // Adjust selection to be within valid bounds
    int newOffset = newText.length < baseOffset ? newText.length : baseOffset;

    return newValue.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newOffset),
    );
  }
}

class RemoveDecimalTrailingZeroFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String newText = newValue.text;

    // Remove `.0` only if it's at the end
    if (RegExp(r'^\d+\.0$').hasMatch(newText)) {
      newText = newText.replaceFirst(RegExp(r'\.0$'), '');
    }

    return newValue.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}

class MaxIntValueTextInputFormatter extends TextInputFormatter {
  final int maxValue;
  final VoidCallback? onMaxValueExceeded;

  MaxIntValueTextInputFormatter(
      {required this.maxValue, this.onMaxValueExceeded});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isNotEmpty) {
      int enteredValue = int.tryParse(newValue.text) ?? 0;
      if (enteredValue > maxValue) {
        //  Execute the callback if provided
        if (onMaxValueExceeded != null) onMaxValueExceeded!();
        // Return the old value if the entered value exceeds the maximum
        return oldValue;
      }
    }
    return newValue;
  }
}

class MaxNumValueTextInputFormatter extends TextInputFormatter {
  final num maxValue;
  final VoidCallback? onMaxValueExceeded;

  MaxNumValueTextInputFormatter(
      {required this.maxValue, this.onMaxValueExceeded});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isNotEmpty) {
      num enteredValue = num.tryParse(newValue.text) ?? 0;
      if (enteredValue > maxValue) {
        //  Execute the callback if provided
        if (onMaxValueExceeded != null) onMaxValueExceeded!();
        // Return the old value if the entered value exceeds the maximum
        return oldValue;
      }
    }
    return newValue;
  }
}

class MaxValueTextInputFormatter<T extends num> extends TextInputFormatter {
  final T maxValue;
  final T? Function(String) parser;
  final VoidCallback? onMaxValueExceeded;

  MaxValueTextInputFormatter({
    required this.maxValue,
    required this.parser,
    this.onMaxValueExceeded,
  });

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isNotEmpty) {
      final T? enteredValue = parser(newValue.text);
      if (enteredValue != null && enteredValue > maxValue) {
        // Execute callback if provided
        onMaxValueExceeded?.call();
        // Revert to the old value if the entered value exceeds maxValue
        return oldValue;
      }
    }
    return newValue;
  }
}

class SingleDecimalPointInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // If the new text has more than one period, revert to the old value.
    if (newValue.text.split('.').length - 1 > 1) {
      return oldValue;
    }
    return newValue;
  }
}

class DecimalTextInputFormatter extends TextInputFormatter {
  final int decimalRange;
  final List<String> allowedDecimals;

  DecimalTextInputFormatter({
    required this.decimalRange,
    this.allowedDecimals = const [],
  }) : assert(decimalRange >= 0, 'Decimal range must be non-negative');

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final String text = newValue.text;

    // Allow empty text.
    if (text.isEmpty) {
      return newValue;
    }

    // Prevent more than one decimal point.
    if (text.split('.').length - 1 > 1) {
      return oldValue;
    }

    // If there's a decimal point, process the decimals.
    if (text.contains('.')) {
      final int decimalPointIndex = text.indexOf('.');
      final String decimals = text.substring(decimalPointIndex + 1);

      // If allowedDecimals list is provided, restrict to those values.
      if (allowedDecimals.isNotEmpty) {
        // Determine the maximum allowed length among the allowed decimal values.
        final int maxAllowedLength = allowedDecimals
            .map((s) => s.length)
            .reduce((a, b) => a > b ? a : b);

        // Reject if the entered decimals exceed the longest allowed decimal.
        if (decimals.length > maxAllowedLength) {
          return oldValue;
        }

        // Allow the user to type the decimal point without any digits yet.
        if (decimals.isNotEmpty) {
          // Check that the entered decimals are a prefix of one of the allowed values.
          final bool matchesPrefix = allowedDecimals.any(
            (allowed) => allowed.startsWith(decimals),
          );
          if (!matchesPrefix) {
            return oldValue;
          }
        }
      } else {
        // Otherwise, enforce a strict decimal range.
        if (decimals.length > decimalRange) {
          return oldValue;
        }
      }
    }

    return newValue;
  }
}

// class DecimalTextInputFormatter extends TextInputFormatter {
//   final int decimalRange;

//   DecimalTextInputFormatter({required this.decimalRange})
//       : assert(decimalRange >= 0, 'Decimal range must be non-negative');

//   @override
//   TextEditingValue formatEditUpdate(
//     TextEditingValue oldValue,
//     TextEditingValue newValue,
//   ) {
//     final String text = newValue.text;

//     // Allow empty text.
//     if (text.isEmpty) {
//       return newValue;
//     }

//     // Prevent more than one decimal point.
//     if (text.split('.').length - 1 > 1) {
//       return oldValue;
//     }

//     // If there's a decimal point, limit the digits after it.
//     if (text.contains('.')) {
//       final int index = text.indexOf('.');
//       final String decimals = text.substring(index + 1);

//       if (decimals.length > decimalRange) {
//         return oldValue;
//       }
//     }

//     return newValue;
//   }
// }
