import 'dart:math';

import 'package:dlibphonenumber/phone_number_util.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class Validators {
  /// Validates if a string is a valid email address using a regular expression
  static bool validateEmail(String value) {
    return RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value);
  }

  /// Validates and formats a phone number using libphonenumber
  /// Returns the formatted number if valid, null otherwise
  static Future<String?> validatePhoneNumber(
      String number, String countryCode) async {
    try {
      final PhoneNumberUtil phoneUtil = PhoneNumberUtil.instance;
      final num = phoneUtil.parse(number, countryCode);
      if (phoneUtil.isValidNumber(num)) {
        return "+${num.countryCode}${num.nationalNumber}";
      }
      return null;
    } catch (err) {
      return null;
    }
  }

  /// Validates password strength:
  /// - Must not be empty
  /// - Must be at least 8 characters
  /// - Must contain alphabets (not just numbers)
  static String? validatePassword(String? password) {
    RegExp numeric = RegExp(r'^-?\d+$');
    if (password?.isEmpty ?? true) {
      return 'Please provide your password';
    }
    if (password!.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (numeric.hasMatch(password)) {
      return 'Password must contain alphabets';
    }
    return null;
  }

  /// Returns a validator function that checks if two values match
  static String? Function(String?) validateDiffChange(
    String field, [
    String? error,
  ]) {
    return (String? value) {
      if (field != value) {
        return error ?? 'Values don\'t match';
      }
      return null;
    };
  }

  /// Validates that password confirmation matches the original password
  static String? confirmPassword(String? value, String passwordField) {
    if (value!.isEmpty) {
      return 'Please enter a password.';
    }
    return validateDiffChange(
      passwordField,
      'The passwords don\'t match',
    )(value);
  }

  /// Simple password presence validator
  static String? verifyPassword(String? value) {
    if (value!.isEmpty) {
      return 'Please enter a password';
    }
    return null;
  }

  /// Validates email format and presence
  static String? verifyEmail(String? value) {
    const errMsg = 'Please enter a valid email address';
    value = value?.trim();
    if (value?.isEmpty ?? true) {
      return errMsg;
    }
    return validateEmail(value!) ? null : errMsg;
  }

  /// Generic input validator that checks:
  /// - Input presence
  /// - Minimum length requirement
  static String? verifyInput(String? value,
      {String field = 'Field', int length = 1}) {
    value = value?.trim();
    if (value?.isEmpty ?? true) {
      return '$field is required';
    } else if (value!.length < length) {
      return '$length characters ${length > 1 ? 'are' : 'is'} required';
    }
    return null;
  }

  /// Input formatter that only allows alphanumeric characters and spaces
  static TextInputFormatter validInput() {
    return FilteringTextInputFormatter.allow(
      RegExp("[a-zA-Z0-9 ]"),
    );
  }

  /// Input formatter that only allows numbers and plus sign
  static TextInputFormatter validNumberInput() {
    return FilteringTextInputFormatter.allow(
      RegExp("[0-9+]"),
    );
  }

  /// Input formatter for decimal numbers with configurable decimal places
  /// Handles:
  /// - Single decimal point
  /// - No leading decimal point
  /// - Maximum decimal places
  static TextInputFormatter validDecimalNumberInput(
      [int maxDecimalPlaces = 2]) {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      final text = newValue.text;

      // Allow empty input
      if (text.isEmpty) return newValue;

      // Disallow multiple dots
      if ('.'.allMatches(text).length > 1) return oldValue;

      // Disallow leading dot without digit
      if (text == '.') return oldValue;

      // Validate format: digits, optional single dot, optional digits after dot
      final validFormat = RegExp(r'^\d*\.?\d*$');
      if (!validFormat.hasMatch(text)) return oldValue;

      // Limit to 2 decimal places if decimal is present
      if (text.contains('.')) {
        final parts = text.split('.');
        if (parts.length > 1 && parts[1].length > maxDecimalPlaces) {
          return oldValue;
        }
      }

      return newValue;
    });
  }

  /// Input formatter that prevents leading zeros in numbers
  static TextInputFormatter noLeadingZeros() {
    return TextInputFormatter.withFunction(
      (TextEditingValue oldValue, TextEditingValue newValue) {
        if (newValue.text.startsWith('0')) {
          return oldValue;
        }
        final regex = RegExp(r'^([1-9][0-9]*)?$');
        if (regex.hasMatch(newValue.text)) {
          return newValue;
        }
        return oldValue;
      },
    );
  }

  /// Input formatter that formats numbers as currency with:
  /// - Thousands separators
  /// - Configurable decimal places
  /// - Proper cursor position handling
  static TextInputFormatter formatCurrency({int decimalPlaces = 0}) {
    return TextInputFormatter.withFunction(
      (TextEditingValue oldValue, TextEditingValue newValue) {
        if (newValue.text.isEmpty) {
          return newValue.copyWith(text: '');
        } else if (newValue.text.compareTo(oldValue.text) != 0) {
          final int selectionIndexFromTheRight =
              newValue.text.length - newValue.selection.end;

          final formatter = NumberFormat("#,##0.${"#" * decimalPlaces}");
          final rawText =
              newValue.text.replaceAll(formatter.symbols.GROUP_SEP, '');

          String newString;

          if (rawText.contains('.')) {
            final parts = rawText.split('.');
            final decimalPart = parts[1];
            final truncatedDecimal = decimalPart.substring(
                0, min(decimalPlaces, decimalPart.length));
            final wholePart = double.tryParse(parts[0]) ?? 0.0;
            final formattedWhole = formatter.format(wholePart);
            newString = '$formattedWhole.$truncatedDecimal';
          } else {
            final numValue = int.tryParse(rawText) ?? 0;
            newString = formatter.format(numValue);
          }

          return TextEditingValue(
            text: newString,
            selection: TextSelection.collapsed(
                offset: newString.length - selectionIndexFromTheRight),
          );
        } else {
          return newValue;
        }
      },
    );
  }

  /// Input formatter that adds thousands separators to numbers
  /// while maintaining proper cursor position
  static TextInputFormatter formatInput() {
    return TextInputFormatter.withFunction(
      (TextEditingValue oldValue, TextEditingValue newValue) {
        if (newValue.text.isEmpty) {
          return newValue.copyWith(text: '');
        }

        final int selectionIndexFromTheRight =
            newValue.text.length - newValue.selection.end;

        final rawText = newValue.text.replaceAll(',', '');

        // If the input is not a valid number, keep the old value
        final int? value = int.tryParse(rawText);
        if (value == null) {
          return oldValue;
        }

        final formattedText = NumberFormat('#,###').format(value);

        return TextEditingValue(
          text: formattedText,
          selection: TextSelection.collapsed(
            offset: formattedText.length - selectionIndexFromTheRight,
          ),
        );
      },
    );
  }

  /// Input formatter that allows numbers only between min and max
 static TextInputFormatter formatRange(num min, num max) {
  return TextInputFormatter.withFunction(
    (TextEditingValue oldValue, TextEditingValue newValue) {
      final text = newValue.text;
      if (text.isEmpty) return newValue;

      final value = double.tryParse(text);
      if (value == null || value < min || value > max) {
        return oldValue; // Reject input outside the range
      }

      return newValue;
    },
  );
}

}
