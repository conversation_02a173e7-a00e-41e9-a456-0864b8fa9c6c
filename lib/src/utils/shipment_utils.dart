import 'package:td_procurement/app/shipments/domain/entities/shipment_params.dart';

String getQueryString(FetchShipmentsParam params) {
  final filters = <String, dynamic>{};

  if (params.status.isNotEmpty && params.status != 'all') {
    filters['shippingStatus'] = params.status;
  }

  if (params.searchText.isNotEmpty) {
    filters['shipmentNumber'] = params.searchText;
  }

  if (params.selectedDates.length == 2) {
    filters['startDate'] = params.selectedDates[0]?.toIso8601String();
    filters['endDate'] = params.selectedDates[1]
        ?.add(const Duration(
            hours: 23, minutes: 59, seconds: 59, milliseconds: 999))
        .toIso8601String();
  }

  filters['page'] = params.currentPage.toString();
  filters['perPage'] = params.perPage.toString();

  return Uri(queryParameters: filters).query;
}
