import 'package:flutter/material.dart';

extension ThemeExtension on BuildContext {
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => Theme.of(this).textTheme;
  ColorScheme get colorScheme => Theme.of(this).colorScheme;
  AppBarTheme get appBarTheme => Theme.of(this).appBarTheme;
  ButtonThemeData get buttonTheme => Theme.of(this).buttonTheme;
  IconThemeData get iconTheme => Theme.of(this).iconTheme;
  InputDecorationTheme get inputDecorationTheme =>
      Theme.of(this).inputDecorationTheme;
  OutlinedButtonThemeData get outlinedButtonTheme =>
      Theme.of(this).outlinedButtonTheme;
  RadioThemeData get radioTheme => Theme.of(this).radioTheme;
  SliderThemeData get sliderTheme => Theme.of(this).sliderTheme;
  SwitchThemeData get switchTheme => Theme.of(this).switchTheme;
  TextSelectionThemeData get textSelectionTheme =>
      Theme.of(this).textSelectionTheme;
  TextButtonThemeData get textButtonTheme => Theme.of(this).textButtonTheme;
  TooltipThemeData get tooltipTheme => Theme.of(this).tooltipTheme;
  Typography get typography => Theme.of(this).typography;
}
