import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

extension CurrencyExtensions on num {
  /// Calculate the decimal digits for formatting
  int _calculateDecimalDigits(int? decimalDigits) {
    if (decimalDigits != null) return decimalDigits;

    if (this <= 0 || this % 1 == 0) return 0;

    final fractionalPart = toString().split('.').last;

    if ((int.tryParse(fractionalPart) ?? 0) == 0) return 0;

    final lastDigits = fractionalPart.length;

    return lastDigits > 2 ? 2 : lastDigits;
  }

  /// Formatter for a given locale and decimal digits
  NumberFormat formatter(BuildContext context, [int? decimalDigits]) {
    final resolvedDecimalDigits = _calculateDecimalDigits(decimalDigits);
    return NumberFormat.currency(
      decimalDigits: resolvedDecimalDigits,
      locale: Localizations.localeOf(context).toString(),
      name: '',
    );
  }

  /// Format the value as a currency string
  String formatCurrency(
    BuildContext context,
    String currencyIso, {
    int? decimalDigits,
    bool allowNegative = false,
  }) {
    final ft = formatter(context, decimalDigits);
    final displayAmount = allowNegative ? this : (this < 0 ? 0 : this);
    return "${ft.simpleCurrencySymbol(currencyIso)}${ft.format(displayAmount)}";
  }

  /// Format the value as "ISO Amount"
  String formatCurrencyWithIso(
    BuildContext context,
    String currencyIso, {
    int? decimalDigits,
    bool allowNegative = false,
  }) {
    final ft = formatter(context, decimalDigits);
    final displayAmount = allowNegative ? this : (this < 0 ? 0 : this);
    return "$currencyIso ${ft.format(displayAmount)}";
  }

  /// Format as table string with ISO and currency symbol
  String formatAsTableString(
    BuildContext context,
    String currencyIso, {
    int? decimalDigits,
    bool allowNegative = false,
  }) {
    final ft = formatter(context, decimalDigits);
    final displayAmount = allowNegative ? this : (this < 0 ? 0 : this);
    return "${ft.simpleCurrencySymbol(currencyIso)}${ft.format(displayAmount)} $currencyIso";
  }

  /// Render a widget with currency info and optional extra widget
  Widget asTableWidget(
    BuildContext context,
    Currency? currency, {
    int? decimalDigits,
    MainAxisAlignment? mainAxisAlignment,
    bool allowNegative = false,
    Widget? extra,
  }) {
    if (currency == null) return const SizedBox.shrink();
    return Row(
      mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
      children: [
        Text(
            formatCurrency(context, currency.iso!,
                decimalDigits: decimalDigits, allowNegative: allowNegative),
            style: Theme.of(context).textTheme.bodyMedium),
        const Gap(5),
        Text(
          '${currency.iso}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
        ),
        if (extra != null) extra
      ],
    );
  }

  /// Get the currency symbol for a given ISO code
  String currencySymbol(BuildContext context, String currencyIso) {
    return formatter(context, 0).simpleCurrencySymbol(currencyIso);
  }

  /// Format the amount with a fixed number of decimal digits
  String formattedAmount(
    BuildContext context, {
    int? decimalDigits,
    bool allowNegative = false,
  }) {
    final displayAmount = allowNegative ? this : (this < 0 ? 0 : this);
    return formatter(context, decimalDigits).format(displayAmount);
  }

  /// Format the number to a clean string without trailing zeros
  String toCleanString() {
    if (this == roundToDouble()) {
      return toInt().toString(); // No decimal part
    }

    String str = toStringAsFixed(2);
    // Remove trailing zero if any (e.g. 5.20 -> 5.2)
    if (str.endsWith('0')) {
      str = str.substring(0, str.length - 1);
    }
    return str;
  }
}
