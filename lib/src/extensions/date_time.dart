import 'package:intl/intl.dart';

extension DateTimeFormatting on DateTime {
  String toDayMonth() {
    return DateFormat('d MMM').format(toLocal());
  }

  String toDate() {
    return DateFormat('MMM dd, yyyy').format(toLocal());
  }

  String toFixedDate() {
    return DateFormat('dd/MM/yyyy').format(toLocal());
  }

  String toFullDateTime() {
    return DateFormat('dd/MM/yyyy h:mm a').format(toLocal());
  }

  String toMonthName() {
    return DateFormat('MMMM').format(toLocal());
  }

  String toFullDate() {
    return DateFormat('MMMM d, yyyy').format(toLocal());
  }
}
