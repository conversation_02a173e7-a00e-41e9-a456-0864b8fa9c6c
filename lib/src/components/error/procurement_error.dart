import 'package:flutter/material.dart';
import 'package:td_procurement/src/utils/error_handler/error_handler.dart';

class ProcurementError extends StatelessWidget {
  ProcurementError(
      {super.key,
      this.errorDetails,
      this.retryAction,
      this.isComponent = false}) {
    if (errorDetails != null) {
      ErrorHandler.report(
        errorDetails!.exception,
        errorDetails!.stack,
        hint: {
          'context': errorDetails!.context,
          'summary': errorDetails!.summary.toString(),
          'library': errorDetails!.library,
        },
      );
    }
  }

  static Future<void> showModal(BuildContext context,
      {FlutterErrorDetails? errorDetails}) async {
    if (context.mounted) {
      return await showModalBottomSheet<void>(
        context: context,
        builder: (_) => ProcurementError(errorDetails: errorDetails),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(18),
          ),
        ),
        clipBehavior: Clip.antiAlias,
      );
    }
  }

  final FlutterErrorDetails? errorDetails;
  final VoidCallback? retryAction;
  final bool isComponent;

  @override
  Widget build(BuildContext context) {
   // final textTheme = Theme.of(context).textTheme;
/*    final actionButton = retryAction == null
        ? KButtonPrimary(
      text: 'Dismiss',
      onTap: () {
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        } else {
          context.goNamed(HomePath);
        }
      },
    )
        : KButtonPrimary(
      text: 'Retry',
      onTap: retryAction,
    );*/
    return Scaffold(
      body: Placeholder()/*Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (kReleaseMode || errorDetails == null) ...[
                SvgPicture.asset(kSvgErrorFrame),
                const YMargin(16),
                Text(
                  'Oops! Something went wrong',
                  style: textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                ),
                if (!isComponent) ...[
                  const YMargin(16),
                  Text(
                    'Don’t worry, we are in the process of fixing the issue. Please contact support if you need immediate help or give us a little time to work things out.',
                    style: textTheme.bodyLarge?.copyWith(color: k717A8E),
                    textAlign: TextAlign.center,
                  ),
                  const YMargin(16),
                  Text(
                    'Thank you for your patience.',
                    style: textTheme.bodyLarge?.copyWith(
                      color: k122242,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                const YMargin(16),
                actionButton
              ] else ...[
                Text(
                  'Error',
                  style: textTheme.titleLarge,
                ),
                SizedBox(height: 20),
                Text(
                  errorDetails?.exception.toString() ??
                      'Oops! Something went wrong',
                  textAlign: TextAlign.center,
                ),
                if (!isComponent) ...[
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 20),
                    child: Text('IN'),
                  ),
                  Text(
                    errorDetails?.context.toString() ??
                        'Don’t worry, we are in the process of fixing the issue. Please contact support if you need immediate help or give us a little time to work things out.',
                    textAlign: TextAlign.center,
                  ),
                ],
                const YMargin(16),
                actionButton
              ]
            ],
          ),
        ),
      ),*/
    );
  }
}
