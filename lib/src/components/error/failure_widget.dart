import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

class FailureWidget extends StatelessWidget {
  const FailureWidget({
    super.key,
    required this.e,
    required this.retry,
    this.fullScreen = false,
    this.heightFactor = 0.77,
  });

  final dynamic e;
  final VoidCallback retry;
  final bool? fullScreen;
  final double? heightFactor;

  Widget child() {
    if (e is Failure) {
      final msg = e.error.error.message.toString();
      return _FailureBody(
        message: msg,
        retry: retry,
      );
    }

    if (e is DefaultError) {
      final msg = (e as DefaultError).errorMessage;
      return _FailureBody(
        message: msg,
        retry: retry,
      );
    }

    return _FailureBody(
      message: 'Something went wrong',
      retry: retry,
    );
  }

  @override
  Widget build(BuildContext context) {
    return fullScreen!
        ? SizedBox(
            height: MediaQuery.sizeOf(context).height * heightFactor!,
            child: child(),
          )
        : child();
  }
}

class _FailureBody extends StatelessWidget {
  const _FailureBody({super.key, required this.message, required this.retry});

  final String message;
  final VoidCallback retry;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        // color: Palette.k040302,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              message,
              style: textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const Gap(5),
            TextButton(
              onPressed: retry,
              style: OutlinedButton.styleFrom(
                // foregroundColor: Palette.kFFEABC,
                // surfaceTintColor: Palette.kFFEABC,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8), // Border radius
                ),
                side: const BorderSide(
                    // color: Palette.kFFEABC,
                    ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Text(
                  'retry',
                  style: textTheme.bodyMedium?.copyWith(
                    // color: Palette.kFFEABC,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const Gap(10),
          ],
        ),
      ),
    );
  }
}
