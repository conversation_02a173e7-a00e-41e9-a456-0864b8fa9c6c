import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class Config {
  final double width;
  final double height;
  final double? top;
  final double? right;
  Config({
    required this.width,
    required this.height,
    this.top,
    this.right,
  });
}

class MultiDatePickerOverlay extends StatefulWidget {
  final DateTime? currentStart;
  final DateTime? currentEnd;
  final void Function(DateTime?, DateTime?) onSelectRange;
  final VoidCallback onCancel;

  const MultiDatePickerOverlay({
    super.key,
    required this.onSelectRange,
    required this.onCancel,
    this.currentStart,
    this.currentEnd,
  });

  @override
  State<StatefulWidget> createState() => _MultiDatePickerOverlayState();
}

class _MultiDatePickerOverlayState extends State<MultiDatePickerOverlay> {
  late DateTime? _selectedStartDate = widget.currentStart;
  late DateTime? _selectedEndDate = widget.currentEnd;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: _buildCalendar(widget.currentStart, "Start Date", true),
            ),
            Flexible(
              child: _buildCalendar(widget.currentEnd, "End Date", false),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 20, bottom: 10),
              child: FilledButton(
                onPressed: () => widget.onSelectRange(null, null),
                style: FilledButton.styleFrom(
                  minimumSize: const Size(80, 32),
                  textStyle: Theme.of(context).textTheme.bodyMedium,
                  overlayColor: Colors.transparent,
                  foregroundColor: Palette.primary,
                  backgroundColor: Palette.primary.withOpacity(0.12),
                ),
                child: const Text('Cancel'),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 10, bottom: 10),
              child: FilledButton(
                onPressed: () {
                  if ((_selectedEndDate != null &&
                          _selectedStartDate != null) &&
                      (_selectedEndDate!.isAfter(_selectedStartDate!) ||
                          _selectedEndDate!
                              .isAtSameMomentAs(_selectedStartDate!))) {
                    widget.onSelectRange(_selectedStartDate, _selectedEndDate);
                  } else {
                    Toast.error("date error", context);
                  }
                },
                style: FilledButton.styleFrom(
                  minimumSize: const Size(80, 32),
                  textStyle: Theme.of(context).textTheme.bodyMedium,
                ),
                child: const Text('Select'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCalendar(DateTime? currentDate, String label, bool isStartDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 25),
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        CalendarDatePicker2(
          config: CalendarDatePicker2WithActionButtonsConfig(
            calendarType: CalendarDatePicker2Type.single,
            calendarViewMode: CalendarDatePicker2Mode.day,
            modePickersGap: 10,
            currentDate: currentDate,
            dayMaxWidth: 35,
            gapBetweenCalendarAndButtons: 1,
            controlsHeight: 30,
            buttonPadding: EdgeInsets.zero,
            lastMonthIcon: SvgPicture.asset(kChevronLeftSvg),
            nextMonthIcon: SvgPicture.asset(kChevronRightSvg),
            cancelButton: const SizedBox.shrink(),
            okButton: const SizedBox.shrink(),
            hideScrollViewTopHeader: true,
            hideMonthPickerDividers: true,
            hideScrollViewTopHeaderDivider: true,
            hideYearPickerDividers: true,
            hideScrollViewMonthWeekHeader: true,
            useAbbrLabelForMonthModePicker: true,
          ),
          displayedMonthDate: currentDate,
          onValueChanged: (dates) {
            isStartDate
                ? _selectedStartDate = dates.first
                : _selectedEndDate = dates.first;
          },
          value: [currentDate],
        )
      ],
    );
  }
}
