/*
import 'package:flutter/material.dart';
import 'package:shop/src/components/src/toast/src/toast_type.dart';
import 'package:shop/src/components/src/toast/src/toast_widget.dart';
import 'package:td_commons_flutter/models/index.dart';

class ToastView {
  static final ToastView _singleton = new ToastView._internal();

  factory ToastView() {
    return _singleton;
  }

  ToastView._internal();

  static OverlayState? overlayState;
  static OverlayEntry? _overlayEntry;
  static bool _isVisible = false;

  static void createView(
    String? msg,
    BuildContext context,
    int duration,
    String? title,
    Variant? variant,
    bool? rootNavigator, {
    ToastType? type,
  }) async {
    overlayState = Overlay.of(context, rootOverlay: rootNavigator ?? false);

    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) => ToastWidget(
        title: title,
        text: msg,
        type: type,
        variant: variant,
      ),
    );
    _isVisible = true;
    overlayState!.insert(_overlayEntry!);
    await Future.delayed(
      Duration(
        seconds: duration,
      ),
    );
    dismiss();
  }

  static dismiss() async {
    if (!_isVisible) {
      return;
    }
    _isVisible = false;
    _overlayEntry?.remove();
  }
}
*/
