/*
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';
import 'package:shop/src/components/src/toast/src/toast_type.dart';
import 'package:shop/src/components/src/widgets/cached_image/cached_image.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/variant.dart';

class ToastBar extends StatefulWidget {
  final String message;
  final String? title;
  final ToastType type;
  final int duration;
  final Variant? variant;
  final Function closeAction;

  ToastBar({
    required this.type,
    required this.message,
    required this.closeAction,
    this.title,
    this.variant,
    this.duration = 5,
  });

  @override
  State<StatefulWidget> createState() {
    return _ToastBar();
  }
}

class _ToastBar extends State<ToastBar> {
  @override
  Widget build(BuildContext context) {
    bool selected = false;
    bool isLargeScreen = ResponsiveDesign.isLargeScreen(context);
    Future.delayed(Duration(seconds: widget.duration))
        .whenComplete(() => selected
            ? null
            : mounted
                ? Navigator.maybePop(context)
                : null);
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          selected = true;
          Navigator.pop(context);
        },
        child: Align(
          child: widget.type == ToastType.product
              ? ProductView(widget.variant!)
              : Container(
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(4)),
                  child: Row(
                    children: [
                      const SizedBox(width: 10),
                      Icon(_icon, color: Colors.white, size: 30),
                      const SizedBox(width: 15),
                      Flexible(
                          child: Column(
                            children: [
                              SizedBox(height: 5),
                              Text(_title(context),
                                  style: KTextStyle.semiBold24.copyWith(
                                      height: 1.0, color: Colors.white)),
                              Flexible(
                                  child: Text(widget.message,
                                      style: KTextStyle.book14.copyWith(
                                          color: Colors.white, fontSize: 16)),
                                  fit: FlexFit.loose),
                              SizedBox(height: 5)
                            ],
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                          ),
                          fit: FlexFit.loose),
                      const SizedBox(width: 15),
                    ],
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                  ),
                  margin: EdgeInsets.only(top: 20, right: 20, left: 20),
                  width: isLargeScreen ? 400 : null,
                  height: isLargeScreen ? 80 : null,
                ),
          alignment: Alignment.topRight,
        ),
      ),
    );
  }

  */
/*Widget _productView(BuildContext context, Variant variant) {
    final appConfig = context.read<AppConfig>();
    String imageUrl = getSmallImageURL(variant.variantId, appConfig.flavor);
    return Container(
      constraints: BoxConstraints.loose(Size(500, 100)),
      margin: EdgeInsets.only(top: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: SizedBox(
              height: 60.0,
              child: CachedImage(
                imageUrl,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Center(
                  child: Text(
                    _title(context),
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
                  ),
                ),
                Text(
                  'Your item has been successfully added into your cart.',
                  style: TextStyle(
                    color: kColorBlack50,
                    fontWeight: FontWeight.w300,
                    fontSize: 14
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Material(
              shape: CircleBorder(),
              color: _color(true),
              clipBehavior: Clip.antiAlias,
              child: Icon(_icon, color: Colors.white, size: 30),
            ),
          )
        ],
      ),
    );
  }*//*


  Color _color(bool isLargeScreen, [bool dark = false]) {
    if (widget.type == ToastType.danger) {
      if (dark) return kColorToastDarkRed;

      return isLargeScreen ? kColorOrange : kColorToastRed;
    }

    if (widget.type == ToastType.info) {
      if (dark) return kColorToastDarkOrange;

      return kColorToastOrange;
    }

    if (widget.type == ToastType.product) {
      if (dark) return kColorToastDarkGreen;

      return Theme.of(context).colorScheme.onPrimary;
    }

    if (dark) return kColorToastDarkGreen;

    return kColorToastGreen;
  }

  IconData get _icon {
    if (widget.type == ToastType.success || widget.type == ToastType.product)
      return Icons.check;
    return Icons.clear_outlined;
  }

  String _title(BuildContext context) {
    if (widget.title != null) return widget.title!;
    if (widget.type == ToastType.danger)
      return '${AppLocalizations.of(context)!.error}!';
    if (widget.type == ToastType.info)
      return '${AppLocalizations.of(context)!.info}!';
    if (widget.type == ToastType.product)
      return '${AppLocalizations.of(context)!.product_toast_title}';
    return '${AppLocalizations.of(context)!.success}!';
  }
}

class ProductView extends StatelessWidget {
  final Variant variant;
  ProductView(this.variant);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(4)),
      padding: EdgeInsets.all(10),
      child: Row(
        children: [
          SizedBox(
            height: 45.0,
            width: 45.0,
            child: CachedImage(variant.variantId, ImageSize.small),
          ),
          XSpacing(15),
          Flexible(
              child: Column(
                children: [
                  Text(
                    "Added to cart",
                    style: KTextStyle.semiBold24
                        .copyWith(height: 1.0, fontSize: 18),
                  ),
                  Flexible(
                      child: Text(
                        'Your item has been successfully added into your cart.',
                        style: KTextStyle.book14,
                      ),
                      fit: FlexFit.loose),
                ],
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
              ),
              fit: FlexFit.loose),
          XSpacing(15),
          CircleAvatar(
            radius: 20,
            child: Icon(Icons.check, color: Colors.white, size: 30),
          )
        ],
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
      ),
      margin: EdgeInsets.only(top: 20, right: 20, left: 20),
      width: 370,
      height: 90,
    );
  }
}
*/
