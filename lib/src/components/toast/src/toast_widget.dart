/*
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/src/components/src/toast/src/toast_type.dart';
import 'package:shop/src/components/src/toast/src/toast_view.dart';
import 'package:shop/src/components/src/widgets/cached_image/index.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:td_commons_flutter/models/variant.dart';

class ToastWidget extends StatelessWidget {
  final ToastType? type;
  final String? title;
  final String? text;
  final Variant? variant;

  const ToastWidget({
    Key? key,
    this.type = ToastType.success,
    this.title,
    required this.text,
    this.variant,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _build(context);
  }

  Widget _build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Align(
        child: Container(
          constraints: BoxConstraints.loose(Size(600, 130)),
          //width: context.width - 40,
          margin: EdgeInsets.symmetric(horizontal: 20, vertical: 30),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(offset: Offset(0, 5), blurRadius: 10),
            ],
            border: Border.all(),
            color: _color(),
          ),
          padding: const EdgeInsets.only(
            top: 10,
            bottom: 20,
            left: 20,
            right: 10,
          ),
          child: type == ToastType.product
              ? _productView(context, variant!)
              : _otherView(context),
        ),
        alignment: Alignment.topCenter,
      ),
    );
  }

  Widget _productView(BuildContext context, Variant variant) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          flex: 1,
          child: Container(
            height: 60.0,
            alignment: Alignment.centerLeft,
            child: CachedImage(variant.variantId, ImageSize.small),
          ),
        ),
        Expanded(
          flex: 2,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Center(
                child: Text(
                  _title(context)!,
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
              ),
              Text(
                'Your item has been successfully added into your cart.',
                style: TextStyle(),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: Material(
            shape: CircleBorder(),
            color: _color(true),
            clipBehavior: Clip.antiAlias,
            child: InkWell(
              onTap: ToastView.dismiss,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: SvgPicture.asset(
                  _svg,
                  width: 20,
                  height: 20,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _otherView(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _title(context)!,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
            Material(
              shape: CircleBorder(),
              color: _color(true),
              clipBehavior: Clip.antiAlias,
              child: InkWell(
                onTap: ToastView.dismiss,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SvgPicture.asset(
                    _svg,
                    color: Colors.white,
                  ),
                ),
              ),
            )
          ],
        ),
        Text(
          text!,
          style: TextStyle(
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Color _color([bool dark = false]) {
    if (type == ToastType.danger) {
      if (dark) return kColorToastDarkRed;

      return kColorToastRed;
    }

    if (type == ToastType.info) {
      if (dark) return kColorToastDarkOrange;

      return kColorToastOrange;
    }

    if (type == ToastType.product) {
      if (dark) return kColorToastDarkGreen;

      return kColorWhite;
    }

    if (dark) return kColorToastDarkGreen;

    return kColorToastGreen;
  }

  String get _svg {
    if (type == ToastType.success || type == ToastType.product) return kSvgDone;
    return kSvgClose;
  }

  String? _title(BuildContext context) {
    if (title != null) return title;
    if (type == ToastType.danger)
      return '${AppLocalizations.of(context)!.error}!';
    if (type == ToastType.info) return '${AppLocalizations.of(context)!.info}!';
    if (type == ToastType.product)
      return '${AppLocalizations.of(context)!.product_toast_title}';
    return '${AppLocalizations.of(context)!.success}!';
  }
}
*/
