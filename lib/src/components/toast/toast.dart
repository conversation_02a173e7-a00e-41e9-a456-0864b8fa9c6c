import 'dart:async';

import 'package:flutter/material.dart';
import 'package:td_procurement/src/extensions/strings.dart';
import 'package:td_procurement/src/res/assets/images/images.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/error_handler/error_mapper.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

class Toast extends StatefulWidget {
  final String? title;
  final String? message;
  final Color backgroundTopColor;
  final Color backgroundBottomColor;
  final int duration;
  final VoidCallback onClose;
  final void Function(ToastState state)? stateSetter;
  final CustomAction? customAction;

  const Toast({
    super.key,
    this.title,
    this.message,
    required this.backgroundTopColor,
    required this.backgroundBottomColor,
    this.duration = 5,
    required this.onClose,
    this.stateSetter,
    this.customAction,
  });

  @override
  ToastState createState() => ToastState();

  static OverlayEntry? _currentToast;
  static Timer? _toastTimer;
  static bool _isToastVisible = false;
  static ToastState? _toastState;

  static void show(
    String message,
    BuildContext context, {
    String? title,
    int duration = 5,
    CustomAction? action,
    bool? preventRestart,
  }) {
    _showToast(
      context,
      title ?? 'Success',
      message,
      duration: duration,
      topColor: HexColor('#FFFAEDE7'),
      bottomColor: HexColor('#FFFDF8F6'),
      customAction: action,
      preventRestart: preventRestart,
    );
  }

  static void success(
    String message,
    BuildContext context, {
    String? title,
    int duration = 5,
    CustomAction? action,
    bool? preventRestart,
  }) {
    _showToast(
      context,
      title ?? 'Success',
      message,
      duration: duration,
      topColor: HexColor('#FFFAEDE7'),
      bottomColor: HexColor('#FFFDF8F6'),
      customAction: action,
      preventRestart: preventRestart,
    );
  }

  static void error(
    String message,
    BuildContext context, {
    String? title,
    int duration = 5,
    CustomAction? action,
    bool? preventRestart,
  }) {
    _showToast(
      context,
      ErrorMapper.fetchErrorTitle(message, context) ?? title ?? 'Error',
      ErrorMapper.fetchErrorMessage(message, context) ?? message,
      duration: duration,
      topColor: HexColor('#FFFDECEA'),
      bottomColor: HexColor('#FFF8D7DA'),
      customAction: action,
      preventRestart: preventRestart,
    );
  }

  static void apiError(
    AppException exception,
    BuildContext context, {
    String? title,
    int duration = 5,
    CustomAction? action,
    bool? preventRestart,
  }) {
    final is404 = ErrorMapper.is404(exception.error);
    final errorTitle = is404 ? 'Oops Something Went Wrong!' : title;
    String errorMessage = is404 ? kDefaultErrMsg : exception.error.toString();
    error(
      errorMessage,
      context,
      title: errorTitle,
      action: action ??
          ErrorMapper.getCustomAction(
            exception.error.toString(),
          ),
      preventRestart: preventRestart,
    );
  }

  static void info(
    String message,
    BuildContext context, {
    String? title,
    int duration = 5,
    CustomAction? action,
    bool? preventRestart,
  }) {
    _showToast(
      context,
      title ?? 'Info',
      message,
      duration: duration,
      topColor: HexColor('#FFFAEDE7'),
      bottomColor: HexColor('#FFFDF8F6'),
      customAction: action,
      preventRestart: preventRestart,
    );
  }

  static void _showToast(
    BuildContext context,
    String? title,
    String message, {
    int duration = 5,
    required Color topColor,
    required Color bottomColor,
    CustomAction? customAction,
    bool? preventRestart,
  }) {
    final overlay = Overlay.of(context);

    if (_isToastVisible && _currentToast != null) {
      if (preventRestart ?? true) {
        // Update the existing toast's content and restart the timer
        _toastState?.updateToast(
          newTitle: title,
          newMessage: message,
          newTopColor: topColor,
          newBottomColor: bottomColor,
          newCustomAction: customAction,
          newDuration: duration,
        );
        return;
      }
      _currentToast?.remove();
      _toastTimer?.cancel();
      _isToastVisible = false;
    }

    late OverlayEntry toastEntry;

    toastEntry = OverlayEntry(
      builder: (_) => Toast(
        title: title,
        message: message,
        backgroundTopColor: topColor,
        backgroundBottomColor: bottomColor,
        duration: duration,
        customAction: customAction,
        onClose: () {
          toastEntry.remove();
          _toastTimer?.cancel();
          _isToastVisible = false;
        },
        stateSetter: (state) {
          _toastState = state;
        },
      ),
    );

    _currentToast = toastEntry;
    overlay.insert(_currentToast!);
    _isToastVisible = true;

    _toastTimer = Timer(Duration(seconds: duration), () {
      if (_isToastVisible && _toastState != null) {
        _toastState!._closeToast();
      }
    });
  }

  static void dismissToast() {
    _toastState?._closeToast();
    _toastState = null;
    _isToastVisible = false;
  }
}

enum BtnType { filled, outline }

class CustomAction {
  final String text;
  final Function(BuildContext ctx) action;
  final BtnType? btnType;

  CustomAction(this.text, this.action, [this.btnType]);
}

class ToastState extends State<Toast> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;

  late String? title;
  late String? message;
  late CustomAction? customAction;
  late Color topColor;
  late Color bottomColor;
  late int duration;

  @override
  void initState() {
    super.initState();

    title = widget.title;
    message = widget.message;
    customAction = widget.customAction;
    topColor = widget.backgroundTopColor;
    bottomColor = widget.backgroundBottomColor;

    widget.stateSetter?.call(this);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: const Offset(0.0, 0.0),
    )
        .chain(
          CurveTween(curve: Curves.fastOutSlowIn),
        )
        .animate(_animationController);

    _animationController.forward();

    _startCloseTimer(widget.duration);

    // SchedulerBinding.instance.addPostFrameCallback((_) {
    //   Future.delayed(Duration(seconds: widget.duration), () {
    //     if (mounted) {
    //       _closeToast();
    //     }
    //   });
    // });
  }

  void _startCloseTimer(int duration) {
    Toast._toastTimer?.cancel();
    Toast._toastTimer = Timer(Duration(seconds: duration), _closeToast);
  }

  void _closeToast() {
    _animationController.reverse().then((_) {
      if (mounted) {
        widget.onClose();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void updateToast({
    String? newTitle,
    String? newMessage,
    CustomAction? newCustomAction,
    Color? newTopColor,
    Color? newBottomColor,
    int? newDuration,
  }) {
    if (mounted) {
      setState(() {
        if (newTitle != null) title = newTitle;
        if (newMessage != null) message = newMessage;
        if (newCustomAction != null) customAction = newCustomAction;
        if (newTopColor != null) topColor = newTopColor;
        if (newBottomColor != null) bottomColor = newBottomColor;
        if (newDuration != null) _startCloseTimer(newDuration);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = ColorScheme.fromSeed(
        seedColor: Palette.primary, brightness: Theme.of(context).brightness);

    final isFilledButton = customAction?.btnType == null
        ? true
        : customAction?.btnType == BtnType.filled;

    return Positioned(
      bottom: 70,
      right: 20,
      child: SlideTransition(
        position: _slideAnimation,
        child: SafeArea(
          child: Material(
            elevation: 0,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              width: 425,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(ImgPictures.bg.path),
                  fit: BoxFit.cover,
                ),
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    offset: const Offset(0, 4),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16).copyWith(left: 30, top: 8),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.topRight,
                      child: IconButton(
                        icon: const Icon(Icons.close, color: Colors.black),
                        onPressed: _closeToast,
                      ),
                    ),
                    if (title != null && title!.isNotEmpty)
                      Text(
                        title!,
                        style: textTheme.titleLarge?.copyWith(
                          fontSize: 20,
                        ),
                      ),
                    if (message != null && message!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0, bottom: 6),
                        child: Text(
                          message!,
                          style: textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w400,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    if (customAction != null) ...[
                      isFilledButton
                          ? SizedBox(
                              width: double.maxFinite,
                              child: FilledButton(
                                onPressed: () {
                                  _closeToast();
                                  customAction?.action.call(context);
                                },
                                child: Text(customAction!.text.capitalize),
                              ),
                            )
                          : OutlinedButton(
                              style: ButtonStyle(
                                side: WidgetStateProperty.all(
                                  BorderSide(color: colorScheme.outline),
                                ),
                              ),
                              onPressed: () {
                                _closeToast();
                                customAction?.action.call(context);
                              },
                              child: Text(
                                customAction!.text,
                                style: textTheme.bodyMedium
                                    ?.copyWith(color: colorScheme.onSurface),
                              ),
                            ),
                    ]
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}


// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:td_procurement/src/extensions/strings.dart';
// import 'package:td_procurement/src/res/assets/images/images.dart';
// import 'package:td_procurement/src/res/styles/colors/colors.dart';
// import 'package:td_procurement/src/res/values/constants/constants.dart';
// import 'package:td_procurement/src/utils/error_handler/error_mapper.dart';
// import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

// class Toast {
//   //static const double _toastHeight = 70.0;
//   //static const Color _backgroundColor = Color.fromRGBO(247, 247, 247, 1);

//   static void show(String? msg, BuildContext context,
//       {String? title, int duration = 5, CustomAction? action}) {
//     _showToast(
//         context,
//         title ?? 'Success',
//         msg ?? '',
//         Icons.check_circle_outline,
//         ColorScheme.fromSeed(
//             seedColor: const Color(0xFF25D366),
//             brightness: Theme.of(context).brightness),
//         duration,
//         customAction: action);
//   }

//   static void success(
//     String? msg,
//     BuildContext context, {
//     String? title,
//     int duration = 5,
//   }) {
//     _showToast(
//       context,
//       title ?? 'Success',
//       msg ?? '',
//       Icons.check_circle_outline,
//       ColorScheme.fromSeed(
//           seedColor: const Color(0xFF25D366),
//           brightness: Theme.of(context).brightness),
//       duration,
//     );
//   }

//   static void error(
//     String? msg,
//     BuildContext context, {
//     String? title,
//     int duration = 5,
//     Object? appException,
//     CustomAction? customAction,
//   }) {
//     String message = msg ?? appException?.toString() ?? "";
//     String header = title ?? "Error";
//     final is404 = ErrorMapper.is404(appException ?? message);
//     _showToast(
//       context,
//       is404
//           ? 'Oops Something Went Wrong!'
//           : ErrorMapper.fetchErrorTitle(message, context) ?? header,
//       is404
//           ? kDefaultErrMsg
//           : ErrorMapper.fetchErrorMessage(message, context) ?? message,
//       Icons.error_outline,
//       ColorScheme.fromSeed(
//           seedColor: const Color(0xFFEB0000),
//           brightness: Theme.of(context).brightness),
//       duration,
//       customAction: customAction ?? ErrorMapper.getCustomAction(msg),
//     );
//   }

//   static void apiError(
//     AppException exception,
//     BuildContext context, {
//     String? title,
//     int duration = 5,
//     bool mcaError = false,
//     CustomAction? customAction,
//   }) {
//     error(null, context,
//         title: title,
//         duration: duration,
//         appException: exception.error,
//         customAction: customAction);
//   }

//   static void info(
//     String? msg,
//     BuildContext context, {
//     String? title,
//     int duration = 5,
//   }) {
//     _showToast(
//         context,
//         title ?? 'Info',
//         msg ?? '',
//         Icons.warning_outlined,
//         ColorScheme.fromSeed(
//             seedColor: const Color(0xFFF59300),
//             brightness: Theme.of(context).brightness),
//         duration);
//   }

//   static void _showToast(
//     BuildContext context,
//     String title,
//     String body,
//     IconData icon,
//     ColorScheme iconColor,
//     int seconds, {
//     CustomAction? customAction,
//   }) {
//     if (!context.mounted) return;
//     final overlayState = Overlay.of(context);
//     final textTheme = Theme.of(context).textTheme;
//     final duration = Duration(seconds: seconds);

//     if (!context.mounted) return;

//     OverlayEntry? overlayEntry;

//     overlayEntry = OverlayEntry(
//       builder: (ctx) => Align(
//         alignment: Alignment.bottomRight,
//         child: GestureDetector(
//           onTap: () {
//             if (overlayEntry != null && overlayEntry.mounted) {
//               overlayEntry.remove();
//             }
//           },
//           onHorizontalDragEnd: (_) {
//             if (overlayEntry != null && overlayEntry.mounted) {
//               overlayEntry.remove();
//             }
//           },
//           child: Dismissible(
//             key: UniqueKey(),
//             direction: DismissDirection.horizontal,
//             onDismissed: (_) {
//               if (overlayEntry != null && overlayEntry.mounted) {
//                 overlayEntry.remove();
//               }
//             },
//             child: SafeArea(
//               child: Container(
//                 padding: const EdgeInsets.only(
//                     left: 30, top: 20, bottom: 20, right: 20),
//                 margin: const EdgeInsets.all(30),
//                 width: 518,
//                 height: 207,
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(12),
//                   border: Border.all(
//                     color: Palette.kE7E7E7,
//                   ),
//                   image: DecorationImage(
//                     image: AssetImage(ImgPictures.bg.path),
//                   ),
//                   color: Colors.white,
//                   boxShadow: const [
//                     BoxShadow(
//                       color: Palette.k0000000A,
//                       blurRadius: 12,
//                       spreadRadius: 0,
//                       offset: Offset(0, 4),
//                     )
//                   ],
//                 ),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   children: [
//                     Align(
//                       alignment: Alignment.centerRight,
//                       child: IconButton(
//                         onPressed: () {
//                           if (overlayEntry != null && overlayEntry.mounted) {
//                             overlayEntry.remove();
//                           }
//                         },
//                         icon: Icon(
//                           Icons.close_outlined,
//                           color: Palette.primaryBlack,
//                         ),
//                       ),
//                     ),
//                     const Gap(10),
//                     Text(title, style: textTheme.labelMedium),
//                     const Gap(4),
//                     Text(
//                       body,
//                       style: textTheme.bodyLarge?.copyWith(
//                           color: Palette.k6B797C, fontWeight: FontWeight.w500),
//                     ),
//                     const SizedBox(height: 5.0),
//                     if (customAction != null)
//                       FilledButton(
//                         onPressed: () {
//                           if (overlayEntry != null && overlayEntry.mounted) {
//                             overlayEntry.remove();
//                           }
//                           customAction.action(ctx);
//                         },
//                         child: Text(customAction.text.capitalize),
//                       )
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );

//     overlayState.insert(overlayEntry);

//     Future.delayed(duration).then((_) {
//       if (overlayEntry != null && overlayEntry.mounted) {
//         overlayEntry.remove();
//       }
//     });
//   }
// }

// enum BtnType { filled, outline }

// class CustomAction {
//   final String text;
//   final Function(BuildContext ctx) action;
//   final BtnType? btnType;

//   CustomAction(this.text, this.action, [this.btnType]);
// }

// class ThemeClass extends StatelessWidget {
//   const ThemeClass({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Material(
//       color: Theme.of(context).primaryColor,
//       child: const SizedBox(
//         width: 200,
//         height: 200,
//       ),
//     );
//   }
// }

