import 'package:flutter/material.dart';

class CustomToast {
  static final List<OverlayEntry> _toastOverlays = [];

  static void show(
    BuildContext context, {
    required String message,
    int duration = 2,
    Color backgroundColor = Colors.black54,
    Color textColor = Colors.white,
    double fontSize = 16,
  }) {
    final overlay = Overlay.of(context);

    // Remove existing toast overlays
    _removePreviousToasts();

    // Calculate the position of the calling widget
    final renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    // final widgetSize = renderBox.size;

    final overlayEntry = OverlayEntry(
      builder: (BuildContext context) => Positioned(
        // left: position.dx,
        right: position.dy,
        top: position.dy,
        child: ToastWidget(
          message: message,
          backgroundColor: backgroundColor,
          textColor: textColor,
          fontSize: fontSize,
        ),
      ),
    );

    _toastOverlays.add(overlayEntry);

    overlay.insert(overlayEntry);

    Future.delayed(Duration(seconds: duration), () {
      // Check if the overlay is still in the list before removing
      if (_toastOverlays.contains(overlayEntry)) {
        overlayEntry.remove();
        _toastOverlays.remove(overlayEntry);
      }
    });
  }

  static void _removePreviousToasts() {
    for (var overlayEntry in _toastOverlays) {
      // Check if the overlay is still in the list before removing
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    }
    _toastOverlays.clear();
  }
}

class ToastWidget extends StatelessWidget {
  final String message;
  final Color backgroundColor;
  final Color textColor;
  final double fontSize;

  const ToastWidget({
    super.key,
    required this.message,
    required this.backgroundColor,
    required this.textColor,
    required this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 10.0,
          ),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Text(
            message,
            style: TextStyle(
              color: textColor,
              fontSize: fontSize,
            ),
          ),
        ),
      ),
    );
  }
}


// class CustomToast {
//   static List<OverlayEntry> _toastOverlays = [];

//   static void show(
//     BuildContext context, {
//     required String message,
//     int duration = 2,
//     Color backgroundColor = Colors.black54,
//     Color textColor = Colors.white,
//     double fontSize = 16,
//   }) {
//     final overlay = Overlay.of(context);

//     // Remove existing toast overlays
//     _removePreviousToasts();

//     final overlayEntry = OverlayEntry(
//       builder: (BuildContext context) => ToastWidget(
//         message: message,
//         backgroundColor: backgroundColor,
//         textColor: textColor,
//         fontSize: fontSize,
//       ),
//     );

//     _toastOverlays.add(overlayEntry);

//     overlay.insert(overlayEntry);

//     Future.delayed(Duration(seconds: duration), () {
//       // Check if the overlay is still in the list before removing
//       if (_toastOverlays.contains(overlayEntry)) {
//         overlayEntry.remove();
//         _toastOverlays.remove(overlayEntry);
//       }
//     });
//   }

//   static void _removePreviousToasts() {
//     for (var overlayEntry in _toastOverlays) {
//       // Check if the overlay is still in the list before removing
//       if (overlayEntry.mounted) {
//         overlayEntry.remove();
//       }
//     }
//     _toastOverlays.clear();
//   }
// }

// class ToastWidget extends StatefulWidget {
//   final String message;
//   final Color backgroundColor;
//   final Color textColor;
//   final double fontSize;

//   const ToastWidget({
//     Key? key,
//     required this.message,
//     required this.backgroundColor,
//     required this.textColor,
//     required this.fontSize,
//   }) : super(key: key);

//   @override
//   _ToastWidgetState createState() => _ToastWidgetState();
// }

// class _ToastWidgetState extends State<ToastWidget> {
//   late double bottomPadding = MediaQuery.of(context).viewInsets.bottom;
//   late OverlayEntry overlayEntry =
//       OverlayEntry(builder: (BuildContext context) => Container());

//   @override
//   void initState() {
//     super.initState();
//     // Listen for changes in the keyboard state
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       overlayEntry = OverlayEntry(
//         builder: (BuildContext context) => Positioned(
//           bottom: bottomPadding,
//           width: MediaQuery.of(context).size.width,
//           child: Material(
//             color: Colors.transparent,
//             child: Align(
//               alignment: Alignment.bottomCenter,
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Container(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 16.0,
//                     vertical: 10.0,
//                   ),
//                   decoration: BoxDecoration(
//                     color: widget.backgroundColor,
//                     borderRadius: BorderRadius.circular(8.0),
//                   ),
//                   child: Text(
//                     widget.message,
//                     style: TextStyle(
//                         color: widget.textColor, fontSize: widget.fontSize),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       );
//       Overlay.of(context).insert(overlayEntry);
//     });
//   }

//   @override
//   void dispose() {
//     if (overlayEntry.mounted) {
//       overlayEntry.remove();
//     }
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return overlayEntry.builder(context);
//   }
// }
