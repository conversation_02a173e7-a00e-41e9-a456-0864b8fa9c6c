import 'package:flutter/material.dart';
import 'package:td_procurement/src/components/widgets/multi_value_listenable_builder.dart';

class CustomFilledButton extends StatefulWidget {
  const CustomFilledButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.loaderNotifier,
    this.disabledNotifier,
    this.style,
  });

  final String text;
  final VoidCallback onPressed;
  final ValueNotifier<bool>? loaderNotifier;
  final ValueNotifier<bool>? disabledNotifier;
  final ButtonStyle? style;

  @override
  State<StatefulWidget> createState() => _CustomFilledButton();
}

class _CustomFilledButton extends State<CustomFilledButton> {
  late ValueNotifier<bool> _loaderNotifier;
  late ValueNotifier<bool> _disabledNotifier;

  @override
  void initState() {
    super.initState();
    _loaderNotifier = widget.loaderNotifier ?? ValueNotifier<bool>(false);
    _disabledNotifier = widget.disabledNotifier ?? ValueNotifier<bool>(false);
  }

  @override
  Widget build(BuildContext context) {
    return MultiValueListenableBuilder<bool, bool, Null>(
      valueListenable1: _loaderNotifier,
      valueListenable2: _disabledNotifier,
      builder: (context, loading, disabled, _, __) {
        final isButtonDisabled = disabled || loading;
        return FilledButton(
          onPressed: isButtonDisabled ? null : widget.onPressed,
          style: widget.style?.copyWith(
                backgroundColor: WidgetStatePropertyAll(
                    isButtonDisabled ? Colors.grey : null),
              ) ??
              FilledButton.styleFrom(
                backgroundColor: isButtonDisabled ? Colors.grey : null,
              ),
          child: loading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(),
                )
              : Center(
                  child: Text(widget.text),
                ),
        );
      },
    );
  }

  @override
  void dispose() {
    if (widget.loaderNotifier == null) {
      _loaderNotifier.dispose();
    }
    if (widget.disabledNotifier == null) {
      _disabledNotifier.dispose();
    }
    super.dispose();
  }
}
