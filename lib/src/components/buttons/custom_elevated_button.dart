import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class CustomElevatedButton extends StatelessWidget {
  final String text;
  final String? icon;
  final VoidCallback onPressed;
  final Size? size;

  const CustomElevatedButton({
    super.key,
    required this.text,
    this.icon,
    required this.onPressed,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        elevation: 0.4,
        backgroundColor: Colors.white,
        foregroundColor: Palette.primaryBlack,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        shadowColor: Palette.k0000000A,
        textStyle: Theme.of(context)
            .textTheme
            .bodyMedium
            ?.copyWith(fontWeight: FontWeight.w500),
        side: BorderSide(color: Palette.kE7E7E7),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        minimumSize: size,
      ),
      child: Row(
        children: [
          Text(text),
          if (icon != null) ...[
            const Gap(10),
            SvgPicture.asset(icon!),
          ],
        ],
      ),
    );
  }
}
