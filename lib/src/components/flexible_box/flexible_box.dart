import 'package:flutter/material.dart';

class FlexibleConstrainedBox extends StatelessWidget {
  final Widget child;
  final double minHeight;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;

  const FlexibleConstrainedBox(
      {super.key,
      required this.child,
      this.minHeight = 800,
      this.physics,
      this.padding});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints viewportConstraints) {
      return ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: minHeight,
          maxHeight: viewportConstraints.maxHeight -
              (padding?.along(Axis.vertical) ?? 0),
        ).flexible,
        child: child,
      );
      // return SingleChildScrollView(
      //   physics: physics,
      //   padding: padding,
      //   child: ConstrainedBox(
      //     constraints: BoxConstraints(
      //       minHeight: minHeight,
      //       maxHeight: viewportConstraints.maxHeight -
      //           (padding?.along(Axis.vertical) ?? 0),
      //     ).flexible,
      //     child: child,
      //   ),
      // );
    });
  }
}

extension Scroll on BoxConstraints {
  BoxConstraints get flexible => BoxConstraints(
      minHeight: minHeight,
      maxHeight: maxHeight > minHeight ? maxHeight : minHeight);
}

extension ViewLimit on Size {
  double flexible(double size) => height > size ? height : size;
}
