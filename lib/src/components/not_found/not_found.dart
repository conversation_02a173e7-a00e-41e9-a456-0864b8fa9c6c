import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/res/assets/images/images.dart';

class NotFoundPage extends ConsumerWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context, ref) {
    final textTheme = Theme.of(context).textTheme;
    return Scaffold(
      body: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Image.asset(ImgPictures.emptyCan.path),
            ),
            const Gap(100),
            Flexible(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Oops!',
                    style: textTheme.displayLarge,
                  ),
                  const Gap(10),
                  Text(
                    'We could not find the page you are looking for',
                    textAlign: TextAlign.center,
                    style: textTheme.headlineLarge,
                  ),
                  const Gap(20),
                  FilledButton(
                    style: FilledButton.styleFrom(
                      minimumSize: const Size(120, 40),
                    ),
                    onPressed: () {
                      ref.read(storageProvider).refresh().whenComplete(() {
                        if (context.mounted) {
                          context.goNamed(kHomeRoute);
                        }
                      });
                    },
                    child: const Text('<-   Go Home'),
                  ),
                ],
              ),
            ),
            const Gap(100),
          ],
        ),
      ),
    );
  }
}
