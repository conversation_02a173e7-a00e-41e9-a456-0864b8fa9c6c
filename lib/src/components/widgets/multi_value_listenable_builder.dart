import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class MultiValueListenableBuilder<T1, T2, T3> extends StatelessWidget {
  final ValueListenable<T1> valueListenable1;
  final ValueListenable<T2> valueListenable2;
  final ValueListenable<T3>? valueListenable3; // Nullable
  final Widget Function(BuildContext, T1, T2, T3?, Widget?) builder;
  final Widget? child;

  const MultiValueListenableBuilder({
    super.key,
    required this.valueListenable1,
    required this.valueListenable2,
    this.valueListenable3,
    required this.builder,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<T1>(
      valueListenable: valueListenable1,
      builder: (context, value1, _) {
        return ValueListenableBuilder<T2>(
          valueListenable: valueListenable2,
          builder: (context, value2, _) {
            if (valueListenable3 != null) {
              return ValueListenableBuilder<T3>(
                valueListenable: valueListenable3!,
                builder: (context, value3, _) {
                  // Pass all three values if T3 is provided
                  return builder(context, value1, value2, value3, child);
                },
              );
            } else {
              // Pass value3 as null if T3 is not provided
              return builder(context, value1, value2, null, child);
            }
          },
        );
      },
    );
  }
}


// class MultiValueListenableBuilder<T1, T2> extends StatelessWidget {
//   final ValueListenable<T1> valueListenable1;
//   final ValueListenable<T2> valueListenable2;
//   final Widget Function(BuildContext, T1, T2, Widget?) builder;
//   final Widget? child;

//   const MultiValueListenableBuilder({
//     super.key,
//     required this.valueListenable1,
//     required this.valueListenable2,
//     required this.builder,
//     this.child,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return ValueListenableBuilder<T1>(
//       valueListenable: valueListenable1,
//       builder: (context, value1, _) {
//         return ValueListenableBuilder<T2>(
//           valueListenable: valueListenable2,
//           builder: (context, value2, _) {
//             return builder(context, value1, value2, child);
//           },
//         );
//       },
//     );
//   }
// }
