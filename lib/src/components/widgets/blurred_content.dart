import 'dart:ui';

import 'package:flutter/material.dart';

class BlurredContent extends StatelessWidget {
  final Widget child;
  final bool blur;
  final double blurValue;

  const BlurredContent({
    super.key,
    required this.child,
    this.blur = true,
    this.blurValue = 3,
  });

  @override
  Widget build(BuildContext context) {
    if (blur) {
      return ClipRect(
        child: ImageFiltered(
          imageFilter: ImageFilter.blur(sigmaX: blurValue, sigmaY: blurValue),
          child: child,
        ),
      );
    } else {
      return child;
    }
  }
}
