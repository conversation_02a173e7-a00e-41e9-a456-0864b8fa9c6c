import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class CurrencyWidget extends StatelessWidget {
  final num amount;
  final String currencyIso;
  final String? currencySymbol;
  final TextStyle? amountStyle;
  final int? decimalDigits;
  final bool allowNegative;

  const CurrencyWidget(
    this.amount,
    this.currencyIso, {
    super.key,
    this.currencySymbol,
    this.amountStyle,
    this.decimalDigits,
    this.allowNegative = false,
  });

  // Determine decimal digits if not specified
  static int _calculateDecimalDigits(num amount, int? decimalDigits) {
    if (decimalDigits != null) return decimalDigits;

    if (amount <= 0 || amount % 1 == 0) return 0;

    final fractionalPart = amount.toString().split('.').last;

    if ((int.tryParse(fractionalPart) ?? 0) == 0) return 0;

    final lastDigits = fractionalPart.length;

    return lastDigits > 2 ? 2 : lastDigits;
  }

  // Formatter with optional decimal digits based on context, amount, and currency
  static NumberFormat _formatter(BuildContext context, num amount,
      [int? decimalDigits]) {
    final resolvedDecimalDigits =
        _calculateDecimalDigits(amount, decimalDigits);
    return NumberFormat.currency(
      decimalDigits: resolvedDecimalDigits,
      locale: Localizations.localeOf(context).toString(),
      name: '',
    );
  }

  /// Static utility to format amount based on currency, locale, and optional decimal digits
  static String value(
    BuildContext context,
    String currencyIso,
    num amount, {
    int? decimalDigits,
    bool allowNegative = false,
  }) {
    final formatter = _formatter(context, amount, decimalDigits);
    final displayAmount = allowNegative ? amount : (amount < 0 ? 0 : amount);
    return "${formatter.simpleCurrencySymbol(currencyIso)}${formatter.format(displayAmount)}";
  }

  /// Render currency value as "ISO Amount" format with optional decimal digits
  static String valueWithIso(
    BuildContext context,
    String currencyIso,
    num amount, {
    int? decimalDigits,
    bool allowNegative = false,
  }) {
    final formatter = _formatter(context, amount, decimalDigits);
    final displayAmount = allowNegative ? amount : (amount < 0 ? 0 : amount);
    return "$currencyIso ${formatter.format(displayAmount)}";
  }

  /// Format as a table string with ISO and currency symbol with optional decimal digits
  static String tableString(
    BuildContext context,
    String currencyIso,
    num amount, {
    int? decimalDigits,
    bool allowNegative = false,
  }) {
    final formatter = _formatter(context, amount, decimalDigits);
    final displayAmount = allowNegative ? amount : (amount < 0 ? 0 : amount);
    return "${formatter.simpleCurrencySymbol(currencyIso)}${formatter.format(displayAmount)} $currencyIso";
  }

  /// Render a table widget with currency info and optional extra widget
  static Widget tableWidget(
    BuildContext context,
    Currency? currency,
    num amount, {
    int? decimalDigits,
    MainAxisAlignment? mainAxisAlignment,
    bool allowNegative = false,
    Widget? extra,
  }) {
    if (currency == null) return const SizedBox.shrink();
    return Row(
      mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
      children: [
        Text(
            value(context, currency.iso!, amount,
                decimalDigits: decimalDigits, allowNegative: allowNegative),
            style: Theme.of(context).textTheme.bodyMedium),
        const Gap(5),
        Text(
          '${currency.iso}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
        ),
        if (extra != null) extra
      ],
    );
  }

  /// Get the currency symbol for a given ISO code
  static String symbol(BuildContext context, String currencyIso) {
    return _formatter(context, 0).simpleCurrencySymbol(currencyIso);
  }

  static String formattedAmount(
    BuildContext context,
    num amount, [
    int decimalDigits = 2,
    bool allowNegative = false,
  ]) {
    final displayAmount = allowNegative ? amount : (amount < 0 ? 0 : amount);
    return _formatter(context, amount).format(displayAmount);
  }

  @override
  Widget build(BuildContext context) {
    return Text(
        value(context, currencyIso, amount,
            decimalDigits: decimalDigits, allowNegative: allowNegative),
        style: amountStyle ?? Theme.of(context).textTheme.bodyMedium);
  }
}
