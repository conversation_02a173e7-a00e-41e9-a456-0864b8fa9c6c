import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class EmptyWidget extends StatelessWidget {
  final String icon;
  final String title;
  final String subTitle;
  final String? routeName;
  final String? routeInfo;
  final bool baseline;
  const EmptyWidget(
      {super.key,
      required this.icon,
      required this.title,
      required this.subTitle,
      this.baseline = true,
      this.routeName,
      this.routeInfo});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return SizedBox(
      width: 270,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            icon,
            colorFilter:
                ColorFilter.mode(Palette.primaryBlack, BlendMode.srcIn),
          ),
          const Gap(10),
          Text(
            title,
            style: textTheme.headlineSmall,
          ),
          const Gap(5),
          Text(
            subTitle,
            style: textTheme.bodyLarge?.copyWith(color: Palette.blackSecondary),
            textAlign: TextAlign.center,
          ),
          if (baseline) ...[
            const Gap(5),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Palette.primary),
              onPressed: () =>
                  context.pushNamed(routeName ?? kCreateOrderRoute),
              child: Text(
                routeInfo ?? 'Create a new order →',
              ),
            ),
          ]
        ],
      ),
    );
  }
}
