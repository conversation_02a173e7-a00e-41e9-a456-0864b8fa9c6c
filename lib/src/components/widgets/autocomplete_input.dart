import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/search_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/src/components/widgets/compact_input.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class AutocompleteInput extends ConsumerStatefulWidget {
  const AutocompleteInput({
    super.key,
    required this.controller,
    this.hint,
    this.onSelected,
    this.autofocus = false,
    this.enabled = true,
    this.height = 34.0,
    this.borderRadius = 6.0,
    this.focusNode,
  });

  final TextEditingController controller;
  final String? hint;
  final void Function(Variant? variant)? onSelected;
  final bool autofocus;
  final bool enabled;
  final double height;
  final double borderRadius;
  final FocusNode? focusNode;

  @override
  ConsumerState<AutocompleteInput> createState() => _AutocompleteInputState();
}

class _AutocompleteInputState extends ConsumerState<AutocompleteInput> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  Timer? _debounceTimer;
  List<Variant> _suggestions = [];
  bool _isLoading = false;
  int _selectedIndex = -1;
  late final _focusNode = widget.focusNode ?? FocusNode();
  Variant? _selectedVariant;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onSearchChanged);
    (_focusNode..addListener(_onFocusChanged)).requestFocus();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _hideOverlay();
    widget.controller.removeListener(_onSearchChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _hideOverlay();
    } else if (widget.controller.text.isNotEmpty && _selectedVariant == null) {
      _onSearchChanged();
    }
  }

  void _clearSelection() {
    setState(() {
      _selectedVariant = null;
      widget.controller.clear();
    });
    widget.onSelected?.call(null);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_focusNode.canRequestFocus) {
        _focusNode.requestFocus();
      }
    });
  }

  void _onSearchChanged() {
    if (!_focusNode.hasFocus || _selectedVariant != null) return;

    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () async {
      if (widget.controller.text.isEmpty) {
        _hideOverlay();
        return;
      }

      setState(() => _isLoading = true);
      _showOverlay();

      try {
        final res = await ref.read(searchRetailProductsUseCaseProvider(
          SearchParams(
              term: widget.controller.text,
              retailOutletId: ref.read(outletIdProvider) ?? ''),
        ));

        res.when(
          success: (variants) {
            setState(() {
              _suggestions = variants;
              _isLoading = false;
              _selectedIndex = -1;
            });
            _updateOverlay();
          },
          failure: (error, _) {
            setState(() {
              _suggestions = [];
              _isLoading = false;
            });
            _updateOverlay();
          },
        );
      } catch (e) {
        setState(() {
          _suggestions = [];
          _isLoading = false;
        });
        _updateOverlay();
      }
    });
  }

  void _showOverlay() {
    if (_overlayEntry == null) {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    } else {
      _updateOverlay();
    }
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    if (mounted) {
      setState(() {
        _suggestions = [];
        _selectedIndex = -1;
      });
    }
  }

  void _updateOverlay() {
    _overlayEntry?.markNeedsBuild();
  }

  void _onVariantSelected(Variant variant) {
    setState(() {
      _selectedVariant = variant;
      widget.controller.text = variant.name ?? '';
    });
    widget.onSelected?.call(variant);

    _hideOverlay();
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 4),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 200,
                minWidth: size.width,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: Border.all(color: Palette.stroke),
              ),
              child: _buildSuggestionsList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionsList() {
    if (_isLoading) {
      return LinearProgressIndicator(color: Palette.primary);
      // return const Center(
      //   heightFactor: 2,
      //   child: SizedBox(
      //     height: 20,
      //     width: 20,
      //     child: CircularProgressIndicator(strokeWidth: 2),
      //   ),
      // );
    }

    if (_suggestions.isEmpty) {
      return const SizedBox.shrink();
      // return const Center(
      //   heightFactor: 2,
      //   child: Text('No results found'),
      // );
    }

    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: _suggestions.length,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final variant = _suggestions[index];
        final isSelected = index == _selectedIndex;

        return Material(
          color: isSelected
              ? Palette.primary.withValues(alpha: 0.1)
              : Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
            child: Text(
              variant.name ?? '-',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isSelected ? Palette.primary : Palette.primaryBlack,
                  ),
            ),
          ).onTap(() => _onVariantSelected(variant)),
        );
      },
    );
  }

  void _handleKeyEvent(KeyEvent event) {
    if (_suggestions.isEmpty) return;

    if (event is KeyDownEvent) {
      switch (event.logicalKey.keyLabel) {
        case 'Arrow Down':
          setState(() {
            _selectedIndex = (_selectedIndex + 1) % _suggestions.length;
          });
          _updateOverlay();
          break;
        case 'Arrow Up':
          setState(() {
            _selectedIndex = _selectedIndex <= 0
                ? _suggestions.length - 1
                : _selectedIndex - 1;
          });
          _updateOverlay();
          break;
        case 'Enter':
          if (_selectedIndex >= 0 && _selectedIndex < _suggestions.length) {
            _onVariantSelected(_suggestions[_selectedIndex]);
          }
          break;
        case 'Escape':
          if (_selectedVariant != null) {
            _clearSelection();
          } else {
            _hideOverlay();
          }
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: _handleKeyEvent,
        child: Stack(
          children: [
            Container(
              decoration: _selectedVariant != null
                  ? BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Palette.stroke),
                        right: BorderSide(color: Palette.stroke),
                        left: BorderSide(color: Palette.stroke),
                      ),
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                    )
                  : null,
              child: CompactInput(
                controller: widget.controller,
                hint: widget.hint,
                autofocus: widget.autofocus,
                borderRadius: widget.borderRadius,
                focusNode: _focusNode,
                readOnly: _selectedVariant != null,
                decoration: _selectedVariant != null
                    ? InputDecoration(
                        hintText: widget.hint,
                        suffixIcon: IconButton(
                          icon: Icon(
                            Icons.close,
                            size: 14,
                            color: Palette.placeholder,
                          ),
                          onPressed: _clearSelection,
                          splashRadius: 16,
                        ),
                        filled: true,
                        fillColor:
                            widget.enabled ? Colors.white : Palette.kF7F7F7,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        border: OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                          borderSide: BorderSide(color: Palette.stroke),
                        ),
                        enabledBorder: UnderlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                          borderSide: BorderSide(color: Palette.primary),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                          borderSide: BorderSide(
                            color: Palette.primary,
                            width: 1,
                          ),
                        ),
                        constraints:
                            BoxConstraints.tightFor(height: widget.height),
                      )
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
