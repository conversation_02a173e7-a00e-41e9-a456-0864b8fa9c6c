import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class StatusFilteringWidget extends StatelessWidget {
  final String status;

  final bool isSelected;
  final VoidCallback onPressed;
  final double? width;
  final double? height;
  final Color? inactiveItemsColor;
  final EdgeInsetsGeometry? padding;
  const StatusFilteringWidget(
    this.status,
    this.isSelected, {
    super.key,
    required this.onPressed,
    this.width,
    this.height,
    this.inactiveItemsColor,
    this.padding,
  });

  bool get hasDefaultSize => width != null || height != null;

  @override
  Widget build(BuildContext context) {
    final text = Text(
      status,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isSelected
                ? Palette.primaryBlack
                : inactiveItemsColor ?? Palette.blackSecondary,
            fontWeight: FontWeight.w500,
          ),
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
    );

    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(
              color: isSelected ? Palette.primaryBlack : Palette.stroke,
              width: isSelected ? 2 : 1),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: padding ??
              const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        ),
        child: hasDefaultSize
            ? SizedBox(
                width: width,
                height: height,
                child: Center(child: text),
              )
            : text,
      ),
    );
  }
}
