import 'package:flutter/material.dart';

class AnimatedSwitcherWidget extends StatelessWidget {
  const AnimatedSwitcherWidget({super.key, this.child});

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: kThemeAnimationDuration,
      switchInCurve: Curves.easeOut,
      switchOutCurve: Curves.fastOutSlowIn,
      transitionBuilder: (Widget widget, Animation<double> animation) {
        return SizeTransition(
          axis: Axis.vertical,
          axisAlignment: -1.0,
          sizeFactor: animation,
          child: widget,
        );
      },
      child: child,
    );
  }
}
