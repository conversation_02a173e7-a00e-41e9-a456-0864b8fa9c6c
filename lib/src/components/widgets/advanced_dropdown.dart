import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AdvancedDropdown<T> extends StatefulWidget {
  const AdvancedDropdown({
    super.key,
    this.selectedValue,
    required this.onChanged,
    this.options = const [],
    this.hint,
    this.searchHint,
    required this.itemToString,
    this.isExpanded = true,
    this.menuMaxHeight = 200,
    this.title,
    this.searchable = true,
    this.enabled = true,
    this.height = 48.0,
    this.spaceOffsetAbove,
  });

  final T? selectedValue;
  final ValueChanged<T?> onChanged;
  final List<T> options;
  final String? hint;
  final String? searchHint;
  final String Function(T) itemToString;
  final bool isExpanded;
  final double menuMaxHeight;
  final String? title;
  final bool searchable;
  final bool enabled;
  final double height;
  final double? spaceOffsetAbove;

  @override
  State<AdvancedDropdown<T>> createState() => _AdvancedDropdownState<T>();
}

class _AdvancedDropdownState<T> extends State<AdvancedDropdown<T>> {
  final LayerLink _layerLink = LayerLink();
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  List<T> _filteredOptions = [];

  @override
  void initState() {
    super.initState();
    _filteredOptions = widget.options;
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    _searchFocusNode.dispose();
    _searchController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus && _isOpen) {
      _closeDropdown();
    }
  }

  void _toggleDropdown() {
    if (!widget.enabled) return;

    if (_isOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    _filteredOptions = widget.options;
    _searchController.clear();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isOpen = true;
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isOpen && _searchFocusNode.canRequestFocus) {
        _searchFocusNode.requestFocus();
      }
    });
  }

  void _closeDropdown() {
    _removeOverlay();
    setState(() {
      _isOpen = false;
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _filterOptions(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredOptions = widget.options;
      } else {
        _filteredOptions = widget.options
            .where((option) => widget
                .itemToString(option)
                .toLowerCase()
                .contains(query.toLowerCase()))
            .toList();
      }
    });
    _overlayEntry?.markNeedsBuild();
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final screenHeight = MediaQuery.of(context).size.height;
    final spaceBelow = screenHeight - offset.dy - size.height;
    final spaceAbove = offset.dy;

    final Offset menuOffset;
    if (spaceBelow < widget.menuMaxHeight &&
        spaceAbove > widget.menuMaxHeight) {
      if (widget.spaceOffsetAbove != null) {
        menuOffset =
            Offset(0.0, -(widget.menuMaxHeight - widget.spaceOffsetAbove!));
      } else {
        menuOffset = Offset(0.0, -(widget.menuMaxHeight + 4.0));
      }
    } else {
      menuOffset = Offset(0.0, size.height + 4.0);
    }

    return OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: _closeDropdown,
        child: Stack(
          children: [
            Positioned.fill(
              child: GestureDetector(
                onTap: _closeDropdown,
                child: Container(color: Colors.transparent),
              ),
            ),
            Positioned(
              width: size.width,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: menuOffset,
                child: Material(
                  elevation: 8,
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                  child: Container(
                    constraints: BoxConstraints(
                      maxHeight: widget.menuMaxHeight,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widget.searchable) ...[
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 6),
                            child: TextField(
                              focusNode: _searchFocusNode,
                              autofocus: true,
                              controller: _searchController,
                              onChanged: _filterOptions,
                              decoration: InputDecoration(
                                hintText: widget.searchHint ?? '',
                                border: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                isDense: true,
                              ),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(color: Palette.blackSecondary),
                            ),
                          ),
                          Divider(height: 1, color: Palette.stroke),
                        ],
                        if (_filteredOptions.isEmpty)
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Text(
                              'No results found',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(color: Palette.blackSecondary),
                            ),
                          )
                        else
                          Flexible(
                            child: ListView.builder(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              itemCount: _filteredOptions.length,
                              itemBuilder: (context, index) {
                                final option = _filteredOptions[index];
                                final isSelected =
                                    widget.selectedValue == option;

                                return InkWell(
                                  onTap: () {
                                    widget.onChanged(option);
                                    _closeDropdown();
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? Palette.primaryBlack
                                              .withValues(alpha: 0.1)
                                          : null,
                                    ),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            widget.itemToString(option),
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.copyWith(
                                                  color: isSelected
                                                      ? Palette.primaryBlack
                                                      : Palette.blackSecondary,
                                                  fontWeight: isSelected
                                                      ? FontWeight.w600
                                                      : FontWeight.w400,
                                                ),
                                          ),
                                        ),
                                        if (isSelected)
                                          Icon(
                                            Icons.check,
                                            size: 16,
                                            color: Palette.primaryBlack,
                                          ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return CompositedTransformTarget(
      link: _layerLink,
      child: Focus(
        focusNode: _focusNode,
        child: GestureDetector(
          onTap: _toggleDropdown,
          child: Container(
            height: widget.height,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              border: Border.all(
                color: _isOpen ? Palette.primary : Palette.stroke,
                width: _isOpen ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: widget.enabled ? Colors.white : Palette.stroke,
            ),
            child: Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.selectedValue != null
                        ? widget.itemToString(widget.selectedValue as T)
                        : widget.hint ?? 'Select an option',
                    style: textTheme.bodyMedium?.copyWith(
                      color: widget.selectedValue != null
                          ? Palette.primaryBlack
                          : Palette.placeholder,
                      fontWeight: FontWeight.w400,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // const SizedBox(width: 8),
                SizedBox(
                  width: 20,
                  height: 22,
                  child: Stack(
                    // alignment: AlignmentDirectional.center,
                    children: [
                      Positioned(
                        top: 0,
                        child: AnimatedRotation(
                          turns: _isOpen ? 0.5 : 0,
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            size: 16,
                            Icons.keyboard_control_key,
                            // Icons.keyboard_arrow_up,
                            color: widget.enabled
                                ? Palette.primaryBlack
                                : Palette.stroke,
                            blendMode: BlendMode.srcIn,
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        child: AnimatedRotation(
                          turns: _isOpen ? 0 : 0.5,
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            size: 16,
                            Icons.keyboard_control_key,
                            // Icons.keyboard_arrow_down,
                            color: widget.enabled
                                ? Palette.primaryBlack
                                : Palette.stroke,
                            blendMode: BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// class AdvancedDropdown<T> extends StatefulWidget {
//   const AdvancedDropdown({
//     super.key,
//     this.selectedValue,
//     required this.onChanged,
//     this.options = const [],
//     this.hint,
//     this.searchHint,
//     required this.itemToString,
//     this.isExpanded = true,
//     this.menuMaxHeight = 200,
//     this.title,
//     this.searchable = true,
//     this.enabled = true,
//     this.height = 48.0,
//     this.customMenuOffset,
//     this.adjustOffsetForProblematicParents = true,
//   });

//   final T? selectedValue;
//   final ValueChanged<T?> onChanged;
//   final List<T> options;
//   final String? hint;
//   final String? searchHint;
//   final String Function(T) itemToString;
//   final bool isExpanded;
//   final double menuMaxHeight;
//   final String? title;
//   final bool searchable;
//   final bool enabled;
//   final double height;
//   final Offset? customMenuOffset;
//   final bool adjustOffsetForProblematicParents;

//   @override
//   State<AdvancedDropdown<T>> createState() => _AdvancedDropdownState<T>();
// }

// class _AdvancedDropdownState<T> extends State<AdvancedDropdown<T>> {
//   final LayerLink _layerLink = LayerLink();
//   final FocusNode _focusNode = FocusNode();
//   final TextEditingController _searchController = TextEditingController();
//   final FocusNode _searchFocusNode = FocusNode();
//   OverlayEntry? _overlayEntry;
//   bool _isOpen = false;
//   List<T> _filteredOptions = [];

//   @override
//   void initState() {
//     super.initState();
//     _filteredOptions = widget.options;
//     _focusNode.addListener(_onFocusChanged);
//   }

//   @override
//   void dispose() {
//     _focusNode.removeListener(_onFocusChanged);
//     _focusNode.dispose();
//     _searchFocusNode.dispose();
//     _searchController.dispose();
//     _removeOverlay();
//     super.dispose();
//   }

//   void _onFocusChanged() {
//     if (!_focusNode.hasFocus && _isOpen) {
//       _closeDropdown();
//     }
//   }

//   void _toggleDropdown() {
//     if (!widget.enabled) return;

//     if (_isOpen) {
//       _closeDropdown();
//     } else {
//       _openDropdown();
//     }
//   }

//   void _openDropdown() {
//     _filteredOptions = widget.options;
//     _searchController.clear();
//     _overlayEntry = _createOverlayEntry();
//     Overlay.of(context).insert(_overlayEntry!);
//     setState(() {
//       _isOpen = true;
//     });

//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       if (_isOpen && _searchFocusNode.canRequestFocus) {
//         _searchFocusNode.requestFocus();
//       }
//     });
//   }

//   void _closeDropdown() {
//     _removeOverlay();
//     setState(() {
//       _isOpen = false;
//     });
//   }

//   void _removeOverlay() {
//     _overlayEntry?.remove();
//     _overlayEntry = null;
//   }

//   void _filterOptions(String query) {
//     setState(() {
//       if (query.isEmpty) {
//         _filteredOptions = widget.options;
//       } else {
//         _filteredOptions = widget.options
//             .where((option) => widget
//                 .itemToString(option)
//                 .toLowerCase()
//                 .contains(query.toLowerCase()))
//             .toList();
//       }
//     });
//     _overlayEntry?.markNeedsBuild();
//   }

//   OverlayEntry _createOverlayEntry() {
//     final RenderBox renderBox = context.findRenderObject() as RenderBox;
//     final size = renderBox.size;
//     final offset = renderBox.localToGlobal(Offset.zero);
//     final screenHeight = MediaQuery.of(context).size.height;
//     final spaceBelow = screenHeight - offset.dy - size.height;
//     final spaceAbove = offset.dy;

//     final Offset menuOffset;
//     if (spaceBelow < widget.menuMaxHeight &&
//         spaceAbove > widget.menuMaxHeight) {
//       // Menu opens above the dropdown
//       final baseOffset = Offset(0.0, -(widget.menuMaxHeight + 4.0));

//       // Apply custom offset if provided
//       if (widget.customMenuOffset != null) {
//         menuOffset = widget.customMenuOffset!;
//       } else if (widget.adjustOffsetForProblematicParents) {
//         // Apply automatic adjustment for problematic parent widgets
//         final adjustment = _calculateOffsetAdjustment();
//         menuOffset = Offset(baseOffset.dx, baseOffset.dy + adjustment);
//       } else {
//         menuOffset = baseOffset;
//       }
//     } else {
//       // Menu opens below the dropdown
//       menuOffset = Offset(0.0, size.height + 4.0);
//     }

//     return OverlayEntry(
//       builder: (context) => GestureDetector(
//         behavior: HitTestBehavior.opaque,
//         onTap: _closeDropdown,
//         child: Stack(
//           children: [
//             Positioned.fill(
//               child: GestureDetector(
//                 onTap: _closeDropdown,
//                 child: Container(color: Colors.transparent),
//               ),
//             ),
//             Positioned(
//               width: size.width,
//               child: CompositedTransformFollower(
//                 link: _layerLink,
//                 showWhenUnlinked: false,
//                 offset: menuOffset,
//                 child: Material(
//                   elevation: 8,
//                   borderRadius: BorderRadius.circular(8),
//                   color: Colors.white,
//                   child: Container(
//                     constraints: BoxConstraints(
//                       maxHeight: widget.menuMaxHeight,
//                     ),
//                     decoration: BoxDecoration(
//                       borderRadius: BorderRadius.circular(8),
//                       border: Border.all(color: Colors.grey[200]!),
//                     ),
//                     child: Column(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         if (widget.searchable) ...[
//                           Padding(
//                             padding: const EdgeInsets.symmetric(vertical: 6),
//                             child: TextField(
//                               focusNode: _searchFocusNode,
//                               autofocus: true,
//                               controller: _searchController,
//                               onChanged: _filterOptions,
//                               decoration: InputDecoration(
//                                 hintText: widget.searchHint ?? '',
//                                 border: InputBorder.none,
//                                 focusedBorder: InputBorder.none,
//                                 enabledBorder: InputBorder.none,
//                                 contentPadding: const EdgeInsets.symmetric(
//                                   horizontal: 12,
//                                   vertical: 8,
//                                 ),
//                                 isDense: true,
//                               ),
//                               style: Theme.of(context)
//                                   .textTheme
//                                   .bodyMedium
//                                   ?.copyWith(color: Palette.blackSecondary),
//                             ),
//                           ),
//                           Divider(height: 1, color: Palette.stroke),
//                         ],
//                         if (_filteredOptions.isEmpty)
//                           Padding(
//                             padding: const EdgeInsets.symmetric(vertical: 12),
//                             child: Text(
//                               'No results found',
//                               style: Theme.of(context)
//                                   .textTheme
//                                   .bodyMedium
//                                   ?.copyWith(color: Palette.blackSecondary),
//                             ),
//                           )
//                         else
//                           Flexible(
//                             child: ListView.builder(
//                               padding: EdgeInsets.zero,
//                               shrinkWrap: true,
//                               itemCount: _filteredOptions.length,
//                               itemBuilder: (context, index) {
//                                 final option = _filteredOptions[index];
//                                 final isSelected =
//                                     widget.selectedValue == option;

//                                 return InkWell(
//                                   onTap: () {
//                                     widget.onChanged(option);
//                                     _closeDropdown();
//                                   },
//                                   child: Container(
//                                     padding: const EdgeInsets.symmetric(
//                                       horizontal: 16,
//                                       vertical: 12,
//                                     ),
//                                     decoration: BoxDecoration(
//                                       color: isSelected
//                                           ? Palette.primaryBlack
//                                               .withValues(alpha: 0.1)
//                                           : null,
//                                     ),
//                                     child: Row(
//                                       children: [
//                                         Expanded(
//                                           child: Text(
//                                             widget.itemToString(option),
//                                             style: Theme.of(context)
//                                                 .textTheme
//                                                 .bodyMedium
//                                                 ?.copyWith(
//                                                   color: isSelected
//                                                       ? Palette.primaryBlack
//                                                       : Palette.blackSecondary,
//                                                   fontWeight: isSelected
//                                                       ? FontWeight.w600
//                                                       : FontWeight.w400,
//                                                 ),
//                                           ),
//                                         ),
//                                         if (isSelected)
//                                           Icon(
//                                             Icons.check,
//                                             size: 16,
//                                             color: Palette.primaryBlack,
//                                           ),
//                                       ],
//                                     ),
//                                   ),
//                                 );
//                               },
//                             ),
//                           ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   /// Calculates offset adjustment based on parent widget context
//   double _calculateOffsetAdjustment() {
//     // First, check for InheritedWidget context (most reliable)
//     final positioningContext = DropdownPositioningContext.of(context);
//     if (positioningContext != null) {
//       return positioningContext.offsetAdjustment;
//     }

//     // Fallback to string-based detection for backward compatibility
//     if (_isInsideSmoothExpandable()) {
//       return 200.0; // Your manual adjustment value for SmoothExpandable
//     }

//     // Check for other problematic parent widgets
//     if (_isInsideAnimatedSwitcher()) {
//       return 100.0; // Smaller adjustment for animated containers
//     }

//     if (_isInsideCustomExpandable()) {
//       return 150.0; // Different adjustment for other expandable widgets
//     }

//     return 0.0; // No adjustment needed
//   }

//   /// Checks if the dropdown is inside a SmoothExpandable widget
//   bool _isInsideSmoothExpandable() {
//     try {
//       // Traverse up the widget tree to find SmoothExpandable
//       final ancestor = context.findAncestorWidgetOfExactType<Widget>();
//       if (ancestor != null) {
//         final widgetName = ancestor.runtimeType.toString();
//         return widgetName.contains('SmoothExpandable');
//       }
//     } catch (e) {
//       // Silently handle any errors
//     }
//     return false;
//   }

//   /// Checks if the dropdown is inside an AnimatedSwitcher
//   bool _isInsideAnimatedSwitcher() {
//     try {
//       final ancestor =
//           context.findAncestorWidgetOfExactType<AnimatedSwitcher>();
//       return ancestor != null;
//     } catch (e) {
//       return false;
//     }
//   }

//   /// Checks if the dropdown is inside other custom expandable widgets
//   bool _isInsideCustomExpandable() {
//     try {
//       final ancestor = context.findAncestorWidgetOfExactType<Widget>();
//       if (ancestor != null) {
//         final widgetName = ancestor.runtimeType.toString();
//         return widgetName.contains('Expandable') &&
//             !widgetName.contains('SmoothExpandable');
//       }
//     } catch (e) {
//       // Silently handle any errors
//     }
//     return false;
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return CompositedTransformTarget(
//       link: _layerLink,
//       child: Focus(
//         focusNode: _focusNode,
//         child: GestureDetector(
//           onTap: _toggleDropdown,
//           child: Container(
//             height: widget.height,
//             padding: const EdgeInsets.symmetric(horizontal: 16),
//             decoration: BoxDecoration(
//               border: Border.all(
//                 color: _isOpen ? Palette.primary : Palette.stroke,
//                 width: _isOpen ? 2 : 1,
//               ),
//               borderRadius: BorderRadius.circular(8),
//               color: widget.enabled ? Colors.white : Palette.stroke,
//             ),
//             child: Row(
//               // mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Expanded(
//                   child: Text(
//                     widget.selectedValue != null
//                         ? widget.itemToString(widget.selectedValue as T)
//                         : widget.hint ?? 'Select an option',
//                     style: textTheme.bodyMedium?.copyWith(
//                       color: widget.selectedValue != null
//                           ? Palette.primaryBlack
//                           : Palette.placeholder,
//                       fontWeight: FontWeight.w400,
//                     ),
//                     maxLines: 1,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ),
//                 // const SizedBox(width: 8),
//                 SizedBox(
//                   width: 20,
//                   height: 22,
//                   child: Stack(
//                     // alignment: AlignmentDirectional.center,
//                     children: [
//                       Positioned(
//                         top: 0,
//                         child: AnimatedRotation(
//                           turns: _isOpen ? 0.5 : 0,
//                           duration: const Duration(milliseconds: 200),
//                           child: Icon(
//                             size: 16,
//                             Icons.keyboard_control_key,
//                             // Icons.keyboard_arrow_up,
//                             color: widget.enabled
//                                 ? Palette.primaryBlack
//                                 : Palette.stroke,
//                             blendMode: BlendMode.srcIn,
//                           ),
//                         ),
//                       ),
//                       Positioned(
//                         bottom: 0,
//                         child: AnimatedRotation(
//                           turns: _isOpen ? 0 : 0.5,
//                           duration: const Duration(milliseconds: 200),
//                           child: Icon(
//                             size: 16,
//                             Icons.keyboard_control_key,
//                             // Icons.keyboard_arrow_down,
//                             color: widget.enabled
//                                 ? Palette.primaryBlack
//                                 : Palette.stroke,
//                             blendMode: BlendMode.srcIn,
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

// /// InheritedWidget to provide dropdown positioning context
// class DropdownPositioningContext extends InheritedWidget {
//   const DropdownPositioningContext({
//     super.key,
//     required super.child,
//     required this.offsetAdjustment,
//     required this.contextType,
//   });

//   final double offsetAdjustment;
//   final String contextType;

//   static DropdownPositioningContext? of(BuildContext context) {
//     return context
//         .dependOnInheritedWidgetOfExactType<DropdownPositioningContext>();
//   }

//   @override
//   bool updateShouldNotify(DropdownPositioningContext oldWidget) {
//     return offsetAdjustment != oldWidget.offsetAdjustment ||
//         contextType != oldWidget.contextType;
//   }
// }

// /// Widget wrapper for SmoothExpandable to provide positioning context
// class SmoothExpandableWithDropdownContext extends StatelessWidget {
//   const SmoothExpandableWithDropdownContext({
//     super.key,
//     required this.child,
//     this.offsetAdjustment = 200.0,
//   });

//   final Widget child;
//   final double offsetAdjustment;

//   @override
//   Widget build(BuildContext context) {
//     return DropdownPositioningContext(
//       offsetAdjustment: offsetAdjustment,
//       contextType: 'SmoothExpandable',
//       child: child,
//     );
//   }
// }
