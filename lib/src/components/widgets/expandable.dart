import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class Expandable extends StatefulWidget {
  const Expandable({
    super.key,
    this.header,
    this.content,
    this.expanded = true,
  });

  final Widget? header;
  final Widget? content;
  final bool expanded;

  @override
  ExpandableState createState() => ExpandableState();
}

class ExpandableState extends State<Expandable>
    with SingleTickerProviderStateMixin {
  late bool _isExpanded = widget.expanded;
  late AnimationController _controller;
  late Animation<double> _heightFactor;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _heightFactor = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: _toggleExpand,
          child: Container(
            height: 36,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: Palette.kF7F7F7,
              border: Border(bottom: BorderSide(color: Palette.stroke)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                widget.header != null ? widget.header! : Container(),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: _isExpanded
                      ? SvgPicture.asset(
                          '$kSvgDir/order/chevron_up.svg',
                          width: 24,
                          height: 24,
                        )
                      : SvgPicture.asset(
                          '$kSvgDir/order/chevron_down.svg',
                          width: 24,
                          height: 24,
                        ),
                ),
              ],
            ),
          ),
        ),
        AnimatedBuilder(
          animation: _heightFactor,
          builder: (context, child) => ClipRect(
            child: Align(
              heightFactor: _heightFactor.value,
              child: Container(
                color: Colors.white,
                child: child,
              ),
            ),
          ),
          child: widget.content,
        ),
      ],
    );
  }
}
