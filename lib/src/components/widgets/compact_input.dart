import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class CompactInput extends StatelessWidget {
  const CompactInput({
    super.key,
    required this.controller,
    this.hint,
    this.prefix,
    this.suffix,
    this.keyboardType,
    this.inputFormatters,
    this.onEditingComplete,
    this.onChanged,
    this.autofocus = false,
    this.enabled = true,
    this.readOnly = false,
    this.height = 34.0,
    this.borderRadius = 6.0,
    this.focusNode,
    this.decoration,
    this.suffixIcon,
  });

  final TextEditingController controller;
  final String? hint;
  final String? prefix;
  final String? suffix;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onChanged;
  final bool autofocus;
  final bool enabled;
  final bool readOnly;
  final double height;
  final double borderRadius;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final Widget? suffixIcon;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return SizedBox(
      // height: height,
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        onEditingComplete: onEditingComplete,
        onChanged: onChanged,
        autofocus: autofocus,
        focusNode: focusNode,
        enabled: enabled,
        readOnly: readOnly,
        style: textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w400,
          color: enabled ? Palette.primaryBlack : Palette.placeholder,
        ),
        decoration: decoration ?? InputDecoration(
          hintText: hint,
          hintStyle: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w400,
            color: Palette.placeholder,
          ),
          prefixText: prefix,
          suffixText: suffix,
          suffixIcon: suffixIcon,
          filled: true,
          fillColor: enabled ? Colors.white : Palette.kF7F7F7,
          // isDense: true,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(color: Palette.stroke),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(color: Palette.stroke),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(
              color: Palette.primary,
              width: 1,
            ),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(
              color: Palette.stroke.withValues(alpha: 0.5),
            ),
          ),
          constraints: BoxConstraints.tightFor(height: height),
        ),
      ),
    );
  }
}
