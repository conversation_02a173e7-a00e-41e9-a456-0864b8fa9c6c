import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class StreamedImageWidget extends StatefulWidget {
  final String imageUrl;

  const StreamedImageWidget({super.key, required this.imageUrl});

  @override
  State<StreamedImageWidget> createState() => _StreamedImageWidgetState();
}

class _StreamedImageWidgetState extends State<StreamedImageWidget> {
  Uint8List? _imageBytes;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final dio = Dio();

      final response = await dio.get<List<int>>(
        widget.imageUrl,
        options: Options(
          responseType: ResponseType.bytes,
          // headers: {'Authorization': 'Bearer your-token'}, // if needed
        ),
      );

      if (response.statusCode == 200) {
        if (mounted) {
          setState(() {
            _imageBytes = Uint8List.fromList(response.data!);
            _loading = false;
          });
        }
      } else {
        throw Exception("Image load failed with status ${response.statusCode}");
      }
    } catch (e) {
      debugPrint("Image fetch error: $e");
      if (mounted) {
        setState(() {
          _loading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return SizedBox(
        width: 35,
        height: 35,
        child: Transform.scale(
          scale: 0.4,
          child: const CircularProgressIndicator(),
        ),
      );
    }
    if (_imageBytes == null) return const Icon(Icons.broken_image);
    return Image.memory(_imageBytes!);
  }
}
