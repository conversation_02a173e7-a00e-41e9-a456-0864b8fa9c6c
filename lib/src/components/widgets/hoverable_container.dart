import 'package:flutter/material.dart';

class HoverableContainer extends StatefulWidget {
  final int index;
  final Widget? child;
  final Widget Function(bool isHovered)? builder;
  final ValueChanged<int>? onHoverChanged;
  final bool? isLastIndex;
  final EdgeInsetsGeometry? margin;

  const HoverableContainer({
    super.key,
    required this.index,
    this.child,
    this.builder,
    this.onHoverChanged,
    this.isLastIndex = false,
    this.margin,
  });

  @override
  HoverableContainerState createState() => HoverableContainerState();
}

class HoverableContainerState extends State<HoverableContainer> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: Container(
        margin: widget.margin ??
            EdgeInsets.only(bottom: widget.isLastIndex! ? 20 : 0),
        decoration: BoxDecoration(
          color: _isHovered ? Colors.grey[100] : Colors.transparent,
        ),
        child: widget.child ?? (widget.builder?.call(_isHovered)),
      ),
    );
  }

  void _onHover(bool isHovered) {
    // Only update the state if the hover state has actually changed
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      widget.onHoverChanged?.call(widget.index);
    }
  }
}
