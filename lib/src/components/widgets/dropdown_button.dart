import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class DropdownButtonWidget<T> extends StatelessWidget {
  const DropdownButtonWidget({
    super.key,
    this.selectedValue,
    required this.onChanged,
    this.options = const [],
    this.hint,
    required this.itemToString,
    this.isExpanded = false,
    this.menuMaxHeight,
    this.title,
  });

  final T? selectedValue;
  final ValueChanged<T?> onChanged;
  final List<T> options;
  final String? hint;
  final String Function(T) itemToString;
  final bool isExpanded;
  final double? menuMaxHeight;
  final String? title;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final T? initialValue = selectedValue ??
        (title == null && options.isNotEmpty ? options.first : null);

    return DropdownButtonFormField<T>(
      isExpanded: isExpanded,
      menuMaxHeight: menuMaxHeight,
      value: initialValue,
      hint: hint != null ? Text(hint!, style: textTheme.bodyMedium) : null,
      icon: Skeleton.shade(
        child: SvgPicture.asset(
          '$kSvgDir/order/chevron_down.svg',
          colorFilter: ColorFilter.mode(Palette.primaryBlack, BlendMode.dst),
        ),
      ),
      onChanged: (T? newValue) {
        onChanged(newValue);
      },
      items: [
        if (title != null)
          DropdownMenuItem<T>(
            value: null,
            enabled: false,
            child: Text(
              title!,
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Palette.blackSecondary,
              ),
              // overflow: TextOverflow.ellipsis,
            ),
          ),
        ...options.map<DropdownMenuItem<T>>((T option) {
          return DropdownMenuItem<T>(
            value: option,
            child: Text(
              itemToString(option),
              style: textTheme.bodyMedium,
              // overflow: TextOverflow.ellipsis,
            ),
          );
        }),
      ],
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.transparent),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.transparent),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.transparent),
        ),
      ),
      dropdownColor: Colors.white,
      focusColor: Colors.transparent,
      borderRadius: BorderRadius.zero,
    );
  }
}




// class DropdownButtonWidget<T> extends StatelessWidget {
//   const DropdownButtonWidget({
//     super.key,
//     required this.selectedValue,
//     required this.onChanged,
//     this.options = const [],
//     this.hint,
//     required this.itemToString,
//     this.isExpanded = false,
//     this.menuMaxHeight,
//   });

//   final T selectedValue;
//   final ValueChanged<T?> onChanged;
//   final List<T> options;
//   final String? hint;
//   final String Function(T) itemToString;
//   final bool isExpanded;
//   final double? menuMaxHeight;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return DropdownButtonFormField<T>(
//       isExpanded: isExpanded,
//       menuMaxHeight: menuMaxHeight,
//       value: selectedValue,
//       hint: hint != null ? Text(hint!, style: textTheme.bodyMedium) : null,
//       icon: Skeleton.shade(
//         child: SvgPicture.asset(
//           '$kSvgDir/order/chevron_down.svg',
//           colorFilter: ColorFilter.mode(Palette.primaryBlack, BlendMode.dst),
//         ),
//       ),
//       onChanged: (T? newValue) {
//         onChanged(newValue);
//       },
//       items: options.map<DropdownMenuItem<T>>((T option) {
//         return DropdownMenuItem<T>(
//           value: option,
//           child: Text(
//             itemToString(option),
//             style: textTheme.bodyMedium,
//             overflow: TextOverflow.ellipsis,
//           ),
//         );
//       }).toList(),
//       decoration: InputDecoration(
//         border: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(8),
//           borderSide: BorderSide(color: Palette.stroke),
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(8),
//           borderSide: BorderSide(color: Palette.primary),
//         ),
//       ),
//       dropdownColor: Colors.white,
//       focusColor: Colors.transparent,
//       borderRadius: BorderRadius.circular(8),
//     );
//   }
// }
