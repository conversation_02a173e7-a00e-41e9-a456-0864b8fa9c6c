import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// A service that provides enhanced image caching with CORS handling
class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();

  // Cache for successful URLs
  final Map<String, String> _urlCache = {};

  // Cache for failed URLs to avoid retrying immediately
  final Map<String, DateTime> _failedUrlCache = {};

  // Maximum cache size
  static const int _maxCacheSize = 1000;

  // Time to wait before retrying a failed URL (in minutes)
  static const int _retryDelayMinutes = 5;

  /// Get a working image URL for the given variant ID and size
  /// Returns null if no working URL is found
  Future<String?> getWorkingImageUrl({
    required String variantId,
    required List<String> candidateUrls,
  }) async {
    if (variantId.isEmpty || candidateUrls.isEmpty) return null;

    final cacheKey = variantId;

    // Check if we have a cached successful URL
    if (_urlCache.containsKey(cacheKey)) {
      return _urlCache[cacheKey];
    }

    // Try each candidate URL
    for (final url in candidateUrls) {
      // Skip URLs that recently failed
      if (_isRecentlyFailed(url)) continue;

      if (await _testImageUrl(url)) {
        _cacheSuccessfulUrl(cacheKey, url);
        return url;
      } else {
        _cacheFailedUrl(url);
      }
    }

    return null;
  }

  /// Test if an image URL is accessible
  Future<bool> _testImageUrl(String url) async {
    try {
      final imageProvider = NetworkImage(url);
      final imageStream = imageProvider.resolve(ImageConfiguration.empty);

      final completer = Completer<bool>();
      late ImageStreamListener listener;

      listener = ImageStreamListener(
        (ImageInfo image, bool synchronousCall) {
          imageStream.removeListener(listener);
          completer.complete(true);
        },
        onError: (exception, stackTrace) {
          imageStream.removeListener(listener);
          completer.complete(false);
        },
      );

      imageStream.addListener(listener);

      // Test with a shorter timeout for efficiency
      return await completer.future.timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          imageStream.removeListener(listener);
          return false;
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Failed to test image URL $url: $e');
      }
      return false;
    }
  }

  /// Cache a successful URL
  void _cacheSuccessfulUrl(String key, String url) {
    // Implement LRU cache behavior
    if (_urlCache.length >= _maxCacheSize) {
      final firstKey = _urlCache.keys.first;
      _urlCache.remove(firstKey);
    }
    _urlCache[key] = url;
  }

  /// Cache a failed URL with timestamp
  void _cacheFailedUrl(String url) {
    _failedUrlCache[url] = DateTime.now();

    // Clean up old failed entries
    if (_failedUrlCache.length > _maxCacheSize) {
      final oldEntries = _failedUrlCache.entries
          .where((entry) =>
              DateTime.now().difference(entry.value).inMinutes >
              _retryDelayMinutes)
          .map((entry) => entry.key)
          .toList();

      for (final key in oldEntries) {
        _failedUrlCache.remove(key);
      }
    }
  }

  /// Check if a URL has recently failed
  bool _isRecentlyFailed(String url) {
    final failTime = _failedUrlCache[url];
    if (failTime == null) return false;

    final timeDiff = DateTime.now().difference(failTime).inMinutes;
    return timeDiff < _retryDelayMinutes;
  }

  /// Clear all caches
  void clearCache() {
    _urlCache.clear();
    _failedUrlCache.clear();
  }

  /// Get cache statistics for debugging
  Map<String, dynamic> getCacheStats() {
    return {
      'successfulUrls': _urlCache.length,
      'failedUrls': _failedUrlCache.length,
      'maxCacheSize': _maxCacheSize,
      'retryDelayMinutes': _retryDelayMinutes,
    };
  }
}
