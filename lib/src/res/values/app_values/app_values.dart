import 'package:td_commons_flutter/models/currency.dart';

final defaultCurrency = Currency(symbol: '£', iso: 'GBP');

String countryCodeFromPhone(String phone) {
  const countryCodeMap = {
    '+234': 'NG',
    '+233': 'GH',
    '+27': 'ZA',
    '+44': 'GB',
  };

  return countryCodeMap.entries
      .firstWhere(
        (entry) => phone.trim().startsWith(entry.key),
        orElse: () => const MapEntry('', ''),
      )
      .value;
}

enum Country {
  britain('GB');

  final String slug;
  const Country(this.slug);
}
