import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

final lightTheme = ThemeData(
  secondaryHeaderColor: Palette.kFCFCFC,
  scaffoldBackgroundColor: Colors.white,
  fontFamily: 'Pretendard Variable',
  colorSchemeSeed: Palette.primary,
  textTheme: TextTheme(
    headlineSmall: TextStyle(
        fontWeight: FontWeight.w700, fontSize: 18, color: Palette.primaryBlack),
    headlineMedium: TextStyle(
      fontWeight: FontWeight.w800,
      fontSize: 24,
      color: Palette.primaryBlack,
    ),
    titleMedium: TextStyle(
        fontWeight: FontWeight.w600, fontSize: 24, color: Palette.primaryBlack),
    labelMedium: TextStyle(
        fontWeight: FontWeight.w500, fontSize: 24, color: Palette.primaryBlack),
    bodyMedium: TextStyle(
        fontWeight: FontWeight.w400, fontSize: 14, color: Palette.primaryBlack),
    bodyLarge: TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 16,
      color: Palette.primaryBlack,
    ),
    bodySmall: TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 12,
      color: Palette.primaryBlack,
    ),
  ),
  dropdownMenuTheme: DropdownMenuThemeData(
    inputDecorationTheme: InputDecorationTheme(
      hintStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          color: Palette.primaryBlack),
      errorMaxLines: 2,
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Palette.kE7E7E7),
      ),
      constraints: BoxConstraints.tight(
        const Size.fromHeight(34),
      ),
    ),
    menuStyle: const MenuStyle(
      backgroundColor: WidgetStatePropertyAll(Colors.white),
    ),
    textStyle: TextStyle(
        fontWeight: FontWeight.w500, fontSize: 14, color: Palette.primaryBlack),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: false,
    contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
    hintStyle: TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 14,
      color: Palette.placeholder,
    ),
    outlineBorder: BorderSide(
      color: Palette.stroke,
    ),
    disabledBorder: _defaultInputBorder,
    border: _defaultInputBorder,
    errorBorder: _defaultInputBorder,
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(
        color: Palette.primary,
      ),
    ),
    focusedErrorBorder: _defaultInputBorder,
    enabledBorder: _defaultInputBorder,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: 0,
      foregroundColor: Colors.white,
      backgroundColor: Palette.primaryBlack,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
  ),
  filledButtonTheme: FilledButtonThemeData(
    style: FilledButton.styleFrom(
      foregroundColor: Colors.white,
      backgroundColor: Palette.primaryBlack,
      padding: const EdgeInsets.all(10),
      textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      minimumSize: const Size(389, 49),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: Palette.primary,
      backgroundColor: Colors.transparent,
      padding: const EdgeInsets.all(10),
      textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
    ),
  ),
);

InputBorder _defaultInputBorder = OutlineInputBorder(
  borderRadius: BorderRadius.circular(8),
  borderSide: BorderSide(
    color: Palette.stroke,
  ),
);
