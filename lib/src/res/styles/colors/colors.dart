import 'dart:ui';

class Palette {
  static final primary = HexColor("#FF8D06");
  static final placeholder = HexColor("#AEAEAE");
  static final stroke = HexColor("#E7E7E7");
  static final strokePressed = HexColor("#357AE1");
  static final blackSecondary = HexColor("#6B797C");
  static final primaryBlack = HexColor("#081F24");
  static final brandBlack = HexColor("#717A8E");
  static final orangePrimaryDark = HexColor("#081F24");
  static final statusSuccess = HexColor("#08AA49");
  static final statusPending = HexColor("#FF8D06");
  static final statusVerifying = HexColor("#0679FF");
  static final deleteRed = HexColor("FF4206");
  static final kE7E7E7 = HexColor("#E7E7E7");
  static final k6B797C = HexColor("#6B797C");
  static final kFCFCFC = HexColor("#FCFCFC");
  static final kECEDED = HexColor("#ECEDED");
  static final kEEEEEE = HexColor("#EEEEEE");
  static final kF7F7F7 = HexColor("#F7F7F7");
  static final k202020 = HexColor("#202020");
  static final k0CA653 = HexColor("#0CA653");
  static final kE61010 = HexColor("#E61010");
  static final kFFFFFF = HexColor("#FFFFFF");
  static final k458558 = HexColor("#458558");
  static final kFF4206 = HexColor("#FF4206");
  static final kEFEFEF = HexColor("#EFEFEF");
  static final k0679FF = HexColor("#0679FF");
  static const k0000000A = Color.fromRGBO(0, 0, 0, 0.04);
  static const k0000001A = Color.fromRGBO(0, 0, 0, 0.1);
  static const k0000002B = Color.fromRGBO(0, 0, 0, 0.17);
}

class HexColor extends Color {
  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor";
    }
    return int.parse(hexColor, radix: 16);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));
}
