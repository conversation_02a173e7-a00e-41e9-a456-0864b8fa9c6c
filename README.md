# TD Procurement

This is the official repository for the TD Procurement Flutter application.

## Getting Started

To get started with the development, follow these steps:

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/tradedepot/td-procurement.git
    ```
2.  **Install dependencies:**
    ```bash
    flutter pub get
    ```
3.  **Run the application:**
    ```bash
    flutter run
    ```

## Architecture

The application follows a feature-based architecture. For a detailed explanation of the architecture, please refer to the [ARCHITECTURE.md](ARCHITECTURE.md) file.

## License

This project is licensed under the [MIT License](LICENSE).
