import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/src/components/widgets/cached_image.dart';
import 'package:td_procurement/src/services/image_cache_service.dart';

/// Example demonstrating the enhanced CachedImage widget usage
class CachedImageExample extends ConsumerStatefulWidget {
  const CachedImageExample({super.key});

  @override
  ConsumerState<CachedImageExample> createState() => _CachedImageExampleState();
}

class _CachedImageExampleState extends ConsumerState<CachedImageExample> {
  final ImageCacheService _imageService = ImageCacheService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CachedImage Examples'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _imageService.clearCache();
              setState(() {}); // Refresh the page
            },
            tooltip: 'Clear Cache',
          ),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: _showCacheStats,
            tooltip: 'Cache Stats',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              'Basic Usage',
              'Simple image loading with automatic CORS handling',
              [
                _buildImageCard(
                  'Small Image',
                  const CachedImage('sample-variant-1', ImageSize.small),
                ),
                _buildImageCard(
                  'Large Image',
                  const CachedImage('sample-variant-2', ImageSize.large),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Custom Sizing',
              'Images with specific dimensions and fit modes',
              [
                _buildImageCard(
                  'Fixed Size (100x100)',
                  const CachedImage(
                    'sample-variant-3',
                    ImageSize.small,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                  ),
                ),
                _buildImageCard(
                  'Aspect Ratio (200x100)',
                  const CachedImage(
                    'sample-variant-4',
                    ImageSize.large,
                    width: 200,
                    height: 100,
                    fit: BoxFit.contain,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Custom Widgets',
              'Images with custom placeholder and error widgets',
              [
                _buildImageCard(
                  'Custom Placeholder',
                  CachedImage(
                    'sample-variant-5',
                    ImageSize.small,
                    width: 120,
                    height: 120,
                    placeholder: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('Loading...', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                  ),
                ),
                _buildImageCard(
                  'Custom Error Widget',
                  CachedImage(
                    '', // Empty ID to trigger error
                    ImageSize.small,
                    width: 120,
                    height: 120,
                    errorWidget: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.red[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red),
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error, color: Colors.red, size: 32),
                          SizedBox(height: 8),
                          Text(
                            'Failed to load',
                            style: TextStyle(fontSize: 12, color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Grid Layout',
              'Multiple images in a responsive grid',
              [
                _buildImageGrid(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
      String title, String description, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildImageCard(String title, Widget image) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 12),
            Center(child: image),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid() {
    final variantIds = [
      'grid-variant-1',
      'grid-variant-2',
      'grid-variant-3',
      'grid-variant-4',
      'grid-variant-5',
      'grid-variant-6',
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: variantIds.length,
          itemBuilder: (context, index) {
            return CachedImage(
              variantIds[index],
              ImageSize.small,
              fit: BoxFit.cover,
            );
          },
        ),
      ),
    );
  }

  void _showCacheStats() {
    final stats = _imageService.getCacheStats();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cache Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Successful URLs: ${stats['successfulUrls']}'),
            Text('Failed URLs: ${stats['failedUrls']}'),
            Text('Max Cache Size: ${stats['maxCacheSize']}'),
            Text('Retry Delay: ${stats['retryDelayMinutes']} minutes'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Example app to run the CachedImage examples
class CachedImageExampleApp extends StatelessWidget {
  const CachedImageExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        title: 'CachedImage Example',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: const CachedImageExample(),
      ),
    );
  }
}

void main() {
  runApp(const CachedImageExampleApp());
}
