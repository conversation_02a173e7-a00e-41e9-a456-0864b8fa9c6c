# OpenCode Guidelines for td-procurement

This document provides essential commands and code style guidelines for development in this Flutter project.

**Build/Lint/Test Commands:**

- **Build:** `flutter build`
- **Run all tests:** `flutter test`
- **Run a single test file:** `flutter test <path_to_test_file>`
- **Run analyzer/linter:** `flutter analyze`
- **Format code:** `dart format .`

**Code Style Guidelines:**

- Follow the Dart effective style guide.
- Refer to `analysis_options.yaml` for specific lint rules.
- **Imports:** Grouped and sorted.
- **Formatting:** Use `dart format`.
- **Naming:** `lowerCamelCase` for variables/functions, `UpperCamelCase` for classes/enums, `snake_case` for files.
- **Types:** Use strong typing.
- **Error Handling:** Use `try/catch` and appropriate exceptions.
- Prefer `const` and `final` keywords.
- Avoid unnecessary comments; let the code be self-documenting where possible.
