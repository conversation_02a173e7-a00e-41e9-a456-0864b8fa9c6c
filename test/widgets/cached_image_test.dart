import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:td_procurement/src/components/widgets/cached_image.dart';
import 'package:td_procurement/src/services/image_cache_service.dart';

void main() {
  group('CachedImage Widget Tests', () {
    testWidgets('should show placeholder when loading',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CachedImage('test-variant-id', ImageSize.small),
            ),
          ),
        ),
      );

      // Should show placeholder initially
      expect(find.byType(Image), findsOneWidget);
    });

    testWidgets('should handle null variant ID gracefully',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CachedImage(null, ImageSize.small),
            ),
          ),
        ),
      );

      await tester.pump();

      // Should show error/placeholder widget
      expect(find.byType(Image), findsOneWidget);
    });

    testWidgets('should handle empty variant ID gracefully',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CachedImage('', ImageSize.small),
            ),
          ),
        ),
      );

      await tester.pump();

      // Should show error/placeholder widget
      expect(find.byType(Image), findsOneWidget);
    });

    testWidgets('should use custom placeholder when provided',
        (WidgetTester tester) async {
      const customPlaceholder = Text('Loading...');

      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CachedImage(
                'test-variant-id',
                ImageSize.small,
                placeholder: customPlaceholder,
              ),
            ),
          ),
        ),
      );

      // Should show custom placeholder
      expect(find.text('Loading...'), findsOneWidget);
    });

    testWidgets('should use custom error widget when provided',
        (WidgetTester tester) async {
      const customErrorWidget = Text('Error loading image');

      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CachedImage(
                null, // This will trigger error state
                ImageSize.small,
                errorWidget: customErrorWidget,
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Should show custom error widget
      expect(find.text('Error loading image'), findsOneWidget);
    });

    testWidgets('should apply width and height when provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CachedImage(
                'test-variant-id',
                ImageSize.small,
                width: 100,
                height: 100,
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Find the Image widget and check its properties
      final imageFinder = find.byType(Image);
      expect(imageFinder, findsOneWidget);

      final imageWidget = tester.widget<Image>(imageFinder);
      expect(imageWidget.width, equals(100));
      expect(imageWidget.height, equals(100));
    });
  });

  group('ImageCacheService Tests', () {
    late ImageCacheService service;

    setUp(() {
      service = ImageCacheService();
      service.clearCache(); // Start with clean cache
    });

    test('should return null for empty variant ID', () async {
      final result = await service.getWorkingImageUrl(
        variantId: '',
        candidateUrls: ['https://example.com/image.png'],
      );
      expect(result, isNull);
    });

    test('should return null for empty candidate URLs', () async {
      final result = await service.getWorkingImageUrl(
        variantId: 'test-id',
        candidateUrls: [],
      );
      expect(result, isNull);
    });

    test('should provide cache statistics', () {
      final stats = service.getCacheStats();
      expect(stats, isA<Map<String, dynamic>>());
      expect(stats.containsKey('successfulUrls'), isTrue);
      expect(stats.containsKey('failedUrls'), isTrue);
      expect(stats.containsKey('maxCacheSize'), isTrue);
      expect(stats.containsKey('retryDelayMinutes'), isTrue);
    });

    test('should clear cache successfully', () {
      service.clearCache();
      final stats = service.getCacheStats();
      expect(stats['successfulUrls'], equals(0));
      expect(stats['failedUrls'], equals(0));
    });
  });
}
