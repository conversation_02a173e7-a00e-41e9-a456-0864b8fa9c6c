import 'dart:convert';
import 'dart:io';

Future<void> main() async {
  final config = {
    'INTERCOM_APP_ID': Platform.environment['INTERCOM_APP_ID'],
    'FIREBASE_SERVICE_URL': Platform.environment['FIREBASE_SERVICE_URL'],
    'APP_URL': Platform.environment['APP_URL'],
    'CONSOLE_URL': Platform.environment['CONSOLE_URL'],
    'AWS_API_URL': Platform.environment['AWS_API_URL'],
    'AWS_API_URL_V2': Platform.environment['AWS_API_URL_V2'],
    'AWS_API_URL_V3': Platform.environment['AWS_API_URL_V3'],
    'AWS_API_URL_V4': Platform.environment['AWS_API_URL_V4'],
    'GOOGLE_API_KEY': Platform.environment['GOOGLE_API_KEY'],
    'HUBSPOT_API_KEY': Platform.environment['HUBSPOT_API_KEY'],
    'FLUTTER_APP_FLAVOR': Platform.environment['FLUTTER_APP_FLAVOR'],
    'SENTRY_DSN': Platform.environment['SENTRY_DSN'],
    'SEARCH_URL': Platform.environment['SEARCH_URL']
  };

  const kFileName = 'lib/core/env/.env.dart';
  final file = await File(kFileName).create(recursive: true);
  if (file.existsSync()) {
    file.writeAsString("final appConfig = ${json.encode(config)};");
  }
}
