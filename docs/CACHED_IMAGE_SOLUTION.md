# CachedImage CORS Solution

## Problem Overview

The original `CachedImage` widget was experiencing CORS (Cross-Origin Resource Sharing) errors when loading images from AWS S3 URLs. The main issues were:

1. **Client-side CORS headers**: The original implementation tried to set CORS headers on the client side, which doesn't work. CORS headers must be set by the server.
2. **WebGL SecurityErrors**: Specific errors like "Failed to execute 'texImage2D' on 'WebGL2RenderingContext': The image element contains cross-origin data, and may not be loaded"
3. **Limited fallback mechanisms**: Basic error handling without intelligent URL testing
4. **No caching strategy**: Lost caching benefits when switching from CachedNetworkImage to Image.network

## Solution Architecture

### 1. Enhanced CachedImage Widget

**File**: `lib/src/components/widgets/cached_image.dart`

**Key Features**:
- **Smart URL Testing**: Tests multiple candidate URLs to find working ones
- **Intelligent Caching**: Uses `ImageCacheService` for efficient URL caching
- **Graceful Fallbacks**: Falls back from production to development URLs
- **Enhanced Error Handling**: Comprehensive error handling with proper placeholder/error widgets
- **Flexible Configuration**: Supports custom placeholder, error widgets, and sizing

**Usage Example**:
```dart
// Basic usage
CachedImage(variantId, ImageSize.large)

// Advanced usage with custom configuration
CachedImage(
  variantId,
  ImageSize.small,
  width: 100,
  height: 100,
  fit: BoxFit.cover,
  placeholder: CustomLoadingWidget(),
  errorWidget: CustomErrorWidget(),
)
```

### 2. ImageCacheService

**File**: `lib/src/services/image_cache_service.dart`

**Key Features**:
- **URL Validation Cache**: Caches successful URLs to avoid retesting
- **Failed URL Tracking**: Temporarily caches failed URLs to avoid immediate retries
- **LRU Cache Management**: Implements Least Recently Used cache eviction
- **Configurable Retry Logic**: Waits before retrying failed URLs
- **Performance Monitoring**: Provides cache statistics for debugging

**Benefits**:
- Reduces network requests by caching working URLs
- Improves performance by avoiding repeated failed requests
- Provides intelligent retry mechanisms
- Offers debugging capabilities

### 3. Enhanced URL Generation

**File**: `lib/src/utils/helpers/methods.dart`

**Improvements**:
- **Fallback URL Generation**: Provides fallback S3 URLs if td_commons_flutter functions fail
- **Error Handling**: Wraps external function calls in try-catch blocks
- **Multiple URL Sources**: Supports both production and development environments

## Technical Implementation Details

### CORS Handling Strategy

Instead of trying to set CORS headers on the client side (which doesn't work), the solution:

1. **Uses Flutter's NetworkImage**: Leverages Flutter's built-in image loading capabilities
2. **Tests URL Accessibility**: Pre-validates URLs before attempting to display them
3. **Implements Smart Fallbacks**: Automatically tries alternative URLs when primary ones fail
4. **Caches Working URLs**: Remembers which URLs work to avoid retesting

### Performance Optimizations

1. **Intelligent Caching**: 
   - Caches successful URLs per variant ID
   - Implements LRU eviction for memory management
   - Tracks failed URLs to avoid immediate retries

2. **Efficient URL Testing**:
   - Uses shorter timeouts for URL validation (5 seconds)
   - Implements proper cleanup of image streams
   - Avoids blocking the UI thread

3. **Smart Retry Logic**:
   - Waits 5 minutes before retrying failed URLs
   - Automatically cleans up old failed URL entries
   - Provides configurable retry delays

### Error Handling

1. **Graceful Degradation**: Always shows placeholder or error widgets instead of breaking
2. **Comprehensive Logging**: Logs errors in debug mode for troubleshooting
3. **Multiple Fallback Levels**: Production URL → Development URL → Placeholder/Error widget

## Configuration Options

### CachedImage Widget Parameters

- `variantId`: The product variant ID for image lookup
- `size`: ImageSize.small or ImageSize.large
- `fit`: BoxFit for image scaling (default: BoxFit.cover)
- `width`/`height`: Optional dimensions
- `placeholder`: Custom loading widget
- `errorWidget`: Custom error widget

### ImageCacheService Configuration

- `_maxCacheSize`: Maximum number of cached URLs (default: 1000)
- `_retryDelayMinutes`: Minutes to wait before retrying failed URLs (default: 5)

## Debugging and Monitoring

### Cache Statistics

```dart
final imageService = ImageCacheService();
final stats = imageService.getCacheStats();
print('Cache stats: $stats');
```

### Clear Cache

```dart
final imageService = ImageCacheService();
imageService.clearCache();
```

## Migration Guide

### From Old CachedImage

The new `CachedImage` widget is backward compatible. Existing usage will continue to work:

```dart
// Old usage (still works)
CachedImage(variantId, ImageSize.large)

// New enhanced usage
CachedImage(
  variantId,
  ImageSize.large,
  width: 200,
  height: 200,
  placeholder: CircularProgressIndicator(),
)
```

### Performance Considerations

1. **First Load**: May be slightly slower due to URL testing
2. **Subsequent Loads**: Significantly faster due to caching
3. **Memory Usage**: Minimal additional memory for URL caching
4. **Network Usage**: Reduced due to intelligent caching and retry logic

## Best Practices

1. **Use Appropriate Image Sizes**: Choose ImageSize.small for thumbnails, ImageSize.large for detailed views
2. **Provide Custom Placeholders**: Enhance UX with branded loading indicators
3. **Monitor Cache Performance**: Use cache statistics in development to optimize performance
4. **Handle Edge Cases**: Always provide error widgets for graceful degradation

## Future Enhancements

1. **Persistent Caching**: Store successful URLs in local storage
2. **Image Preloading**: Preload images for better performance
3. **Advanced Retry Strategies**: Exponential backoff for failed URLs
4. **CDN Integration**: Support for multiple CDN endpoints
5. **Image Optimization**: Automatic format selection based on device capabilities
